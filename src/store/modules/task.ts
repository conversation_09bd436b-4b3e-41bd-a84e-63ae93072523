// themeStyle.js

import { defineStore } from 'pinia';
import { wddb_list } from '@/api/portal'

export const useTaskStore = defineStore('task', {
  state: () => ({
    taskList: {
      current: 1,
      records: [],
      size: 1,
      total: 0
    },
  }),
  getters: {
    getTask: (state) => state.taskList,
    getTaskCount: (state) => state.taskList.total,
  },
  actions: {
    setTaskList(value) {
      this.taskList = value;
    },
    async initNewTaskList(params) {
      await wddb_list(params).then(res => {
        this.setTaskList(res.data)
      });
    }
  }
});





