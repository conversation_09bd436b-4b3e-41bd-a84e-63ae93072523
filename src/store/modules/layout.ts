import { defineStore } from 'pinia'

export const useLayoutStore = defineStore('layout', {
  state: () => ({
    headerLoaded: false,
    headerInitialized: false,
  }),
  
  actions: {
    setHeaderLoaded(loaded: boolean) {
      this.headerLoaded = loaded
    },
    
    setHeaderInitialized(initialized: boolean) {
      this.headerInitialized = initialized
    },
    
    // 重置状态
    resetLayout() {
      this.headerLoaded = false
      this.headerInitialized = false
    }
  },
  
  getters: {
    canShowRouterView: (state) => state.headerLoaded && state.headerInitialized,
  }
})
