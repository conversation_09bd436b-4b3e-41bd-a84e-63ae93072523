<template>
  <div class="uk-flex work-top" id="workTop">
    <div class="uk-card uk-card-default work-card">
      <div class="uk-flex uk-card-header">
        <div
          :class="nowTitle.id == item.id ? 'active' : ''"
          v-for="(item, index) in titleList"
          :key="index"
          @click="loadYNXX(item)"
          ><span
            :class="
              nowTitle.id == item.id ? 'uk-card-title active title_' + item.id : 'uk-card-title'
            "
            :style="itemStyle.titleStyle"
            >{{ item.title }}</span
          ></div
        >
        <a @click="tabMore(0, $event)"><img src="@/assets/icons/portal/icon-more.png" /></a>
      </div>
      <div class="uk-card-body">
        <ul class="uk-list" v-if="YNXXList && YNXXList.length > 0">
          <template v-for="item in YNXXList" :key="item.ID">
            <li @click="OpenView(1, item)">
              <span :title="item.Title" class="bgtitle" :style="itemStyle.listStyle"
                ><i v-if="item.IsTop != null" class="top"></i>{{ item.Title }}</span
              >
              <span
                v-if="item && item.CreateTime"
                :style="itemStyle.listStyle"
                class="dateFormate"
                v-text="formatDate(item.CreateTime, itemStyle.dateFormate)"
              ></span>
            </li>
          </template>
        </ul>
        <el-empty v-else description="数据为空" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as portal from '@/api/portal'
import { OpenView, tabMore } from '@/layout/portal/admin'
const YNXXList = ref()
import emitter from '@/utils/mitt'
import { formatDate } from '@/utils/formatTime'

defineOptions({
  name: 'ItemList'
})

const props = defineProps<{ itemJson: itemType }>()

interface itemType {
  titleList: []
  itemStyle: {
    titleStyle: ''
    listStyle: ''
    dateFormate: ''
  }
  isShow: {
    showRanKing: boolean
    showMywork: boolean
  }
}

//监听属性的变化
emitter.on('itemList', (obj: itemType) => {
  init(obj)
})

//解除绑定事件
onUnmounted(() => {
  emitter.off('itemList')
})

const nowTitle = ref({ id: '', title: '' })
const titleList = ref([
  { id: '0', title: '院发文' },
  { id: '1', title: '院通知' },
  { id: '2', title: '院公告' }
])

const itemStyle = ref({
  titleStyle: {},
  listStyle: {},
  dateFormate: ''
})

const loadYNXX = async (item) => {
  nowTitle.value = item
  YNXXList.value = []
  const pageParm = {
    page: 1,
    pageSize: 7
  }
  let ret
  if (item.id == 0) {
    ret = await portal.yfw_list(pageParm)
  } else if (item.id == 1) {
    ret = await portal.ytz_list(pageParm)
  } else if (item.id == 2) {
    ret = await portal.ygg_list(pageParm)
  }
  YNXXList.value = ret.records
}

const init = (itemJson: itemType) => {
  if (itemJson) {
    titleList.value = itemJson.titleList
    itemStyle.value = itemJson.itemStyle
  }
}

onMounted(() => {
  init(props.itemJson)
  nowTitle.value = titleList.value[0]
  loadYNXX(nowTitle.value)
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/master.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/admin.css');

.work-top {
  width: 100%;
  padding: 0;
}
.dateFormate {
  min-width: 200px;
  white-space: nowrap;
  display: inline-block;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
