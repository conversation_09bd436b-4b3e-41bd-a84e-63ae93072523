<template>
  <div class="portal_page">
    <breadcrumb title="服务中心" :active-name="navbarActiveTitle"/>
    <div class="service-container">
      <div class="dashboard-menu">
        <AnalysisMenu @bunsinnes="bunsinnes"/>
      </div>
      <div class="main">
        <div
v-if="navbarIndexActive==0" v-loading="isLoading"
             class="main-container  ">
          <div style="width: 100%;">
            <div class="uk-card-small uk-card-body">
              <ul class="custom-breadcrumb" style="padding-left: 0">
                <template v-for="(item, index) in enterpriseNature" :key="index">
                  <li>
                    <span
                      style="color: #e5e5e5; padding-right: 10px"
                      v-show="index > 0">|</span>
                    <a :href="'#code' + item.ID">{{ item.Name }}</a>
                  </li>
                </template>
              </ul>
              <ul class="custom-breadcrumb" style="padding-left: 0">
                <template v-for="(item, index) in businessNature" :key="index">
                  <li>
                    <span
                      style="color: #e5e5e5; padding-right: 10px"
                      v-show="index > 0">|</span>
                    <a :href="'#code' + item.ID">{{ item.Name }}</a>
                  </li>
                </template>
              </ul>
              <ul class="custom-breadcrumb" style="padding-left: 0">
                <template v-for="(item, index) in serverNature">
                  <li :key="index" v-if="index == 0 || index == 2">
                    <span
                      style="color: #e5e5e5; padding-right: 10px"
                      v-show="index > 0">|</span>
                    <a :href="'#code' + item.ID">{{ item.Name }}</a>
                  </li>
                </template>
                <template v-for="(item, index) in serverNature">
                  <li :key="index" v-if="index == 1">
                    <span style="color: #e5e5e5; padding-right: 10px">|</span>
                    <a :href="'#code' + item.ID">{{ item.Name }}</a>
                  </li>
                </template>
              </ul>
            </div>
            <div class="main-content uk-card-small uk-card-body">
              <template v-for="(item, index) in functionList" :key="index">
                <div class="uk-card uk-card-small">
                  <my-panel
                    :id="['code' + item.ID]"
                    :title="item.Name"
                    :height="'auto'"
                    :show-more="false"
                    :is-logo="true"
                    :logo-img="instance?.proxy.$getFullUrl(item.Icon)">
                    <div
class="custom-grid uk-grid"
                         style="padding: 0;margin: 0;gap:10px;">
                      <div
                        v-for="(secondItem, secondIndex) in item.SubMenu"
                        :key="secondIndex"
                        class="custom-grid-item">
                        <div class="custom-grid-child">
                          <div class="custom-grid-content">
                            <div class="custom-two-level">
                              <img
                                class="custom-two-level-img"
                                :src="instance?.proxy.$getFullUrl(secondItem.Img)"/>
                              <div class="custom-grid-content">
                                <img
class="custom-small-img"
                                     :src="instance?.proxy.$getFullUrl(secondItem.Icon)"/>
                                <span
                                  class="custom-two-level-title">&nbsp;{{
                                    secondItem.Name
                                  }}</span>
                              </div>
                              <hr class="custom-divider"/>
                            </div>
                            <ul class="right-entry-list">
                              <!-- 显示列表 -->
                              <li
                                v-for="(thirdItem, thirdIndex) in secondItem.ShowList"
                                :key="thirdIndex"
                                class="custom-show-list">
                                <div
                                  class="jump-link"
                                  @click="openSystemFunction(
                                    thirdItem.Name,
                                    thirdItem.Url,
                                    thirdItem.ServiceDirectoryID
                                  )"
                                  :title="thirdItem.Name">
                                  <span class="uk-text-truncate">{{ thirdItem.Name }}</span>
                                  <div class="service-directory-modal">
                                  <span
                                    uk-toggle="target: #modal-sections"
                                    class="jump-link-modal"
                                    v-show="thirdItem.IsEnable !== '0'">服务指南</span>
                                  </div>
                                  <span style="width: 10px; top: -16px">
                                  <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <polyline
                                      fill="none"
                                      stroke="#000"
                                      stroke-width="1.03"
                                      points="7 4 13 10 7 16"/>
                                  </svg>
                                </span>
                                </div>
                              </li>
                              <!-- 展开列表 -->
                              <li
                                v-for="(thirdItem, thirdIndex) in secondItem.SwitchList"
                                :key="thirdIndex"
                                v-show-hide="secondItem.IsExpandCollapse">
                                <a
                                  class="jump-link"
                                  @click="openSystemFunction(
                                    thirdItem.Name,
                                    thirdItem.Url,
                                    thirdItem.ServiceDirectoryID
                                  )"
                                  :title="thirdItem.Name">
                                  <span class="uk-text-truncate">{{ thirdItem.Name }}</span>
                                  <div class="service-directory-modal">
                                  <span
                                    uk-toggle="target: #modal-sections"
                                    class="jump-link-modal"
                                    v-show="thirdItem.IsEnable !== '0'">服务指南</span>
                                  </div>
                                  <span uk-icon="chevron-right" style="width: 10px; top: -16px">
                                  <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <polyline
                                      fill="none"
                                      stroke="#000"
                                      stroke-width="1.03"
                                      points="7 4 13 10 7 16"
                                    />
                                  </svg>
                                </span>
                                </a>
                              </li>
                            </ul>
                            <div
                              class="view-more"
                              v-show="secondItem.IsMoreThanFour"
                              @click="toggleExpand(secondItem)">
                              <div>
                              <span
                                v-if="!secondItem.IsExpandCollapse"
                                uk-icon="triangle-down" class="uk-icon">
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  xmlns="http://www.w3.org/2000/svg">
                                  <polygon points="5 7 15 7 10 12"/>
                                  </svg>
                              </span>
                                <span v-else uk-icon="triangle-up" class="uk-icon">
                                <svg
width="20"
                                     height="20"
                                     viewBox="0 0 20 20"
                                     xmlns="http://www.w3.org/2000/svg">
                                  <polygon points="5 13 10 8 15 13"/>
                                </svg>
                              </span>
                                <span>{{ secondItem.IsExpandCollapse ? '收缩' : '展开' }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </my-panel>
                </div>
              </template>
            </div>
          </div>
        </div>
        <div v-if="navbarIndexActive==1" v-loading="isLoading" class="main-container">
          <div style="flex:1">
            <template v-for="(items,index) in functionList" :key="index">
              <myPanel :title="items.Name" :height="'auto'" :show-more="false">
                <div class="list" style="width: 100%;padding: 10px;">
                  <div class="row-list" style="justify-content: flex-start;gap: 8px;">
                    <template v-for="(item, index1) in tempList">
                      <div
                        class="c4btn btn"
                        style="width: 232px;height:76px;"
                        @click="openFunctionChildrenList(item)"
                        v-if="items.Code==item.XTFL"
                        :key="index1">
                        <img src="@/assets/imgs/home/<USER>" class="icon"/>
                        <div class="text">
                          <div class="tit">{{ item.ShowName }}</div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </myPanel>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, getCurrentInstance} from 'vue'
import * as PortalApi from "@/api/system/portal";
import {useUserStore} from "@/store/modules/user";
import {getAccessToken} from "@/utils/auth";
import {funcLog,getOutSystem} from "@/api/system/pageAndFuncLog";
import {replaceUrl} from "@/assets/js/NK";
import {searchStore} from "@/store/modules/search";
import MyPanel from '@/components/Panel/src/index.vue'
import breadcrumb from '@/layoutNew/components/breadcrumb.vue'
import AnalysisMenu from './components/AnalysisMenu.vue'
import { url } from 'inspector';

const indexActive = ref(1)
const indexActiveTitle = ref("")
const navbarIndexActive = ref(0)
const navbarActiveTitle = ref('业务功能')
const functionList = ref<Array<any>>([]);
const enterpriseNature = ref<Array<any>>([]);
const serverNature = ref<Array<any>>([]);
const businessNature = ref<Array<any>>([]);
const functionChildrenList = ref<Array<any>>([]);
const tempList = ref<Array<any>>([]);
const userStore = useUserStore()
const instance = getCurrentInstance() // 获取当前组件实例
const isLoading = ref(false)
const searchStoreInstance = searchStore()
// 常量定义
const navItems = [
  {label: '业务功能', value: 0},
  {label: '业务系统', value: 1}
]
// 监听搜索值变化
watch(
  () => searchStoreInstance.searchValue,
  (newValue) => {
    getSystemFunctionList(newValue)
  }
)
onMounted(() => {
  getSystemFunctionList()
})

const toggleExpand = (item) => {
  item.IsExpandCollapse = !item.IsExpandCollapse
}
const bunsinnes = async (item) => {
  navbarIndexActive.value = item.num
  if (item.num === 1) {
    navbarActiveTitle.value = '业务系统'
    await loadSystemList()
  } else {
    navbarActiveTitle.value = '业务功能'
    await getSystemFunctionList()


  }
}
const loadSystemList = async () => {
  isLoading.value = true
  try {
    tempList.value = []
    const sqlData = await PortalApi.execSystemScript({
      code: 'SQL_adde0127f04a415780e9df7d0316867d',
      ExecData: JSON.stringify([{UserID: userStore.getUser.id}])
    })

    tempList.value = sqlData.map(item => ({
      Url: item.Url,
      DoorCode: item.DoorCode,
      Token: getAccessToken(),
      ShowName: item.ShowName,
      XTFL: item.XTFL,
      IsOutSystem: item.IsOutSystem
    }))

    const result = await PortalApi.serviceCenterGetLeftListCat()
    functionList.value = result
  } finally {
    isLoading.value = false
  }
}
const getSystemFunctionList = async (searchValue = '') => {
  isLoading.value = true
  enterpriseNature.value = []
  serverNature.value = []
  businessNature.value = []
  try {
    const result = await PortalApi.serviceCenterloadData({Search: searchValue})
    result.forEach(firstLevel => {
      if (
        firstLevel.Name == '党建工团' ||
        firstLevel.Name == '行政办公' ||
        firstLevel.Name == '三会决策' ||
        firstLevel.Name == '事项督办' ||
        firstLevel.Name == '纪监审法务'
      ) {
        enterpriseNature.value.push(firstLevel)
      } else if (
        firstLevel.Name == '外事管理' ||
        firstLevel.Name == '档案管理' ||
        firstLevel.Name == '用车管理'
      ) {
        serverNature.value.push(firstLevel)
      } else {
        businessNature.value.push(firstLevel)
      }
      if (firstLevel.SubMenu?.length) {
        firstLevel.SubMenu.forEach(secondLevel => {
          secondLevel.IsExpandCollapse = false
          if (secondLevel.SubMenu?.length) {
            secondLevel.IsMoreThanFour = secondLevel.SubMenu.length > 4
            secondLevel.ShowList = secondLevel.SubMenu.slice(0, 4)
            secondLevel.SwitchList = secondLevel.SubMenu.slice(4)
          }
          secondLevel.SubMenu = []
        })
      }
    })
    functionList.value = result
  } finally {
    isLoading.value = false
  }
}
const openSystemFunction = async (name: string, url: string, menuId: string) => {
  if (!url) {
    ElMessage.error('暂未关联相关业务功能')
    return
  }

  // 记录日志
  await funcLog({
    workno: userStore.user.workNo,
    funcname: name,
    funcurl: url,
    menuId: menuId
  })

  // 处理URL
  let finalUrl = url.startsWith('http') ? url : instance?.proxy.$getFullUrl(url)

  if (finalUrl.includes('{token}')) {
    finalUrl = finalUrl.replace('{token}', getAccessToken())
  } else {
    finalUrl += (finalUrl.includes('?') ? '&' : '?') + `token=${getAccessToken()}`
  }

  // 替换工作号
  finalUrl = finalUrl.replace('{WorkNo}', userStore.user.workNo)
  window.open(finalUrl)
}

const openFunctionChildrenList = async (item: any) => {
  // 记录日志
  await funcLog({
    funcname: item.ShowName,
    funcurl: item.Url,
    menuId: '子系统入口'
  })

  // 构建URL
  let url = `${item.Url}${item.Url.includes('?') ? `&code=${item.DoorCode}` : `?code=${item.DoorCode}`}`
  if(item.IsOutSystem && item.IsOutSystem == "1"){
    let outSystem = await getOutSystem({url:url})
    url = outSystem.url;
  }else{
    if (!url.includes('token')) {
      url += `&token=${item.Token}`
    }
    url = replaceUrl({url})
  }
  
  if (!url.startsWith('http')) {
    url = instance?.proxy.$getFullUrl(url)
  }

  window.open(url)
}
</script>

<style lang="scss" scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/master.css');
@import url('@/assets/css/service-center.css');
@import url('@/assets/css/default.css');

.c4btn {
  .text {
    font-size: 14px !important;
  }
}

.dashboard-menu {
  flex: 0 0 220px;
  background: #fff;
}

.panel {
  border-bottom: 0 !important;
}

.portal_page {
  min-height: 600px;
  width: 1252px;
  margin: 0 auto 10px auto;
}

.service-container {
  background: #f5f8fa;
  margin-top: 8px;
  display: flex;
  gap: 20px;
}

.header-navbar {
  width: 260px;
  box-shadow: var(--el-box-shadow-light);
  height: 660px;

  .row-item {
    display: flex;
    align-items: center;
    height: 52px;
    cursor: pointer;
    padding: 0 20px;
    background: linear-gradient(to right, #6f93b2, #c2d8e5);
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    color: #fff;

    span {
      font-size: 16px;
      font-weight: 700;
      letter-spacing: 2px;
    }
  }

  .row-item:hover {
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    color: #fff;
  }
}

.navbar-active {
  position: relative;
  background: linear-gradient(to right, #296aa2, #6cbcea) !important;
  color: #fff;
}

.navbar-active:after {
  content: ''; /* 伪元素必需属性 */
  display: block; /* 块级元素 */
  position: absolute; /* 绝对定位 */
  bottom: 12px; /* 定位到父元素底部下方 */
  right: -46px; /* 水平居中 */
  transform: translateX(-50%); /* 微调水平居中 */
  width: 0; /* 宽度为0 */
  height: 0; /* 高度为0 */
  border: solid transparent; /* 透明边框（缩写） */
  border-width: 12px 16px; /* 上/下 10px，左右 6px */
  border-left-color: #6cbcea; /* 上边框颜色形成三角形 */
}

.main {
  flex: 1;
  background: #fff;
}

.main-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  gap: 10px;
  min-height: 400px;

  .sidebar-container-row {
    height: 52px;
    background-color: #a6caee;
    border-bottom: 1px solid #fff;
    display: flex;
    align-items: center;
    cursor: pointer;

    span {
      font-family: "Arial", sans-serif;
      font-size: 16px;
      padding: 0 20px;
    }
  }

  .active {
    background-color: #004ea2 !important;
    color: #fff;
  }
}

.sidebar-container {
  width: 260px;
  height: 100%;
}

.main-content {
  flex: 1;

  .main-content-title {
    font-size: 16px;
    font-weight: 700;
    position: relative;
    padding: 20px;
  }

  .main-content-title::before {
    content: "";
    display: block;
    width: 4px;
    height: 21px;
    position: absolute;
    background: #004ea2;
    left: 8px;
  }

  .main-content-container {
    padding: 20px;

    .row {
      height: auto;

      .row-title {
        padding: 10px;

        .row-title-text {
          font-size: 16px;
          font-weight: 700;
          letter-spacing: 2px;
          display: flex;
          align-items: center;
          gap: 10px;

          .custom-small-img {
            width: 20px;
            height: 20px;
          }
        }
      }

      .row-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;

        .row-item {
          cursor: pointer;
          width: 180px;
          padding: 20px;
          background: #f6f9fe;

          span {
            font-size: 16px;
            letter-spacing: 2px;
          }
        }
      }
    }
  }
}

.custom-grid .custom-grid-child > .custom-grid-content {
  margin: 10px 0 0 0;
  border: 1px solid #e5e5e5;
  min-height: 384px;
}

.custom-two-level {
  padding: 20px 20px 0 20px;
}

.custom-two-level .custom-two-level-img {
  width: 100%;
  min-height: 60px;
  max-height: 80px;
  margin-bottom: 15px;
  border-radius: 2px;
}

.custom-two-level hr {
  margin: 10px 0 !important;
}

.custom-grid-content .custom-small-img {
  width: 20px;
  height: 20px;
  margin-top: -5px;
}

.custom-grid-content ul {
  padding: 0 20px;
}

.view-more {
  width: 100%;
  height: 45px;
  border-top: 1px solid #e5e5e5;
  text-align: center;
  color: #999;
  font-size: 16px;
  padding: 10px 0 0 0;
  cursor: pointer;
  color: #333;
}

.view-more .view-all {
  cursor: context-menu;
  color: rgb(102, 102, 102);
}

.custom-two-level-title {
  font-size: 16px;
  color: #333;
}

.right-entry-list {
  list-style: none;
}

.right-entry-list li {
  width: 252px;
  height: 40px;
  vertical-align: middle;
  line-height: 40px;
  clear: both;
  cursor: pointer;

  a {
    text-decoration: none;
  }

  a:hover {
    text-decoration: none;
  }
}


.right-entry-list li .jump-link {
  color: #666;
  font-size: 14px;
  width: 100%;
  text-decoration: none;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding-left: 10px;
}

.right-entry-list li .jump-link:hover {
  color: rgb(60, 145, 214);
  font-weight: bold;

  .chevron-right {
    color: rgb(60, 145, 214);
  }
}

.right-entry-list li .jump-link:before {
  content: '';
  position: absolute;
  left: -10px;
  top: 10px;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  text-decoration: none;
}

.right-entry-list li .jump-link-modal {
  display: inline;
  color: #32b66b;
  font-size: 12px;
  border: 1px solid #32b66b;
  padding: 0 4px;
  border-radius: 3px;
  width: 58px;
}

.right-entry-list li .jump-link-modal:hover {
  display: inline;
  color: #32b66b;
  text-decoration: none;
  font-size: 12px;
  border: 1px solid #32b66b;
  padding: 0 4px;
  border-radius: 3px;
  width: 58px;
}

.custom-grid .custom-grid-item {
  min-width: 310px;
  padding: 0;
}

.right-entry-list .service-directory-modal {
  display: inline-block;
  width: 58px;
  /* position: relative; */
  /* top: -10px; */
}

.right-entry-list span {
  display: inline-block;
  /* position: relative; */
  right: 0;
  /* top: -6px; */
}

/**scroll*/
.right-entry-list::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 2px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.right-entry-list::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: skyblue;
  background-image: -webkit-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.2) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      transparent 75%,
      transparent
  );
}

.right-entry-list::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}
</style>
