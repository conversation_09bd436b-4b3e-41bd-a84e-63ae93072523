<script setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'

const router = useRouter()
const activeMenuItem = ref('dashboard')
// 菜单项数据
const menuItems = [
  {
    id: 'dashboard',
    name: '业务功能',
    icon: 'dashboard',
    num: 0
  },
  {
    id: 'correlation',
    name: '业务系统',
    icon: 'correlation',
    num: 1
  }
]
const emit = defineEmits(['bunsinnes'])
// 导航到选中的菜单项
const navigateTo = (item) => {
  activeMenuItem.value = item.id
  emit('bunsinnes', item)
}
</script>

<template>
  <div class="analysis-menu">
    <div class="menu-header" style="display: none;">
      <h2>服务类型</h2>
    </div>

    <div class="menu-items">
      <div
        v-for="item in menuItems"
        :key="item.id"
        class="menu-item"
        :class="{ 'active': activeMenuItem === item.id }"
        @click="navigateTo(item)">
        <div class="menu-icon" :class="`icon-${item.icon}`"></div>
        <div class="menu-name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.analysis-menu {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(30, 94, 186, 0.1);
  width: 220px;

  .menu-header {
    padding: 20px 15px;
    border-bottom: 1px solid #eaeaea;

    h2 {
      margin: 0;
      color: #1E5EBA;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .menu-items {
    .menu-item {
      display: flex;
      align-items: center;
      padding: 12px 15px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(30, 94, 186, 0.05);
      }

      &.active {
        background-color: rgba(30, 94, 186, 0.1);
        border-bottom: 3px solid #409eff;

        .menu-icon, .menu-name {
          color: #409eff;
        }
      }

      .menu-icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #606266;

        &.icon-dashboard::before {
          content: "📊";
        }

        &.icon-data::before {
          content: "📈";
        }

        &.icon-prediction::before {
          content: "🔮";
        }

        &.icon-correlation::before {
          content: "🔗";
        }

        &.icon-trend::before {
          content: "📉";
        }

        &.icon-anomaly::before {
          content: "⚠️";
        }

        &.icon-mining::before {
          content: "⛏️";
        }

        &.icon-report::before {
          content: "📑";
        }
      }

      .menu-name {
        font-size: 16px;
        color: #303133;
      }
    }
  }
}
</style>
