<template>
  <div class="header">
    <div class="logo"><img src="@/assets/icons/portal/logo.png"/></div>

    <div class="master-ctrl-hdattach"></div>

    <div class="system_top_menu">
      <ul>
        <li :class="topath == m.pathName ? 'gcs_cur' : ''" v-for="(m, idx) in menu" :key="idx">
          <span @click="openNewWindow(m)">
            <a
              v-if="m.icon != null"
              v-show="topath == m.pathName"
              href="javascript:;"
              :style="`background:url('data:image/png;base64,${m.icon}')  no-repeat center center;background-size: 100% auto;background-position: 0px 23px;`"
            ></a>
            <a
              v-else
              v-show="topath == m.pathName"
              href="javascript:;"
              :style="`background:url(${get_url(m.iconUrl)})  no-repeat center center;background-size: 100% auto;background-position: 0px 23px;`"
            ></a>
            <a v-show="topath != m.pathName" href="javascript:;" v-text="m.name"></a>
          </span>
        </li>
      </ul>
    </div>

    <div class="sys_tp_search" v-show="searchBar">
      <input
        type="text"
        v-model="searchValue"
        :placeholder="searchPlaceholder"
        @keyup.enter="handleEnter"
      />
      <span
        class="clear"
        v-show="searchValue"
        @click="
          () => {
            searchValue = ''
            handleEnter()
          }
        "
      >+</span
      >
      <span class="hbm-searchpanel" @click="handleEnter"></span>
    </div>

    <div class="header-control">
      <ul>
        <li class="ctrl_tasks" code="tasks" @click="to_newTask">
          <span class="hbm_newtasks" title="待办"></span>
          <span>待办</span>
          <span
            v-if="unreadMsgCount.NewTaskCount > 0"
            class="gcs_taskcount"
            :title="'待办' + unreadMsgCount.NewTaskCount"
          >{{ unreadMsgCount.NewTaskCount }}</span
          >
        </li>

        <li class="ctrl_message" code="message" @click="openMsgPage()">
          <span class="hbm_msg" title="消息"></span>
          <span>消息</span>
          <span
            v-if="unreadMsgCount.NewMsgCount > 0"
            class="gcs_taskcount"
            :title="'消息:' + unreadMsgCount.NewMsgCount"
          >{{ unreadMsgCount.NewMsgCount }}</span
          >
        </li>
      </ul>
    </div>

    <div class="header-curinfo" @click="showClick">
      <img class="user_info_head" :src="headSrc" alt=""/>

      <span class="ctrl_curinfo" code="">
        <el-dropdown ref="dropdown" trigger="contextmenu">
          <span class="el-dropdown-link" style="border: none; outline: none">
            {{ userStore.getUser.nickname }}
            <!-- <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon> -->
            <span class="ctrl_arrow"></span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                @click="toUserInfo"
                style="height: 30px; width: 100px"
                :icon="UserFilled"
              >个人中心</el-dropdown-item
              >
              <!-- <el-dropdown-item
                @click="formDesigner"
                style="height: 30px; width: 100px"
                :icon="Setting"
                >个性化组件</el-dropdown-item
              > -->
              <!-- <el-dropdown-item @click="layOut" style="height: 30px; width: 100px" :icon="Setting"
                >个性化布局</el-dropdown-item
              >
              <el-dropdown-item
                @click="outLayout"
                style="height: 30px; width: 100px"
                :icon="Setting"
                >退出布局</el-dropdown-item
              > -->
              <el-dropdown-item
                @click="loginOut"
                style="height: 30px; width: 100px"
                :icon="SwitchButton"
              >退出登录</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useUserStore} from '@/store/modules/user'
import {DropdownInstance, ElMessageBox} from 'element-plus'
import * as PortalApi from '@/api/system/portal'
import {searchStore} from '@/store/modules/search'
import {getAccessToken} from '@/utils/auth'
import {SwitchButton, UserFilled, Setting} from '@element-plus/icons-vue'
import {useAppStore} from '@/store/modules/app'
import {CATEGORY, useLayoutCache} from '@/hooks/web/categoryCache'
import {watch} from 'vue'

const gSearchStore = searchStore()
const route = useRoute()
const {push} = useRouter()
const {wsCache} = useLayoutCache()
const docInfo = useAppStore().docInfo
const headSrc = ref('')
const workName = ref(gSearchStore.getToolbarValue)
const searchPlaceholder = ref('')
//跳转到我的代办
const to_newTask = () => {
  push({path: '/Portal/WorkCenter/MyWork', query: {code: 'newtask'}}) //跳转到我的代办
}

//跳转到布局页面
const gridLayOut = () => {
  push({path: '/Portal/gridLayout'})
}

const beforeRouteLeave = (to, from, next) => {
  next()
}

const toUserInfo = () => {
  push('/Portal/UserCenter')
}

//header中添加搜到框
const searchBar = ref(false)
const searchValue = ref('')
const props = defineProps({
  isshow: {
    type: Boolean,
    required: false
  }
})

if (props.isshow) {
  searchBar.value = false
}

// const params = route.params; // 路由参数对象
// // 直接访问URL参数
// const query = route.query; // 获取查询字符串

/*门户头部菜单 */
let menu = ref<any[]>([])

//跳转到个性化组件
// const formDesigner = () => {
//   window.open('/alone/custom', '_blank')
// }
//跳转到个性化布局
const layOut = () => {
  wsCache.set(CATEGORY.IsLayout, true)
  window.location.href = '/Portal/newsLayout'
}
const outLayout = () => {
  wsCache.set(CATEGORY.IsLayout, false)
  window.location.href = '/Portal/workCenter'
}

const getUrl = (url) => {
  if (url.indexOf('http') > -1) {
    return '/Portal/workCenter/workIndex'
  } else {
    return url
  }
}

const {t} = useI18n()
const {replace} = useRouter()
const unreadMsgCount = ref({})

const loginOut = async () => {
  try {
    await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    })
    await userStore.loginOut()
    // replace('/login?redirect=/Portal/WorkCenter')
    replace('/portal/home/<USER>')
  } catch {
  }
}

const userStore = useUserStore()
const initSearch = () => {
  searchBar.value = true
  searchValue.value = ''
}

// 有newUrl的跳转使用打开新窗口的方式进行
const topath = ref<any>()
const openNewWindow = (m) => {
  let url2 = m.url
  let pathName = m.pathName
  let url = m.newUrl
  let openType = m.openType
  topath.value = pathName
  workName.value = gSearchStore.getToolbarValue
  if (
    '/Portal/Home/ProjectCenter' == url2 ||
    '/Portal/ServiceCenter/Index' == url2 ||
    '/Portal/ServiceCenter' == url2 ||
    '/Portal/publicInfoList' == url2 ||
    '/Portal/workCenter/myWork' == url2
  ) {
    initSearch()
  } else {
    searchBar.value = false
  }
  //如果url2以http开头
  if (url2 != null) {
    if (url2.startsWith('http')) {
      if (url2.indexOf('?') != -1) {
        url2 = url2 + '&token=' + getAccessToken()
      } else {
        url2 = url2 + '?token=' + getAccessToken()
      }
    }
    if (url2.indexOf('{token}') != -1) {
      url2 = url2.replace('{token}', getAccessToken())
    }
    if (openType == '_blank') {
      window.open(url2, openType)
    } else {
      docInfo.title = m.name
      docInfo.routeName = m.pathName
      if (wsCache.get(CATEGORY.IsLayout)) {
        push('/Portal/newsLayout')
        if (url2.indexOf('WorkCenter') != -1) {
          useAppStore().category = 'workCenter'
        } else if (url2.indexOf('ServiceCenter') != -1) {
          useAppStore().category = 'serviceCenter'
        } else if (url2.indexOf('Home') != -1) {
          useAppStore().category = 'consultCenter'
        }
      } else {
        push(m.url)
      }
    }
    return
  }
  if (url != null) {
    push({path: url})
  }
}

const openMsgPage = () => {
  let url =
    import.meta.env.VITE_TOURL_PREFIX +
    '/BasicApplication/InfoManage/Msg/UnreadMsgList?token=' +
    getAccessToken()
  // window.open('/Portal/MsgList')
  window.open(url)
}

const emit = defineEmits(['search'])

const handleEnter = () => {
  workName.value = gSearchStore.getToolbarValue
  if (workName.value == 'newtask') {
    gSearchStore.setNewTaskSearchValue(searchValue.value)
  } else {
    gSearchStore.setSearchValue(searchValue.value)
  }
}

function get_url(imagePath) {
  return new URL(imagePath, import.meta.url).href
}

const currentPath = computed(() => route.path)
//绑定时
onMounted(async () => {
  workName.value = gSearchStore.getToolbarValue
  if (
    '/Portal/ProjectCenter' == currentPath.value ||
    '/Portal/ServiceCenter/Index' == currentPath.value ||
    '/Portal/ServiceCenter' == currentPath.value ||
    '/Portal/publicInfoList' == currentPath.value ||
    '/Portal/WorkCenter/myWork' == currentPath.value
  ) {
    initSearch()
  } else {
    if (workName.value != 'newtask') {
      searchBar.value = false
    }
  }
  menu.value = await PortalApi.GetSysMenu1l()
  if (route.name == undefined || route.name == 'PortalWorkIndex' || useAppStore().isLayout) {
    topath.value = menu.value[0].pathName
  } else {
    topath.value = route.name
  }
  unreadMsgCount.value = await PortalApi.getMsgCount()
  getImage()
})
watch(
  () => userStore.user.avatar,
  () => {
    getImage()
  }
)
watch(
  () => gSearchStore.toolbarValue,
  (newValue) => {
    if (newValue == 'newtask') {
      initSearch()
      searchPlaceholder.value = '搜索我的待办'
    } else {
      if (
        !(
          '/Portal/ProjectCenter' == currentPath.value ||
          '/Portal/ServiceCenter/Index' == currentPath.value ||
          '/Portal/ServiceCenter' == currentPath.value ||
          '/Portal/publicInfoList' == currentPath.value ||
          ('/Portal/WorkCenter/myWork' == currentPath.value && newValue == 'newtask')
        )
      ) {
        searchBar.value = false
      } else {
        searchPlaceholder.value = '请输入菜单名称、关键词'
      }
    }
  }
)
const imageData = ref<string>('')
const getImage = () => {
  if (isBinaryData(userStore.getUser.avatar)) {
    let type = userStore.getUser.avatar.replace(/^0x/, '')
    let bytes = []
    for (let i = 0; i < type.length; i += 2) {
      bytes.push(parseInt(type.substr(i, 2), 16))
    }
    imageData.value = btoa(String.fromCharCode.apply(null, bytes))
    headSrc.value = 'data:image/jpg;base64,' + imageData
  } else {
    headSrc.value =
      import.meta.env.VITE_TOURL_PREFIX +
      '/BasicApplication/DownloadFile?FileID=' +
      userStore.getUser.avatar
  }
}

//判断是否是二进制文件
function isBinaryData(data) {
  // 检查是否是ArrayBuffer或Blob类型
  if (data instanceof ArrayBuffer || data instanceof Blob) {
    return true
  }

  // 检查是否是base64编码的图片
  if (typeof data === 'string' && data.startsWith('data:image/')) {
    return true
  }

  // 其他情况视为图片名称
  return false
}

const dropdown = ref<DropdownInstance>()
const showClick = () => {
  if (!dropdown.value) return
  dropdown.value.handleOpen()
}
</script>

<style lang="scss" scoped>
::v-deep.no-border .el-input__inner .box-shadow {
  border: none;
  box-shadow: none;
}

::v-deep.no-border .el-input__inner {
  border: none;
  box-shadow: none;
}

::v-deep.no-border .el-input__inner:focus,
::v-deep.no-border .el-input__inner:hover {
  border: none;
  box-shadow: none;
}

* {
  font-family: 'Microsoft YaHei', 'MicrosoftYaHei';
  text-decoration-style: soild;
  text-decoration-color: rgb(102, 102, 102);
  text-decoration-thickness: auto;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.header {
  height: 60px;
  width: 100%;
  position: fixed;
  top: 0;
  border-bottom: 1px solid #f0f0f0;
  overflow: hidden;
  background: #fff url('@/assets/icons/portal/top_bg.jpg') no-repeat center center;
  background-size: cover;
  box-shadow: 0 0px 2px rgba(0, 0, 0, 0.1);
  z-index: 99;

  display: flex;
  align-items: center;
  justify-content: space-between;

  .logo {
    // height: 39px;

    // float: left;
    // left: 0px;
    // position: relative;

    height: 30px;
    margin-left: 32px;

    img {
      height: 100%;
      width: auto;
      /* background-color: #FD482C; */
      // height: 30px;
      // margin-top: 16px;
      // margin-left: 32px;
    }
  }

  .system_top_menu {
    height: 60px;
    line-height: 63px;
    margin-right: auto;
    margin-left: 74px;

    ul {
      margin: 0;
      padding: 0;
      height: 60px;
      list-style: none;

      display: flex;
      align-items: center;

      li {
        margin: 0 15px;
        float: left;
        font-size: 16px;
        border-bottom: 5px solid transparent;
        height: 60px;
        position: relative;

        a {
          color: #000;
          font-weight: bold;
          font-size: 16px;
          text-decoration: none;
        }

        .m_link {
          white-space: nowrap;
        }

        &.gcs_cur {
          width: 80px;
          height: 60px;
          text-indent: -999px;
          cursor: pointer;
          border-bottom: 5px solid #0070ff;

          &::after {
            content: '';
            border: 5px solid;
            border-color: transparent transparent #0070ff transparent;
            position: absolute;
            left: 45%;
            bottom: 0px;
          }

          a {
            display: block;
            width: 80px;
            height: 60px;
            text-indent: -99999px;
          }
        }
      }
    }
  }

  .sys_tp_search {
    margin-left: auto;
    margin-right: 24px;
    padding: 0 5px;
    box-sizing: border-box;

    width: 210px;
    height: 36px;
    line-height: 36px;

    border-radius: 5px;
    background-color: #fff;

    display: flex;
    align-items: center;
    justify-content: center;

    input {
      width: 147px;
      height: 30px;
      line-height: 30px;
      vertical-align: middle;
      border: 0px;
      outline: none;
      background-color: transparent;
      text-align: left;
      margin-right: 10px;
      font-size: 14px;
    }

    .clear {
      width: 20px;
      font-size: 24px;
      transform: rotate(45deg);
      color: #999;
      cursor: pointer;
    }

    .hbm-searchpanel {
      background: url('@/assets/icons/portal/search_icon.png') no-repeat center center;
      background-size: cover;
      width: 18px;
      height: 18px;
      display: block;
      cursor: pointer;
    }
  }

  .header-control {
    // max-width: 470px;
    height: 100%;
    // float: right;
    // margin-top: 8px;
    font-size: 14px;
    text-align: right;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      height: 100%;

      display: flex;
      align-items: center;
      justify-content: center;

      li {
        float: left;
        margin-right: 20px;
        border: 1px solid transparent;
        cursor: pointer;

        span {
          display: block;
          font-size: 12px;
          color: rgb(102, 102, 102);

          &:nth-child(1) {
            margin: 0 auto;
            width: 27px;
            height: 27px;
          }

          &:nth-child(2) {
            height: 16px;
            line-height: 16px;
          }

          &.gcs_taskcount {
            position: absolute;
            min-width: 16px;
            height: 16px;
            top: -4px;
            padding: 1px;
            right: -6px;
            border-radius: 5px;
            background-color: #fa7582;
            color: #fff;
            margin-left: 0px;
            line-height: 14px;
            font-size: 12px;
            text-align: center;
            overflow: hidden;
          }

          &.hbm_newtasks {
            background: url('@/assets/icons/portal/daiban_icon.png') no-repeat center center;
            background-size: cover;
            margin-bottom: -2px;
          }

          &.hbm_msg {
            background: url('@/assets/icons/portal/message_icon.png') no-repeat center center;
            background-size: cover;
            margin-bottom: -2px;
          }
        }

        &.ctrl_tasks {
          position: relative !important;
        }

        &.ctrl_message {
          position: relative !important;
        }
      }
    }
  }

  .header-curinfo {
    margin-right: 40px;
    cursor: pointer;

    .user_info_head {
      width: 40px;
      height: 40px;
      float: left;
      border-radius: 50%;
      margin-bottom: 0;
      background-size: cover;
      border: 1px solid #eee;
      object-fit: cover;
    }

    span {
      display: inline-block;
      vertical-align: middle;
      line-height: 39px;
      font-size: inherit;

      &.ctrl_arrow {
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 8px solid #fff;
        margin-left: 5px;
      }

      &:nth-child(2) {
        margin-left: 16px;
      }

      a {
        color: rgb(102, 102, 102);
        text-decoration: none;
      }
    }
  }
}
</style>
