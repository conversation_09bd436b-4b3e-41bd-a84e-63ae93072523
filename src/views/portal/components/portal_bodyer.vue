<template>
  <div class="bodyer">
    <div class="siderbar">
      <ul>
        <li class="p_item">
          <a class="m_a" :href="toIndex()" title="回到办公中心">
            <img class="icon" src="@/assets/icons/portal/icon-mywork.png" />
            <span class="text">我的工作</span>
          </a>

          <ul class="ul_sub">
            <li id="newtask"><a href="/Portal/WorkCenter/MyWork?code=newtask">我的待办</a></li>
            <li id="completetask"
              ><a href="/Portal/WorkCenter/MyWork?code=completetask">我的已办</a></li
            >
            <li id="apply"><a href="/Portal/WorkCenter/MyWork?code=apply">我的申请</a></li>
            <li id="myfile"><a href="/Portal/WorkCenter/MyWork?code=myfile">我的文件</a></li>
            <li id="focus"><a href="/Portal/WorkCenter/MyWork?code=focus">我的关注</a></li>
          </ul>
        </li>

        <li class="p_item">
          <a :class="is_open ? 'open m_a expend' : 'm_a expend'" href="#" @click="menu_click">
            <img class="icon" src="@/assets/icons/portal/icon-cyrk.png" />
            <span class="text">点击排行</span>
          </a>

          <ul class="ul_sub ul_sub_sort" :style="{ height: is_open ? '360px' : 0 }">
            <li id="a5d000af-cdb4-4dfd-b8e0-04095f6dffaf"><a>系统开发区</a></li>
            <li id="b03d00bc-8aa4-4afa-8570-c46dafd69d67"><a>项目经理管控一张图</a></li>
            <li id="b053010f-d269-4006-b1c5-5a0c7b01acb3"><a>菜单节点配置</a></li>
            <li id="aff800fe-da76-40a9-ac8e-3b35ceb9351b"><a>电建通消息配置</a></li>
            <li id="67C7C55B-1E60-41E9-B4BD-D0E85BCF1410"><a>资源管理</a></li>
            <li id="b030010b-279b-40b8-9d43-5e91f03100fb"><a>昆明院总承包项目一张图</a></li>
            <li id="b0c9011b-be1e-4fb0-976d-4fd898b13d0b"><a>项目数据大屏</a></li>
            <li id="b065010d-2737-4bb6-90e4-a6146935a84d"><a>权限角色人员</a></li>
            <li id="b06500ff-2a31-4545-87e5-71d4da52ff64"><a>项目数据权限</a></li>
            <li id="b053010f-d269-4006-b1c5-5a0c7b01acb3"><a>项目经理管控配置</a></li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="header_empty"></div>

    <div class="main">
      <div class="flex">
        <div class="tpxw uk-card uk-card-default">
          <div
            class="uk-position-relative uk-visible-toggle uk-light"
            v-if="tpxwList && tpxwList.length > 0"
            tabindex="-1"
            uk-slideshow="autoplay: true;"
          >
            <ul class="uk-slideshow-items">
              <template v-for="(item, index) in tpxwList" :key="index">
                <li @click="OpenView(0, item)">
                  <img :src="item.PicFile" uk-cover uk-img />
                  <div class="uk-position-bottom uk-position-small">
                    <div class="work-top-nva-title" :title="item.Title">
                      <pre class="qkg">{{ item.Title }}</pre>
                    </div>
                  </div>
                </li>
              </template>
            </ul>
            <a
              class="uk-position-center-left uk-position-small uk-hidden-hover"
              href="#"
              uk-slidenav-previous
              uk-slideshow-item="previous"
            ></a>
            <a
              class="uk-position-center-right uk-position-small uk-hidden-hover"
              href="#"
              uk-slidenav-next
              uk-slideshow-item="next"
            ></a>
            <div class="uk-position-bottom-right uk-position-small work-top-nva-bottom">
              <ul class="uk-slideshow-nav uk-dotnav uk-flex-center uk-margin"></ul>
            </div>
          </div>
          <el-empty v-else description="数据为空" />
        </div>

        <div class="yfw_box">院发文</div>
      </div>

      <div class="flex wddb_box"> </div>

      <div class="flex gnsc_box"> </div>

      <div class="flex wdxx_box">
        <div class="wdxx"></div>
        <div class="wdwj"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CATEGORY, useLayoutCache } from '@/hooks/web/categoryCache'

const { wsCache } = useLayoutCache()

//左侧浮动菜单 - 点击排行 - 展开/收缩-------------------------------start
const is_open = ref<boolean>(true)
function menu_click() {
  is_open.value = !is_open.value
}
//左侧浮动菜单 - 点击排行 - 展开/收缩-------------------------------end

const toIndex = () => {
  if (wsCache.get(CATEGORY.IsLayout)) {
    return '/Portal/newsLayout'
  }
  return '/Portal/WorkCenter/Index'
}

//图片新闻
const tpxwList = ref<any[]>([
  {
    Title: '',
    PicFile: '@/assets/icons/portal/IMG_20240802_103041.jpg'
  },
  {
    Title: '',
    PicFile: '@/assets/icons/portal/IMG_20240802_103041.jpg'
  },
  {
    Title: '',
    PicFile: '@/assets/icons/portal/IMG_20240802_103041.jpg'
  },
  {
    Title: '',
    PicFile: '@/assets/icons/portal/IMG_20240802_103041.jpg'
  }
])
function OpenView() {}
</script>

<style lang="scss" scoped>
@import url('@/assets/css/uk.min.css');

* {
  padding: 0;
  margin: 0;
  list-style: none;
  box-sizing: border-box;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header_empty {
  width: 100%;
  height: 80px;
}

.bodyer {
  width: 1390px;
  margin: 0 auto;
  position: relative;
  color: rgb(102, 102, 102);
  overflow-y: auto;

  .siderbar {
    position: fixed;
    z-index: 999;
    margin-top: 80px;
    width: 260px;
    border-radius: 3px;
    background: #fff;
    color: #666;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    padding: 40px 0;

    ul {
      margin: 0 !important;
      padding: 0 !important;
    }

    .p_item {
      white-space: nowrap;
      text-overflow: ellipsis;
      border-left: 2px solid #fff;

      .m_a {
        display: inline-block;
        color: black;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        height: 30px;

        &.expend::after {
          background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Cpolyline%20fill%3D%22none%22%20stroke%3D%22%23666%22%20stroke-width%3D%221.1%22%20points%3D%2210%201%204%207%2010%2013%22%20%2F%3E%0A%3C%2Fsvg%3E);
          background-repeat: no-repeat;
          background-position: 50% 50%;
          transform: rotate(180deg);
          transition: transform 0.4s ease-out;
          width: 1.5em;
          height: 1.5em;
          content: '';
          margin-left: 65px;
        }

        &.open::after {
          transform: rotate(90deg);
        }

        .icon {
          margin-left: 40px;
        }

        .text {
          font-size: 15px;
          margin-left: 5px;
        }
      }

      .ul_sub {
        transition: height 0.3s ease-out;
        overflow: hidden;
        margin-left: -30px;

        li {
          height: 36px;
          line-height: 36px;
          width: 100%;

          a {
            width: 100%;
            height: 100%;
            display: inline-block;
            color: #999;
            font-weight: normal;
            padding-left: 63px;
            text-decoration: none;
            cursor: pointer;

            &:hover {
              color: #333;
            }
          }
        }
      }
    }
  }

  .main {
    width: 1104px;
    height: 3200px;
    border: 1px solid blue;
    margin-left: auto;
  }
}

.tpxw {
  border: 1px solid blue;
  width: 485px;
  height: 330px;
}

.yfw_box {
  border: 1px solid blue;
  width: 590px;
  height: 330px;
}
</style>
