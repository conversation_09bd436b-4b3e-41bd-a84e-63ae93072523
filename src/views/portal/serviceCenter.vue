<template>
  <div class="portal_page">
    <div v-if="sideShow" class="custom-left-menu-bar">
      <div class="close-icon" @click="closeOpen">
        <el-icon color="#fff">
          <Close />
        </el-icon>
      </div>
      <div
        class="uk-card uk-card-default uk-card-small uk-card-body"
        style="min-height: 350px; box-shadow: 0 0 12px rgba(0, 0, 0, 0.12)"
      >
        <div class="custom-left-navigation-title">
          <img src="@/assets/imgs/lefttitle.png" /><span>子系统入口</span>
        </div>
        <ul class="custom-left-menu-list">
          <template v-for="(item, index) in leftMenuCategory" :key="index">
            <li
              :class="['custom-left-menu-item-' + item.code]"
              :data-code="item.code"
              @mouseenter="onEnter($event, index)"
              @mouseleave="onLeave($event, index)"
            >
              <div>
                <div :class="['left-menu-name-' + item.code]">{{ item.name }}</div>
                <span uk-icon="chevron-right">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <polyline
                      fill="none"
                      stroke="#000"
                      stroke-width="1.03"
                      points="7 4 13 10 7 16"
                    />
                  </svg>
                </span>
              </div>
            </li>
          </template>
        </ul>

        <div
          class="custom-left-menu-entry-content-itme"
          @mouseenter="onEnterContent($event)"
          @mouseleave="onLeaveContent($event)"
        >
          <div class="menu_list">
            <div class="group" v-for="(item, index) in menu_list" :key="index">
              <template v-for="subItem in menu_list[index]">
                <a
                  @click="leftMenuLog(subItem)"
                  target="_blank"
                  v-if="activeCode == subItem.XTFL"
                  :key="subItem.XTFL"
                  :href="go_url(subItem)"
                  >{{ subItem.ShowName }}</a>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="sm_side" v-if="!sideShow" class="sm_side">
      <div class="align-center hide_luntan" @click="closeOpen" title="我的工作">
        <img class="w-36 h-36" src="@/assets/imgs/home/<USER>" alt="我的工作" />
      </div>
    </div>
    <div class="body">
      <div
        class="custom-right-menu-content"
        v-loading="show_loading"
        style="left: 0; min-height: 600px"
      >
        <div class="uk-card uk-card-default uk-card-small uk-card-body">
          <ul class="custom-breadcrumb" style="padding-left: 0">
            <template v-for="(item, index) in enterpriseNature" :key="index">
              <li>
                <span style="color: #e5e5e5; padding-right: 10px" v-show="index > 0">|</span>
                <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
              </li>
            </template>
          </ul>
          <ul class="custom-breadcrumb" style="padding-left: 0">
            <template v-for="(item, index) in businessNature" :key="index">
              <li>
                <span style="color: #e5e5e5; padding-right: 10px" v-show="index > 0">|</span>
                <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
              </li>
            </template>
          </ul>
          <ul class="custom-breadcrumb" style="padding-left: 0">
            <template v-for="(item, index) in serverNature">
              <li :key="index" v-if="index == 0 || index == 2">
                <span style="color: #e5e5e5; padding-right: 10px" v-show="index > 0">|</span>
                <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
              </li>
            </template>
            <template v-for="(item, index) in serverNature">
              <li :key="index" v-if="index == 1">
                <span style="color: #e5e5e5; padding-right: 10px">|</span>
                <a @click="onGoToLocation('#code' + item.ID)">{{ item.Name }}</a>
              </li>
            </template>
          </ul>
        </div>
        <template v-for="(item, index) in list" :key="index">
          <div :id="['code' + item.ID]" class="uk-card uk-card-small">
            <my-panel
              :title="item.Name"
              :height="'auto'"
              :show-more="false"
              :is-logo="true"
              :logo-img="proxy.$getFullUrl(item.Icon)"
            >
              <div class="uk-child-width-1-4@l custom-grid uk-grid">
                <div
                  v-for="(secondItem, secondIndex) in item.SubMenu"
                  :key="secondIndex"
                  class="custom-grid-item"
                >
                  <div class="custom-grid-child">
                    <div class="custom-grid-content">
                      <div class="custom-two-level">
                        <img
                          class="custom-two-level-img"
                          :src="proxy.$getFullUrl(secondItem.Img)"
                        />
                        <div class="custom-grid-content">
                          <img class="custom-small-img" :src="proxy.$getFullUrl(secondItem.Icon)" />
                          <span class="custom-two-level-title">&nbsp;{{ secondItem.Name }}</span>
                        </div>
                        <hr class="custom-divider" />
                      </div>
                      <ul class="uk-list right-entry-list">
                        <!-- 显示列表 -->
                        <li
                          v-for="(thirdItem, thirdIndex) in secondItem.ShowList"
                          :key="thirdIndex"
                          class="custom-show-list"
                        >
                          <a
                            @click="
                              logRecord(
                                $event,
                                thirdItem.Name,
                                thirdItem.Url,
                                thirdItem.ServiceDirectoryID
                              )
                            "
                            class="jump-link"
                            :title="thirdItem.Name"
                          >
                            <span class="uk-text-truncate">{{ thirdItem.Name }}</span>
                            <div class="service-directory-modal">
                              <span
                                uk-toggle="target: #modal-sections"
                                @click="
                                  onServiceDirectory(
                                    $event,
                                    thirdItem.ServiceDirectoryID,
                                    thirdItem.Url,
                                    thirdItem.Name
                                  )
                                "
                                class="jump-link-modal"
                                v-show="thirdItem.IsEnable !== '0'"
                                >服务指南</span
                              >
                            </div>
                            <span uk-icon="chevron-right" style="width: 10px; top: -16px">
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <polyline
                                  fill="none"
                                  stroke="#000"
                                  stroke-width="1.03"
                                  points="7 4 13 10 7 16"
                                />
                              </svg>
                            </span>
                          </a>
                        </li>
                        <!-- 展开列表 -->
                        <li
                          v-for="(thirdItem, thirdIndex) in secondItem.SwitchList"
                          :key="thirdIndex"
                          v-show="false"
                          :class="['custom-switch-list-' + index + '-' + secondIndex]"
                        >
                          <a
                            @click="
                              logRecord(
                                $event,
                                thirdItem.Name,
                                thirdItem.Url,
                                thirdItem.ServiceDirectoryID
                              )
                            "
                            class="jump-link"
                            :title="thirdItem.Name"
                          >
                            <span class="uk-text-truncate">{{ thirdItem.Name }}</span>
                            <div class="service-directory-modal">
                              <span
                                uk-toggle="target: #modal-sections"
                                @click="
                                  onServiceDirectory(
                                    $event,
                                    thirdItem.ServiceDirectoryID,
                                    thirdItem.Url,
                                    thirdItem.Name
                                  )
                                "
                                class="jump-link-modal"
                                v-show="thirdItem.IsEnable !== '0'"
                                >服务指南</span
                              >
                            </div>
                            <span uk-icon="chevron-right" style="width: 10px; top: -16px">
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <polyline
                                  fill="none"
                                  stroke="#000"
                                  stroke-width="1.03"
                                  points="7 4 13 10 7 16"
                                />
                              </svg>
                            </span>
                          </a>
                        </li>
                      </ul>
                      <div
                        class="view-more"
                        v-show="secondItem.IsMoreThanFour"
                        @click="onExpandAndCollapse($event, index, secondIndex)"
                      >
                        <div
                          :class="['custom-expand-collapse-switch-' + index + '-' + secondIndex]"
                        >
                          <span uk-icon="triangle-down">
                             </span><span class="view-all">展开</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </my-panel>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import * as PortalApi from '@/api/system/portal'
import { replaceUrl } from '@/assets/js/NK.js'
import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'
import { getCurrentInstance } from 'vue'
import { searchStore } from '@/store/modules/search'
import { funcLog } from '@/api/system/pageAndFuncLog'
import $ from 'jquery'

const message = useMessage()
import MyPanel from '@/components/Panel/src/index.vue'
import { Close } from '@element-plus/icons-vue'

export default {
  components: { Close, MyPanel },
  data() {
    return {
      gSearchStore: searchStore(),
      showTopSearch: true,
      sideShow: false,
      leftMenuCategory: [],
      leftMenuShowColumnNumber: 4,
      internalList: [],
      externalList: [],
      activeIndex: -1,
      requestLeftMenuListPath: '/Portal/ServiceCenter/LeftList',
      list: [],
      requestServiceCenterEntryListPath: '/Portal/ServiceCenter/List',
      searchContent: '',
      requestGetServiceDirectoryPath: '/Portal/ServiceCenter/GetServiceDirectory',
      requestDownloadFilePath: '/FileManager/DownloadFile/GetFileToken',

      //服务指南
      serviceDirectoryContent: '',
      serviceDirectoryName: '',
      serviceDirectoryUrl: '',

      //关联制度
      Institutions: [],

      //相关附件
      OtherFile: [],

      //模态框ID
      modalId: null,
      scrollDistance: 0,

      //特殊处理
      enterpriseNature: [],
      businessNature: [],
      serverNature: [],

      enterpriseText: '企业性质',
      businessText: '业务性质',
      serverText: '服务性质',

      menu_list: [], //菜单项
      temp_list: [],
      activeCode: 0, //菜单激活code
      proxy: {},
      show_loading: false
    }
  },
  watch: {
    deep: true,
    'gSearchStore.searchValue': function (n) {
      this.searchContent = n
      this.loadData()
    }
  },
  mounted: function () {
    const { proxy } = getCurrentInstance()
    this.proxy = proxy
    this.gSearchStore.setToolbarValue('other')
    $('.custom-left-menu-entry-content-itme').hide()
    this.searchContent =
      $('.sys_tp_search>input').val() == null ? '' : $('.sys_tp_search>input').val()
    this.loadLeftMenu()
    this.loadData()

    // 暂时注释
    // UIkit.util.on("#modal-sections", "hide", function () {
    //     $nextTick(function () {
    //         document.body.scrollTo(0, this.scrollDistance);
    //     });
    // });
  },

  methods: {
    closeOpen() {
      this.sideShow = !this.sideShow
    },
    //跳转链接方法
    go_url: function (subItem) {
      let url = subItem.Url

      if (url.indexOf('?') === -1) {
        url += '?'
      } else {
        url += '&'
      }

      url += 'code=' + subItem.DoorCode

      if (url.indexOf('token') === -1) {
        url += '&token=' + subItem.Token
      }

      url = replaceUrl({ url: url })

      // 相对路径修改为绝对路径
      if (!url.startsWith('http')) {
        url = this.proxy.$getFullUrl(url)
      }

      return url
    },

    onEnter: function (e) {
      //显示右边菜单框
      let content = $('.custom-left-menu-entry-content-itme')
      content.show()
      //赋值显示的对应菜单项
      this.activeCode = e.currentTarget.dataset.code //自定义code属性

      $('.left-menu-name-' + this.activeCode).css({
        color: '#39f'
      })
      $('.custom-left-menu-item-' + this.activeCode).css({
        background: '#F4FAFF',
        'border-left': '4px solid #0070FF'
      })
    },

    onLeave: function (e) {
      //隐藏右边菜单框
      let content = $('.custom-left-menu-entry-content-itme')
      content.hide()

      //赋值显示的对应菜单项
      this.activeCode = e.currentTarget.dataset.code //自定义code属性

      //设置样式
      $('.left-menu-name-' + this.activeCode).css({ color: '#666' })
      $('.custom-left-menu-item-' + this.activeCode).css({
        background: 'white',
        'border-left': '4px solid white'
      })
    },

    //鼠标移动到左边菜单，显示右边菜单内容
    onEnterContent: function (e) {
      let content = $('.custom-left-menu-entry-content-itme')
      content.show()
      $('.custom-left-menu-item-' + this.activeCode).css({
        color: '#39f',
        background: '#F4FAFF',
        'border-left': '4px solid #0070FF'
      })
    },

    //鼠标移开菜单
    onLeaveContent: function (e) {
      let content = $('.custom-left-menu-entry-content-itme')
      content.hide()
      $('.custom-left-menu-item-' + this.activeCode).css({
        color: '#666',
        background: 'white',
        'border-left': '4px solid white'
      })
    },

    //加载菜单名称数据
    loadLeftMenu: async function () {
      //将职务信息更新到主表
      const userStore = useUserStore()
      let sqlData = await PortalApi.execSystemScript({
        code: 'SQL_adde0127f04a415780e9df7d0316867d',
        ExecData: JSON.stringify([{ UserID: userStore.getUser.id }])
      })
      sqlData.forEach((item) => {
        item.Token = getAccessToken()
        // 重构数据，只获取需要的数据
        this.temp_list.push({
          Url: item.Url,
          DoorCode: item.DoorCode,
          Token: item.Token,
          ShowName: item.ShowName,
          XTFL: item.XTFL
        })
      })

      // 分组
      this.menu_list = this.group(this.temp_list, this.leftMenuShowColumnNumber)

      //获取左侧菜单分类
      let result = await PortalApi.serviceCenterGetLeftListCat()
      let _temp = []
      result.forEach((item) => {
        this.temp_list.forEach((item2) => {
          if (item.Code == item2.XTFL && _temp.indexOf(item.Code) === -1) {
            _temp.push(item.Code)
            this.leftMenuCategory.push({
              code: item.Code,
              name: item.Name
            })
          }
        })
      })
    },

    onSearch: function (keyword) {
      if (keyword === undefined || keyword === '') {
        window.location.reload()
      } else {
        searchContent = keyword
      }
      loadData()
    },

    //获取服务中心右侧数据列表
    loadData: async function (sv) {
      this.enterpriseNature = []
      this.serverNature = []
      this.businessNature = []
      this.list = []
      this.show_loading = true
      // console.log(this.$route.query.searchValue)
      // var sv = this.$route.query.searchValue;
      if (sv) {
        this.searchContent = sv
      }
      let result = await PortalApi.serviceCenterloadData({ Search: this.searchContent })
      result.forEach((firstLevel, firstLevelIndex) => {
        //*******特殊处理*******
        if (
          firstLevel.Name == '党建工团' ||
          firstLevel.Name == '行政办公' ||
          firstLevel.Name == '三会决策' ||
          firstLevel.Name == '事项督办' ||
          firstLevel.Name == '纪监审法务'
        ) {
          this.enterpriseNature.push(firstLevel)
          // console.log(this.enterpriseNature);
        } else if (
          firstLevel.Name == '外事管理' ||
          firstLevel.Name == '档案管理' ||
          firstLevel.Name == '用车管理'
        ) {
          this.serverNature.push(firstLevel)
        } else {
          this.businessNature.push(firstLevel)
        }

        //*******特殊处理*******
        if (firstLevel.SubMenu.length > 0) {
          firstLevel.SubMenu.forEach(function (secondLevel, secondLevelIndex) {
            secondLevel.IsExpandCollapse = false
            if (secondLevel.SubMenu.length > 0) {
              secondLevel.IsMoreThanFour = secondLevel.SubMenu.length > 4
              secondLevel.ShowList = secondLevel.SubMenu.slice(0, 4)
              secondLevel.SwitchList = secondLevel.SubMenu.slice(4, secondLevel.SubMenu.length)
            }
            secondLevel.SubMenu = []
          })
        }
      })
      this.list = result
      this.show_loading = false
    },

    group: function (array, groupLength) {
      let index = 0
      let newArray = []
      while (index < array.length) {
        newArray.push(array.slice(index, (index += groupLength)))
      }
      return newArray
    },

    onServiceDirectory: function (e, serviceDirectoryID, url, businessName) {
      e.cancelBubble = true //取消事件冒泡，防止打开业务功能事件的响应

      serviceDirectoryName = businessName
      serviceDirectoryUrl = url
      scrollDistance = getScrollTop()

      //获取服务指南内容
      $.post(
        requestGetServiceDirectoryPath,
        { serviceDirectoryID: serviceDirectoryID },
        function (result) {
          if (result.length > 0) {
            serviceDirectoryContent = result[0].ServiceDirectory
            Institutions = result[0].Institutions !== null ? result[0].Institutions.split(',') : ''
            OtherFile = result[0].OtherFile !== null ? result[0].OtherFile.split(',') : ''
          }
        }
      )
    },

    onDownloadAndPreviewFile: function (e, fileId, type) {
      let newWindow = window.open()
      let result = GetFileToken(fileId)
      if (result) {
        if (type === 1) {
          newWindow.location.href = addUrlSearch('/FileManager/DownloadFile?FileToken=' + result)
        } else if (type === 2) {
          newWindow.location.href = addUrlSearch(
            '/UIBuilder/UIViewer/PreviewPage?FileToken=' + result
          )
        }
      } else {
        alert('该文件验证失败，暂不能下载，请联系管理员！')
      }
    },

    // 日志
    logRecord: async function (e, name, url, menuId) {
      const userStore = useUserStore()
      if (url === null || url === '' || url === undefined) {
        if (url === null || url === '' || url === undefined) {
          message.error('暂未关联相关业务功能。')
          return
        }
        return
      }
      let pageName = $('.uk-container-center ul li .active').text()
      let pageUrl = window.location.href
      //let requestUrl = this.proxy.$getFullUrl('/KHIDIService/SysLog/FuncLog')

      let objdata = {
        workno: userStore.user.workNo,
        funcname: name,
        funcurl: url,
        menuId: menuId
      }
      // 相对路径修改为绝对路径
      if (!url.startsWith('http')) {
        url = this.proxy.$getFullUrl(url)
      }
      if (url.indexOf('{token}') != -1) {
        url = url.replace('{token}', getAccessToken())
      } else if (url.indexOf('?') == -1) {
        url = url + '?token=' + getAccessToken()
      } else {
        url = url + '&token=' + getAccessToken()
      }
      // 暂时注释
      let tourl = url.replace('{WorkNo}', userStore.user.workNo)
      // newWindow.location.href = url.replace('{WorkNo}', userStore.user.workNo)
      const log = await funcLog(objdata)
      window.open(tourl)

      //TODO 日志记录待开发
    },

    getScrollTop: function () {
      let scrollTop = 0
      if (document.documentElement && document.documentElement.scrollTop) {
        scrollTop = document.documentElement.scrollTop
      } else if (document.body) {
        scrollTop = document.body.scrollTop
      }
      return scrollTop
    },

    onGoToLocation: function (id) {
      let eid = $(id)
      $('html,body').animate(
        {
          scrollTop: parseFloat(eid.offset().top) - 70
        },
        200
      )
    },

    onExpandAndCollapse: function (e, index, secondIndex) {
      let el = $('.custom-switch-list-' + index + '-' + secondIndex)
      if (el.css('display') === 'none') {
        el.show(500)
      } else {
        el.hide(500)
      }
      let expandCollapse = $('.custom-expand-collapse-switch-' + index + '-' + secondIndex)
      if (expandCollapse.text() === '展开') {
        expandCollapse.find('span').eq(0).attr('uk-icon', 'triangle-up')
        expandCollapse.find('span').eq(1).text('收缩')
      } else {
        expandCollapse.find('span').eq(0).attr('uk-icon', 'triangle-down')
        expandCollapse.find('span').eq(1).text('展开')
      }

      this.list[index].SubMenu[secondIndex].IsExpandCollapse =
        !this.list[index].SubMenu[secondIndex].IsExpandCollapse
    },
    leftMenuLog(item) {
      let objdata = {
        funcname: item.ShowName,
        funcurl: item.Url,
        menuId: '子系统入口'
      }
      const log = funcLog(objdata)
    }
  }
}

// let selectedObj = ref()
// // 组件的生命周期钩子
// onMounted(() => {
//   console.log('视图已挂载');
// });
</script>

<style lang="scss" scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/master.css');
@import url('@/assets/css/bootstrap.min.css');
@import url('@/assets/css/service-center.css');
@import url('@/assets/css/default.css');

.portal_page {
  position: relative;
}

#sm_side {
  height: 80px;
  width: 60px;
  position: fixed;
  cursor: pointer;
  left: 20px;
  top: 400px;
  z-index: 1000;

  .align-center {
    display: flex;
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
    justify-content: center;
    align-items: center;

    .w-36 {
      width: 36px;
    }

    .h-36 {
      height: 36px;
    }
  }

  .hide_luntan {
    width: 60px;
    height: 60px;
    margin-top: 10px;
    background: linear-gradient(135deg, #3e9fff 0%, #176ff2 100%);
    border-radius: 4px;
  }
}

.close-icon {
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  left: 234px;
  top: -12px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  background: rgb(62, 163, 247);
  z-index: 999;
}

.logo {
  line-height: normal;
}

.uk-section-default {
  margin: 0 0 150px 0;
  background: #f9fafd;
}

.custom-left-navigation-title {
  margin-bottom: 2px;
}

.custom-left-navigation-title img {
  width: 30px;
  height: 20px;
  padding-right: 10px;
  margin-bottom: 6px;
}

.custom-left-navigation-title span {
  font-size: 16px;
  color: #333;
}

.custom-left-menu-list {
  margin: 0 -20px;
}

.custom-left-menu-list li div {
  padding: 5px 0;
  text-align: right;
}

.custom-left-menu-list li div div {
  color: #666;
  font-size: 14px;
  display: inline-block;
  padding-right: 112px;
  cursor: pointer;
}

.custom-left-menu-list li div span {
  display: inline-block;
  padding-right: 10px;
  width: 20px;
}

.custom-left-menu-list li {
  border-left: 4px solid white;
  /* margin-left: -30px; */
}

/*          .custom-left-menu-list li:hover {
                color: #0070FF;
                cursor: pointer;
                border-left: 4px solid #0070FF;
                background: #F4FAFF;
            }*/

.custom-left-menu-entry-content-itme {
  position: absolute;
  left: 192px;
  top: 0;
  z-index: 999;
  min-width: 1192px;
  min-height: 350px;
  border: 1px solid #c6ddfb;
  background-color: #fff;
  /*      -webkit-box-shadow: 2px 0 2px #c6ddfb;
        box-shadow: 2px 0 2px #c6ddfb;*/
  -webkit-transition: top 0.25s ease;
  transition: top 0.25s ease;
}

.custom-breadcrumb li {
  display: inline-block;
  margin: 5px;
}

.custom-breadcrumb li a {
  color: #333;
  font-size: 14px;
}

.custom-breadcrumb li a:hover {
  text-decoration: none;
  color: #333;
  font-size: 14px;
}

.custom-breadcrumb li span {
  display: inline-block;
  text-align: right;
}

.uk-breadcrumb li a {
  color: #333;
}

.custom-card {
  margin-top: 10px;
}

.custom-card .custom-one-level-img {
  width: 20px;
  height: 20px;
  margin-top: -8px;
}

.custom-card .custom-one-level-title {
  font-size: 16px;
  color: #333;
}

.custom-grid .custom-grid-child > .custom-grid-content {
  margin: 10px 0 0 0;
  border: 1px solid #e5e5e5;
  min-height: 350px;
}

.custom-two-level {
  padding: 20px 20px 0 20px;
}

.custom-two-level .custom-two-level-img {
  width: 100%;
  min-height: 60px;
  max-height: 80px;
  margin-bottom: 15px;
  border-radius: 2px;
}

.custom-two-level hr {
  margin: 10px 0 !important;
}

.custom-grid-content .custom-small-img {
  width: 20px;
  height: 20px;
  margin-top: -5px;
}

.custom-grid-content ul {
  padding: 0 20px;
}

.view-more {
  width: 100%;
  height: 45px;
  border-top: 1px solid #e5e5e5;
  text-align: center;
  color: #999;
  font-size: 16px;
  padding: 10px 0 0 0;
}

.view-more .view-all {
  cursor: context-menu;
}

/**左侧显示菜单项*/
.custom-left-menu-bar {
  width: 240px;
  position: fixed;
  z-index: 999;
  left: 4px;
  margin-top: 14px;
}

.custom-right-menu-content {
  position: relative;
  left: 230px;
  margin-bottom: 100px;
}

.custom-left-menu-entry-content-itme {
  padding: 20px;
}

.custom-table-menu-list tbody tr td:before {
  content: '';
  position: absolute;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
}

.custom-table-menu-list tbody tr td a {
  padding-left: 20px;
  color: #333;
}

.custom-table-menu-list tbody tr td a:hover {
  padding-left: 20px;
  color: #333;
  text-decoration: none;
}

.uk-grid > * {
  padding-left: 10px;
  padding-right: 20px;
}

.uk-grid {
  padding-left: 30px;
  margin-right: -20px;
}

.custom-two-level-title {
  font-size: 16px;
  color: #333;
}

.right-entry-list li {
  width: 214px;
  height: 30px;
  vertical-align: middle;
  line-height: 30px;
  clear: both;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.right-entry-list li .jump-link {
  display: block;
  padding-left: 15px;
  color: #666;
  font-size: 14px;
  width: 100%;
  text-decoration: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding-left: 10px;
}

.right-entry-list li .jump-link:hover {
  color: #666;
}

.right-entry-list li .jump-link:before {
  content: '';
  position: absolute;
  left: -10px;
  top: 5px;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  text-decoration: none;
}

.right-entry-list li .jump-link-modal {
  display: inline;
  color: #32b66b;
  font-size: 12px;
  border: 1px solid #32b66b;
  padding: 0 4px;
  border-radius: 3px;
  width: 58px;
}

.right-entry-list li .jump-link-modal:hover {
  display: inline;
  color: #32b66b;
  text-decoration: none;
  font-size: 12px;
  border: 1px solid #32b66b;
  padding: 0 4px;
  border-radius: 3px;
  width: 58px;
}

.custom-grid .custom-grid-item {
  min-width: 270px;
}

.right-entry-list .service-directory-modal {
  display: inline-block;
  width: 58px;
  /* position: relative; */
  /* top: -10px; */
}

.right-entry-list span {
  width: 120px;
  display: inline-block;
  /* position: relative; */
  right: 0;
  /* top: -6px; */
}

/**scroll*/
.right-entry-list::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 2px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.right-entry-list::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: skyblue;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

.right-entry-list::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}

.view-entry-all {
  position: absolute;
  border: 1px solid #c6ddfb;
  min-width: 256px;
  z-index: 10;
  background: #f9fafd;
  margin-top: -6px;
}

.view-entry-all {
  padding: 10px 20px 0 20px;
}

.uk-notification {
  top: 80px;
}

.uk-notification-message {
  background: #edf2fc;
}

.uk-notification-message div {
  color: #39f;
  font-size: 18px;
}

.uk-flex {
  display: block;
  padding-top: 10%;
}

.el-message--error {
  margin-top: 60px;
}

/**服务中心菜单 start */
table tr {
  border: 1px solid red;
}

table td {
  border: 1px solid blue;
}

.menu_list a {
  display: block;
  height: 36px;
  color: #333;
  padding: 10px;
  width: 25%;
  border-left: 1px solid rgb(229, 229, 229);
  float: Left;
  font-size: 14px;
  padding-left: 20px;
}

.menu_list a:before {
  content: '';
  position: absolute;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  margin-left: -20px;
}

/**服务中心菜单 end */
</style>
