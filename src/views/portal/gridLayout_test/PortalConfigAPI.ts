import request from '@/config/axios'

// 保存门户布局配置
export function savePortalLayout(data: any) {
  return request.post({
    url: '/system/portal/saveLayout',
    data
  })
}

// 获取门户布局配置
export function getPortalConfig() {
  return request.get({
    url: '/system/portal/config'
  })
}

// 获取门户数据权限
export function getPortalPermissions() {
  return request.get({
    url: '/system/portal/permissions'
  })
}

// 更新门户数据权限
export function updatePortalPermissions(data: any) {
  return request.post({
    url: '/system/portal/permissions',
    data
  })
}
