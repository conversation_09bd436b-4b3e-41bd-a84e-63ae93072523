<template>
  <div class="layout-handel-top">
    <el-form :model="handelFrom" label-width="80px" :inline="true">
      <el-form-item label="组件模块">
        <el-select v-model="handelFrom.category" placeholder="请选择组件模块" clearable>
          <el-option label="办公中心" value="workCenter" />
          <el-option label="服务中心" value="serviceCenter" />
          <el-option label="咨询中心" value="consultCenter" />
          <el-option label="综合处理" value="allCenter" />
        </el-select>
      </el-form-item>
      <el-form-item label="布局分组" prop="name">
        <el-select v-model="handelFrom.type" placeholder="请选择组件模块" clearable>
          <el-option label="权限默认布局" value="AUTH" />
          <el-option label="用户布局" value="USER" />
        </el-select>
      </el-form-item>
      <el-form-item label="权限分组" prop="name">
        <el-select v-model="handelFrom.group" placeholder="请选择组件模块" clearable filterable>
          <el-option label="默认布局" value="DEFAULT" />
          <el-option label="第一中布局" value="DEFAULT1" />
          <el-option label="第二种布局" value="DEFAULT2" />
        </el-select>
      </el-form-item>

      <el-form-item label="用户列表" prop="name" v-if="handelFrom.type == 'USER'">
        <el-select
          v-model="handelFrom.userId"
          clearable
          filterable
          remote
          reserve-keyword
          placeholder="请输入用户名"
          :remote-method="remoteMethod"
          :loading="loading"
          style="width: 240px"
        >
          <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-button @click="searchLayout">搜索布局</el-button>
      <el-button type="primary" @click="coplyLayoout">复制布局</el-button>
      <el-button type="success" @click="useLayoout">使用布局</el-button>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { AUserApi } from '@/api/system/auser'
import emitter from '@/utils/mitt'
const loading = ref(false)
const handelFrom = ref({
  category: 'workCenter',
  type: 'AUTH',
  group: 'DEFAULT',
  userId: ''
})

// 处理搜索数据
const searchLayout = () => {
  if (handelFrom.value.type == 'AUTH') {
    handelFrom.value.userId = ''
  }
  emitter.emit('searchLayout', handelFrom.value)
}

const coplyLayoout = () => {
  emitter.emit('coplyLayoout')
}
const useLayoout = () => {
  emitter.emit('useLayoout')
}

// 获取用户列表
const userList = ref()
const getUserList = async (name: string) => {
  loading.value = true
  const res = await AUserApi.getUserAll({ name: name })
  userList.value = res
  loading.value = false
}

const remoteMethod = (query: string) => {
  if (query) {
    getUserList(query)
  }
}

onMounted(() => {
  getUserList('')
})
</script>

<style lang="scss" scoped>
.layout-handel-top {
  width: 100%;
  height: 6%;
  position: fixed;
  /* 如果需要进一步指定其位置，可以添加以下属性，例如 */
  left: 0;
  top: 0;
  z-index: 99;
  background-color: #fff;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
  display: inline-block;
}
.el-form-item {
  margin-top: 15px;
  width: 260px;
}
</style>
