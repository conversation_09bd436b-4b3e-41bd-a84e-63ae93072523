<template>
  <div class="lay-container">
    <!-- <div>
            <el-button>保存</el-button>
          </div> -->
    <div style="width: 98%; margin: auto">
      <el-form :inline="true" label-width="80px" class="demo-form-inline">
        <el-form-item>
          <el-checkbox v-model="showGroup" label="开启组件" size="large" />
        </el-form-item>
        <!-- <el-form-item >
              <el-checkbox v-model="show" label="开启删除" size="large" />
          </el-form-item> -->
        <el-form-item>
          <el-checkbox
            v-model="draggable"
            label="开启拖拽"
            size="large"
            @change="changeDraggable"
          />
          <el-checkbox
            v-model="resizable"
            label="开启缩放"
            size="large"
            @change="changeResizable"
          />
        </el-form-item>
        <el-form-item>
          <el-button size="small">
            <el-icon style="vertical-align: middle">
              <template #default>
                <FolderChecked />
              </template>
            </el-icon>
            <span style="vertical-align: middle">保存</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="layoutJSON">
      <span>布局数据</span>
      <div class="columns">
        <div class="layoutItem" v-for="item in layoutList" :key="item.i">
          <b>{{ item.i }}</b
          >: [{{ item.x }}, {{ item.y }}, {{ item.w }}, {{ item.h }}]
        </div>
      </div>
    </div>
    <div class="group" v-if="showGroup" style="width: 97%; margin: auto">
      <div
        v-for="item in layoutList"
        :key="item.i"
        class="droppable-element"
        draggable="true"
        unselectable="on"
        @click="handelList(item)"
      >
        <span>{{ item.name }}</span>
        <hr />
        <span>点击添加</span>
      </div>
    </div>
    <div id="content">
      <grid-layout
        ref="gridlayout"
        :margin="[10, 20]"
        :layout="layoutList"
        :col-num="colNum"
        :row-height="rowHeight"
        :is-draggable="draggable"
        :is-resizable="resizable"
        :vertical-compact="true"
        :use-css-transforms="true"
        :auto-size="true"
        class="top"
        @layout-updated="layoutUpdatedEvent"
      >
        <grid-item
          class="father"
          ref="items"
          v-for="item in 1"
          style="background-color: beige;"
          :key="item"
          :x="0"
          :y="0"
          :w="10"
          :h="10"
          :i="item"
        >
          <!-- <component :style="itemStyle" :Item="item.itemJson" :is="getItem(item.sign)" /> -->
          <span class="remove" v-show="show" @click="removeItem(item.i)">x</span>
        </grid-item>
      </grid-layout>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { FolderChecked } from '@element-plus/icons-vue'
import * as LayoutApi from '@/api/system/layout'
import { customList } from '@/utils/custom'
defineOptions({
  name: 'Layout'
})

let colNum = ref(36) //删格列数
let rowHeight = ref(0.5) //默认高度px

const itemStyle = ref()

// 布局对象信息
interface LayoutItem {
  id: string
  name: string
  sign: string
  x: number
  y: number
  w: number
  h: number
  i: number
  minH: string
  minW: string
  itemJson: {
    titleList: {}
    itemSystem: {}
    isShow: {}
  }
}

// 获取布局列表数据
const layoutList = ref<Array<LayoutItem>>([])
const getLayoutList = () => {
  LayoutApi.getLayoutList({ category: 'workCenter' }).then((ret) => {
    console.log(ret)
    layoutList.value = ret.use

  })
}
const groupList = ref()
// 添加集合数据
const handelList = (item) => {
  groupList.value = groupList.value.filter((ite) => ite.i != item.i)

}

//当前布局界面
const nowLayout = ref<Array<LayoutItem>>([])

let dragShow = ref(true) //是否显示拖拽组件

let draggable = ref(true) //是否开启拖拽
let resizable = ref(true) //是否开启缩放

//布局更新事件处理函数
const layoutUpdatedEvent = () => {
  //console.log('layout updated', layout.value);
}
//显示删除按钮
let show = ref(false)
//删除项
const removeItem = (i: number) => {
  dragShow.value = true
}
//显示组件
let showGroup = ref(false)

const getItem = (type: string) => {
  const item = customList.value.find((item) => item.name.name == type)
  return item?.name
}

let items: any = ref(null) //布局项引用{[key: string]:any}
onMounted(() => {
  getLayoutList()
})

const events = ref<{ pointerEvents?: string }>({ pointerEvents: 'none' })

const changeDraggable = (val: boolean) => {
  if (val) {
    events.value.pointerEvents = 'none'
  } else {
    events.value = {}
  }
}
const changeResizable = (val: boolean) => {
  if (val) {
    events.value.pointerEvents = 'none'
  } else {
    events.value = {}
  }
}
</script>

<style scoped lang="scss">
.lay-container {
  width: 90%;
  margin: 0 auto;
  margin-top: 5%;
}

.father {
  box-sizing: border-box;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
}
::v-deep .vue-grid-placeholder {
  background-color: #ccc;
}

.layoutJSON {
  background: #ddd;
  border: 1px solid black;
  margin-top: 10px;
  padding: 10px;
}
.columns {
  -moz-columns: 120px;
  -webkit-columns: 120px;
  columns: 120px;
}
.bg {
  background-color: blue !important;
}
.group {
  margin-top: 10px; /* 设置组件的顶部间距 */
  background-color: #ddd; /* 设置组件的背景颜色 */
  padding: 10px;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.09);
}
</style>
