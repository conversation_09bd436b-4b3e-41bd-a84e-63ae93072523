<template>
  <div 
    class="draggable-panel" 
    :style="{ 
      width: `${width}px`, 
      height: `${height}px`,
      transform: `translate(${position.x}px, ${position.y}px)`,
      zIndex: isDragging ? '100' : '10'
    }"
    :class="{ 'dragging': isDragging }"
    @mousedown="startDrag"
  >
    <div class="panel-title" @mousedown.stop="startDrag">
      <div class="icon">
        <div class="top"></div>
        <div class="bottom"></div>
      </div>
      <div class="cn">
        <span>{{ title }}</span>
        <span v-if="showNumber" class="num">{{ numberCount }}</span>
      </div>
      <div class="eng">
        {{ subtitle }}
      </div>
      <div v-if="showMore" class="more" @click.stop="$emit('more-click')">
        更多
      </div>
    </div>
    
    <div class="panel-content">
      <slot></slot>
    </div>
    
    <!-- 调整大小的手柄 -->
    <div class="resize-handle resize-handle-se" @mousedown.stop="startResize($event, 'se')"></div>
    <div class="resize-handle resize-handle-sw" @mousedown.stop="startResize($event, 'sw')"></div>
    <div class="resize-handle resize-handle-ne" @mousedown.stop="startResize($event, 'ne')"></div>
    <div class="resize-handle resize-handle-nw" @mousedown.stop="startResize($event, 'nw')"></div>
    
    <div class="resize-handle resize-handle-n" @mousedown.stop="startResize($event, 'n')"></div>
    <div class="resize-handle resize-handle-e" @mousedown.stop="startResize($event, 'e')"></div>
    <div class="resize-handle resize-handle-s" @mousedown.stop="startResize($event, 's')"></div>
    <div class="resize-handle resize-handle-w" @mousedown.stop="startResize($event, 'w')"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

defineOptions({
  name: 'DraggablePanel'
})

const props = defineProps({
  title: {
    type: String,
    default: '标题'
  },
  subtitle: {
    type: String,
    default: 'Subtitle'
  },
  initialX: {
    type: Number,
    default: 0
  },
  initialY: {
    type: Number,
    default: 0
  },
  initialWidth: {
    type: Number,
    default: 400
  },
  initialHeight: {
    type: Number,
    default: 300
  },
  minWidth: {
    type: Number,
    default: 200
  },
  minHeight: {
    type: Number,
    default: 150
  },
  showNumber: {
    type: Boolean,
    default: false
  },
  numberCount: {
    type: Number,
    default: 0
  },
  showMore: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:position', 'update:size', 'more-click'])

// 面板大小
const width = ref(props.initialWidth)
const height = ref(props.initialHeight)

// 面板位置
const position = ref({
  x: props.initialX,
  y: props.initialY
})

// 拖拽状态
const isDragging = ref(false)
const isResizing = ref(false)
const resizeDirection = ref('')

// 鼠标拖拽起始点
const dragStart = ref({ x: 0, y: 0 })
const initialPosition = ref({ x: 0, y: 0 })
const initialSize = ref({ width: 0, height: 0 })

// 拖拽开始
const startDrag = (event: MouseEvent) => {
  event.preventDefault()
  isDragging.value = true
  
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
  
  initialPosition.value = {
    x: position.value.x,
    y: position.value.y
  }
  
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 拖拽中
const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const dx = event.clientX - dragStart.value.x
  const dy = event.clientY - dragStart.value.y
  
  position.value = {
    x: initialPosition.value.x + dx,
    y: initialPosition.value.y + dy
  }
  
  emit('update:position', position.value)
}

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 开始调整大小
const startResize = (event: MouseEvent, direction: string) => {
  event.preventDefault()
  event.stopPropagation()
  
  isResizing.value = true
  resizeDirection.value = direction
  
  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }
  
  initialSize.value = {
    width: width.value,
    height: height.value
  }
  
  initialPosition.value = {
    x: position.value.x,
    y: position.value.y
  }
  
  document.addEventListener('mousemove', onResize)
  document.addEventListener('mouseup', stopResize)
}

// 调整大小中
const onResize = (event: MouseEvent) => {
  if (!isResizing.value) return
  
  const dx = event.clientX - dragStart.value.x
  const dy = event.clientY - dragStart.value.y
  
  const direction = resizeDirection.value
  
  let newWidth = initialSize.value.width
  let newHeight = initialSize.value.height
  let newX = initialPosition.value.x
  let newY = initialPosition.value.y
  
  // 根据拖拽方向调整大小和位置
  if (direction.includes('e')) {
    newWidth = Math.max(props.minWidth, initialSize.value.width + dx)
  }
  if (direction.includes('w')) {
    const widthChange = Math.min(initialSize.value.width - props.minWidth, dx)
    newWidth = initialSize.value.width - widthChange
    newX = initialPosition.value.x + widthChange
  }
  if (direction.includes('s')) {
    newHeight = Math.max(props.minHeight, initialSize.value.height + dy)
  }
  if (direction.includes('n')) {
    const heightChange = Math.min(initialSize.value.height - props.minHeight, dy)
    newHeight = initialSize.value.height - heightChange
    newY = initialPosition.value.y + heightChange
  }
  
  width.value = newWidth
  height.value = newHeight
  position.value = { x: newX, y: newY }
  
  emit('update:size', { width: newWidth, height: newHeight })
  emit('update:position', position.value)
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
}

// 清除事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped lang="scss">
.draggable-panel {
  position: absolute;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  cursor: move;

  &.dragging {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .panel-title {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-bottom: 1px solid #eee;
    padding: 0 16px;
    background-color: #f9f9f9;
    user-select: none;
    cursor: move;

    .icon {
      width: 4px;
      height: 22px;

      .top {
        width: 100%;
        height: 11px;
        background: #cecece;
      }

      .bottom {
        width: 100%;
        height: 11px;
        background: #3c91d6;
      }
    }

    .cn {
      display: flex;
      font-size: 16px;
      font-weight: 700;
      color: #060001;
      margin-left: 16px;

      .num {
        box-sizing: border-box;
        min-width: 22px;
        height: 22px;
        padding: 0 5px;
        border-radius: 500px;
        vertical-align: middle;
        background: #1e87f0;
        color: #fff !important;
        font-size: 12px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin-left: 8px;
      }
    }

    .eng {
      color: #b2b3b5;
      font-size: 14px;
      margin-left: 16px;
      margin-right: auto;
    }

    .more {
      color: #939293;
      font-size: 14px;
      border: 1px solid #dcdcdc;
      height: 24px;
      line-height: 22px;
      padding: 0 8px;
      position: relative;
      cursor: pointer;
      border-radius: 2px;
      transition: all .3s;
      
      &:hover {
        color: #3ca1f7;
        border-color: #3ca1f7;
      }
    }
  }

  .panel-content {
    padding: 16px;
    height: calc(100% - 50px);
    overflow: auto;
  }

  .resize-handle {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: transparent;
    z-index: 10;
    
    &.resize-handle-n {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      cursor: n-resize;
      width: 100%;
      height: 6px;
    }
    
    &.resize-handle-e {
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      cursor: e-resize;
      width: 6px;
      height: 100%;
    }
    
    &.resize-handle-s {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      cursor: s-resize;
      width: 100%;
      height: 6px;
    }
    
    &.resize-handle-w {
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      cursor: w-resize;
      width: 6px;
      height: 100%;
    }
    
    &.resize-handle-se {
      bottom: 0;
      right: 0;
      cursor: se-resize;
    }
    
    &.resize-handle-sw {
      bottom: 0;
      left: 0;
      cursor: sw-resize;
    }
    
    &.resize-handle-ne {
      top: 0;
      right: 0;
      cursor: ne-resize;
    }
    
    &.resize-handle-nw {
      top: 0;
      left: 0;
      cursor: nw-resize;
    }
  }
}
</style>
