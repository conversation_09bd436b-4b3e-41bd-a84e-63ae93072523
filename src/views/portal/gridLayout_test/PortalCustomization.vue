<template>
  <div class="portal-customization-container">
    <div class="header-actions">
      <div class="title">
        <h2>个性化门户配置</h2>
        <span class="subtitle">配置您的个性化门户，管理数据授权和展示方式</span>
      </div>
      <!-- 调试信息 -->
      <div class="debug-info" v-if="showDebugGrid">
        <div>面板数量: {{ layoutItems.length }}</div>
        <div>最近添加: {{ lastAddedItem ? lastAddedItem.name : '无' }}</div>
        <div>
          <el-button size="small" @click="resetLayout">重置布局</el-button>
          <el-button size="small" @click="addTestPanel">添加测试面板</el-button>
        </div>
      </div>
      <div class="actions">
        <el-button type="primary" :icon="Document" @click="savePortalConfig">保存配置</el-button>
        <el-button type="success" :icon="View" @click="previewPortal">预览效果</el-button>
        <el-button :icon="Back" @click="returnToPortal">返回门户</el-button>
      </div>
    </div>

    <div class="main-container">
      <div class="sidebar">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="模块管理" name="modules">
            <el-collapse v-model="expandedPanels">
              <el-collapse-item title="集团要闻" name="groupNews">
                <div class="component-list">
                  <div
                    v-for="item in newsComponents"
                    :key="item.id"
                    class="component-item"
                    draggable="true"
                    @dragstart="onDragStart(item, 'news', $event)"
                  >
                    <div class="component-icon">
                      <SvgIcon
                        :icon="item.icon"
                        :title="item.title"
                        :itemId="item.itemId"
                      />
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item title="时政新闻" name="politics">
                <div class="component-list">
                  <div
                    v-for="item in politicsComponents"
                    :key="item.id"
                    class="component-item"
                    draggable="true"
                    @dragstart="onDragStart(item, 'politics', $event)"
                  >
                    <div class="component-icon">
                      <SvgIcon
                        :icon="item.icon"
                        :title="item.title"
                        :itemId="item.itemId"
                      />
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item title="院通知" name="notifications">
                <div class="component-list">
                  <div
                    v-for="item in notificationComponents"
                    :key="item.id"
                    class="component-item"
                    draggable="true"
                    @dragstart="onDragStart(item, 'notification', $event)"
                  >
                    <div class="component-icon">
                      <SvgIcon
                        :icon="item.icon"
                        :title="item.title"
                        :itemId="item.itemId"
                      />
                    </div>
                  </div>
                </div>
              </el-collapse-item>
              <el-collapse-item title="院公告" name="announcements">
                <div class="component-list">
                  <div
                    v-for="item in announcementComponents"
                    :key="item.id"
                    class="component-item"
                    draggable="true"
                    @dragstart="onDragStart(item, 'announcement', $event)"
                  >
                    <div class="component-icon">
                      <SvgIcon
                        :icon="item.icon"
                        :title="item.title"
                        :itemId="item.itemId"
                      />
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-tab-pane>
          <el-tab-pane label="权限配置" name="permissions">
            <div class="permission-settings">
              <div class="permission-group" v-for="(group, index) in permissionGroups" :key="index">
                <h4>{{ group.name }}</h4>
                <div class="permission-items">
                  <el-checkbox-group v-model="group.selectedPermissions">
                    <el-checkbox
                      v-for="permission in group.permissions"
                      :key="permission.id"
                      :label="permission.id"
                    >
                      {{ permission.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 布局容器，允许拖放 -->
      <div
        class="layout-container"
        @dragover="onDragOver"
        @drop="onDrop">

        <!-- 调试辅助网格 -->
        <div class="debug-grid" v-if="showDebugGrid">
          <div class="grid-cell" v-for="i in 550" :key="i"></div>
        </div>

        <!-- 空状态提示 -->
        <div class="empty-layout" v-if="layoutItems.length === 0">
          <el-empty description="当前没有面板，请从左侧拖拽组件到此区域">
            <el-button type="primary" @click="addTestPanel">添加测试面板</el-button>
          </el-empty>
        </div>

        <!-- 可拖拽面板组件 -->
        <div
          v-for="item in layoutItems"
          :key="item.i"
          class="draggable-panel-wrapper">
          <div
            class="draggable-panel"
            :style="{
              width: `${panelSizes[item.i]?.width || item.w * 50}px`,
              height: `${panelSizes[item.i]?.height || item.h * 50}px`,
              transform: `translate(${panelPositions[item.i]?.x || item.x * 50}px, ${panelPositions[item.i]?.y || item.y * 50}px)`,
              zIndex: activeDragItem === item.i ? '100' : '10'
            }"
            :class="{ 'dragging': activeDragItem === item.i }"
            @mousedown="startDrag($event, item.i)"
          >
            <div class="panel-title" @mousedown.stop="startDrag($event, item.i)">
              <div class="icon">
                <div class="top"></div>
                <div class="bottom"></div>
              </div>
              <div class="cn">
                <span>{{ item.name }}</span>
              </div>
              <div class="eng">
                {{ item.itemId }}
              </div>
              <div class="panel-actions">
                <el-button
                  type="primary"
                  size="small"
                  :icon="Setting"
                  circle
                  @click.stop="configureItem(item)"
                />
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  circle
                  @click.stop="removeItem(item.i)"
                />
              </div>
            </div>

            <div class="panel-content">
              <!-- 组件内容 -->
              <div class="panel-placeholder">
                {{ item.name }} 组件内容区域
              </div>
            </div>

            <!-- 调整大小的手柄 -->
            <div
class="resize-handle resize-handle-se"
                 @mousedown.stop="startResize($event, item.i, 'se')"></div>
            <div
class="resize-handle resize-handle-sw"
                 @mousedown.stop="startResize($event, item.i, 'sw')"></div>
            <div
class="resize-handle resize-handle-ne"
                 @mousedown.stop="startResize($event, item.i, 'ne')"></div>
            <div
class="resize-handle resize-handle-nw"
                 @mousedown.stop="startResize($event, item.i, 'nw')"></div>

            <div
class="resize-handle resize-handle-n"
                 @mousedown.stop="startResize($event, item.i, 'n')"></div>
            <div
class="resize-handle resize-handle-e"
                 @mousedown.stop="startResize($event, item.i, 'e')"></div>
            <div
class="resize-handle resize-handle-s"
                 @mousedown.stop="startResize($event, item.i, 's')"></div>
            <div
class="resize-handle resize-handle-w"
                 @mousedown.stop="startResize($event, item.i, 'w')"></div>
          </div>
        </div>
      </div>

      <div class="config-panel" v-if="selectedItem">
        <div class="panel-header">
          <h3>{{ selectedItem.name }} 配置</h3>
          <el-button :icon="Close" circle size="small" @click="closeConfigPanel"/>
        </div>
        <div class="panel-content">
          <el-form label-position="top" :model="selectedItemConfig">
            <el-form-item label="标题">
              <el-input v-model="selectedItemConfig.title"/>
            </el-form-item>
            <el-form-item label="显示条数">
              <el-input-number v-model="selectedItemConfig.displayCount" :min="1" :max="20"/>
            </el-form-item>
            <el-form-item label="刷新频率">
              <el-select v-model="selectedItemConfig.refreshInterval" placeholder="选择刷新频率">
                <el-option label="不自动刷新" value="0"/>
                <el-option label="1分钟" value="60"/>
                <el-option label="5分钟" value="300"/>
                <el-option label="15分钟" value="900"/>
                <el-option label="30分钟" value="1800"/>
                <el-option label="1小时" value="3600"/>
              </el-select>
            </el-form-item>
            <el-form-item label="数据权限">
              <div class="single-transfer-panel">
                <div class="panel-header">可选权限</div>
                <el-checkbox-group v-model="selectedItemConfig.dataPermissions">
                  <div class="checkbox-list">
                    <el-checkbox
                      v-for="item in availablePermissions"
                      :key="item.key"
                      :label="item.key">
                      {{ item.label }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveItemConfig">保存配置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="previewVisible"
      title="门户预览"
      fullscreen
      :show-close="false"
      :modal="true"
      @opened="handlePreviewDialogOpen">
      <div class="preview-container">
        <div class="preview-header">
          <div class="title">门户预览</div>
          <div class="close-btn" @click="previewVisible = false">关闭预览</div>
        </div>
        <div class="preview-content">
          <div v-if="layoutItems.length === 0" class="empty-preview">
            <el-empty description="暂无预览内容">
              <!-- Using Element Plus's built-in empty image -->
            </el-empty>
          </div>
          <div v-else class="cost-optimization-dashboard">
            <div class="dashboard-header">
              <h2>成本优化</h2>
            </div>

            <div class="tab-container">
              <div class="tabs">
                <div class="tab">成本节约建议生成</div>
                <div class="tab active">规则动态优化建议</div>
              </div>

              <div class="sub-tabs">
                <div class="sub-tab">配置优化规则库</div>
                <div class="sub-tab">规则触发通知</div>
                <div class="sub-tab active">规则执行追踪</div>
              </div>

              <div class="dashboard-content">
                <div class="tracking-header">
                  <div class="title">规则执行追踪</div>
                  <div class="filters">
                    <el-select placeholder="规则分类" size="default">
                      <el-option label="全部规则" value="all"/>
                    </el-select>
                    <div class="date-range">
                      <el-date-picker type="date" placeholder="开始日期" size="default"/>
                      <span class="separator">至</span>
                      <el-date-picker type="date" placeholder="结束日期" size="default"/>
                    </div>
                  </div>
                </div>

                <div class="stats-cards">
                  <div class="stat-card">
                    <div class="value blue">{{ chartData.stats.totalRules }}</div>
                    <div class="label">规则触发总次数</div>
                  </div>
                  <div class="stat-card">
                    <div class="value orange">{{ chartData.stats.violationCount }}</div>
                    <div class="label">违规总次数</div>
                  </div>
                  <div class="stat-card">
                    <div class="value green">{{ chartData.stats.correctionRate }}%</div>
                    <div class="label">整体纠正率</div>
                  </div>
                </div>

                <div class="charts-container">
                  <div class="chart-box">
                    <div class="chart-title">规则触发频率</div>
                    <div ref="ruleFrequencyChart" class="chart"></div>
                  </div>
                  <div class="chart-box">
                    <div class="chart-title">纠正率趋势</div>
                    <div ref="correctionRateChart" class="chart"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, reactive, onUnmounted, markRaw} from 'vue'
import {Delete, Setting, View, Document, Back, Close} from '@element-plus/icons-vue'
import SvgIcon from './SvgIcon.vue'
import {savePortalLayout, getPortalConfig} from './PortalConfigAPI'
import {customList} from '@/utils/custom'
import {useRouter} from 'vue-router'
import {useMessage} from '@/hooks/web/useMessage'
// 引入图表库
import * as echarts from 'echarts/core'
import { BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer
])

defineOptions({
  name: 'PortalCustomization'
})

// 定义接口
interface LayoutItem {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  name: string;
  itemId: string;
  category: string;
  itemJson: {
    title: string;
    displayCount: number;
    refreshInterval: string;
    dataPermissions: string[];
  };
}

interface ComponentItem {
  id: string;
  itemId: string;
  title: string;
  icon: string;
  defaultW: number;
  defaultH: number;
}

interface PanelPosition {
  x: number;
  y: number;
}

interface PanelSize {
  width: number;
  height: number;
}

const router = useRouter()
const message = useMessage()

// 状态变量
const activeTab = ref('modules')
const expandedPanels = ref(['groupNews', 'politics', 'notifications', 'announcements'])
const layoutItems = ref<LayoutItem[]>([])
const selectedItem = ref<LayoutItem | null>(null)
const selectedItemConfig = reactive({
  title: '',
  displayCount: 10,
  refreshInterval: '0',
  dataPermissions: [] as string[]
})
const previewVisible = ref(false)

// 拖拽相关状态
const activeDragItem = ref<string | null>(null)
const isResizing = ref(false)
const resizeDirection = ref('')
const resizingItem = ref<string | null>(null)
const dragStart = ref({x: 0, y: 0})
const initialPosition = ref({x: 0, y: 0})
const initialSize = ref({width: 0, height: 0})
const panelPositions = ref<Record<string, PanelPosition>>({})
const panelSizes = ref<Record<string, PanelSize>>({})

// 调试选项
const showDebugGrid = ref(true) // 显示调试网格
const lastAddedItem = ref<LayoutItem | null>(null)

// 图表相关数据
const chartData = reactive({
  // 规则触发频率数据
  ruleFrequency: {
    months: ['1月', '2月', '3月', '4月', '5月', '6月'],
    values: [30, 42, 51, 39, 70, 52]
  },
  // 纠正率趋势数据
  correctionRate: {
    months: ['1月', '2月', '3月', '4月', '5月', '6月'],
    values: [65, 59, 80, 81, 56, 75]
  },
  // 统计数据
  stats: {
    totalRules: 152,
    violationCount: 38,
    correctionRate: 75
  }
})

// 图表实例引用
const ruleFrequencyChart = ref<HTMLElement | null>(null)
const correctionRateChart = ref<HTMLElement | null>(null)
let ruleFrequencyChartInstance: echarts.ECharts | null = null
let correctionRateChartInstance: echarts.ECharts | null = null

// 初始化图表
const initCharts = () => {
  if (previewVisible.value) {
    // 规则触发频率柱状图
    if (ruleFrequencyChart.value) {
      ruleFrequencyChartInstance = echarts.init(ruleFrequencyChart.value)
      ruleFrequencyChartInstance.setOption({
        title: {
          text: '规则触发频率柱状图',
          left: 'center',
          textStyle: {
            color: '#606266',
            fontWeight: 'normal',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: chartData.ruleFrequency.months,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '触发次数',
            type: 'bar',
            barWidth: '60%',
            data: chartData.ruleFrequency.values,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      })
    }

    // 纠正率趋势折线图
    if (correctionRateChart.value) {
      correctionRateChartInstance = echarts.init(correctionRateChart.value)
      correctionRateChartInstance.setOption({
        title: {
          text: '纠正率趋势折线图',
          left: 'center',
          textStyle: {
            color: '#606266',
            fontWeight: 'normal',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: chartData.correctionRate.months,
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
          },
          max: 100
        },
        series: [
          {
            name: '纠正率',
            type: 'line',
            data: chartData.correctionRate.values,
            itemStyle: {
              color: '#67C23A'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(103, 194, 58, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(103, 194, 58, 0.1)'
                  }
                ]
              }
            },
            smooth: true
          }
        ]
      })
    }
  }
}

// 监听预览对话框显示状态
const handlePreviewDialogOpen = () => {
  // 延迟执行以确保DOM已渲染
  setTimeout(() => {
    initCharts()
  }, 100)
}

// 监听窗口大小变化，重绘图表
const handleResize = () => {
  if (ruleFrequencyChartInstance) {
    ruleFrequencyChartInstance.resize()
  }
  if (correctionRateChartInstance) {
    correctionRateChartInstance.resize()
  }
}

// 组件列表
const newsComponents = ref<ComponentItem[]>([
  {id: 'news1', itemId: 'groupNews', title: '集团要闻', icon: 'news', defaultW: 12, defaultH: 8},
  {id: 'news2', itemId: 'hotNews', title: '热点新闻', icon: 'hot', defaultW: 12, defaultH: 6},
  {
    id: 'news3',
    itemId: 'newsCarousel',
    title: '新闻轮播',
    icon: 'carousel',
    defaultW: 8,
    defaultH: 6
  }
])

const politicsComponents = ref<ComponentItem[]>([
  {
    id: 'politics1',
    itemId: 'politicsNews',
    title: '时政要闻',
    icon: 'politics',
    defaultW: 12,
    defaultH: 8
  },
  {
    id: 'politics2',
    itemId: 'policyUpdates',
    title: '政策更新',
    icon: 'policy',
    defaultW: 8,
    defaultH: 6
  }
])

const notificationComponents = ref<ComponentItem[]>([
  {
    id: 'notification1',
    itemId: 'officeNotice',
    title: '办公通知',
    icon: 'notice',
    defaultW: 8,
    defaultH: 6
  },
  {
    id: 'notification2',
    itemId: 'importantNotice',
    title: '重要通知',
    icon: 'important',
    defaultW: 8,
    defaultH: 6
  }
])

const announcementComponents = ref<ComponentItem[]>([
  {
    id: 'announcement1',
    itemId: 'publicAnnouncement',
    title: '公共公告',
    icon: 'announcement',
    defaultW: 12,
    defaultH: 6
  },
  {
    id: 'announcement2',
    itemId: 'tenderAnnouncement',
    title: '招标公告',
    icon: 'tender',
    defaultW: 8,
    defaultH: 6
  }
])

// 权限配置
const permissionGroups = ref([
  {
    name: '新闻权限',
    selectedPermissions: [] as string[],
    permissions: [
      {id: 'news:view:all', name: '查看所有新闻'},
      {id: 'news:view:dept', name: '查看部门新闻'},
      {id: 'news:manage', name: '管理新闻'}
    ]
  },
  {
    name: '通知权限',
    selectedPermissions: [] as string[],
    permissions: [
      {id: 'notification:view:all', name: '查看所有通知'},
      {id: 'notification:view:dept', name: '查看部门通知'},
      {id: 'notification:manage', name: '管理通知'}
    ]
  },
  {
    name: '公告权限',
    selectedPermissions: [] as string[],
    permissions: [
      {id: 'announcement:view:all', name: '查看所有公告'},
      {id: 'announcement:view:dept', name: '查看部门公告'},
      {id: 'announcement:manage', name: '管理公告'}
    ]
  }
])

// 模拟权限列表
const availablePermissions = ref([
  {key: 'news:view:all', label: '查看所有新闻'},
  {key: 'news:view:dept', label: '查看部门新闻'},
  {key: 'news:manage', label: '管理新闻'},
  {key: 'notification:view:all', label: '查看所有通知'},
  {key: 'notification:view:dept', label: '查看部门通知'},
  {key: 'notification:manage', label: '管理通知'},
  {key: 'announcement:view:all', label: '查看所有公告'},
  {key: 'announcement:view:dept', label: '查看部门公告'},
  {key: 'announcement:manage', label: '管理公告'}
])

// 拖拽处理
const onDragStart = (item: ComponentItem, category: string, e: DragEvent) => {
  if (!e.dataTransfer) return

  const transferData = {
    item: item,
    category: category
  }

  // 确保数据正确传输 - 使用纯文本和JSON双重保障
  const jsonData = JSON.stringify(transferData)
  e.dataTransfer.setData('text/plain', jsonData)
  e.dataTransfer.setData('application/json', jsonData)

  // 设置拖拽效果
  e.dataTransfer.effectAllowed = 'copy'

  console.log('开始拖拽组件:', item.title)
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()
  if (!event.dataTransfer) return

  try {
    // 尝试从多种格式获取数据
    let jsonData = event.dataTransfer.getData('application/json')
    if (!jsonData || jsonData === '') {
      jsonData = event.dataTransfer.getData('text/plain')
    }

    console.log('接收到的拖拽数据:', jsonData)

    if (!jsonData || jsonData === '') {
      console.error('没有接收到拖拽数据')
      return
    }

    const data = JSON.parse(jsonData)
    const item = data.item
    const category = data.category

    if (!item || !category) {
      console.error('拖拽数据不完整', data)
      return
    }

    // 获取拖放位置
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    console.log('拖放位置:', {x, y})
    console.log('拖放组件:', item)

    // 创建新的布局项
    const newItemId = `${item.itemId}_${Date.now()}`
    const newItem: LayoutItem = {
      i: newItemId,
      x: Math.floor(x / 50),  // 转换为网格单位
      y: Math.floor(y / 50),  // 转换为网格单位
      w: item.defaultW || 8,
      h: item.defaultH || 6,
      name: item.title,
      itemId: item.itemId,
      category: category,
      itemJson: {
        title: item.title,
        displayCount: 10,
        refreshInterval: '0',
        dataPermissions: []
      }
    }

    // 设置实际位置
    panelPositions.value[newItemId] = {
      x: Math.max(0, x - (item.defaultW * 25)),  // 以鼠标为中心点，但至少从0开始
      y: Math.max(0, y - 25)  // 头部高度偏移，但至少从0开始
    }

    // 设置初始大小
    panelSizes.value[newItemId] = {
      width: item.defaultW * 50,
      height: item.defaultH * 50
    }

    layoutItems.value.push(newItem)
    lastAddedItem.value = newItem
    console.log('已添加新组件:', newItem)
    console.log('当前所有组件:', layoutItems.value)
    message.success(`添加组件: ${item.title}`)
  } catch (err) {
    console.error('解析拖拽数据错误:', err)
  }
}

// 面板拖拽功能
const startDrag = (event: MouseEvent, itemId: string) => {
  event.preventDefault()
  activeDragItem.value = itemId

  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }

  // 初始化或使用现有位置
  if (!panelPositions.value[itemId]) {
    const item = layoutItems.value.find(item => item.i === itemId)
    if (item) {
      panelPositions.value[itemId] = {
        x: item.x * 50,
        y: item.y * 50
      }
    }
  }

  initialPosition.value = {...panelPositions.value[itemId]}

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const onDrag = (event: MouseEvent) => {
  if (!activeDragItem.value) return

  const dx = event.clientX - dragStart.value.x
  const dy = event.clientY - dragStart.value.y

  panelPositions.value[activeDragItem.value] = {
    x: initialPosition.value.x + dx,
    y: initialPosition.value.y + dy
  }

  // 更新布局项位置（网格单位）
  const index = layoutItems.value.findIndex(item => item.i === activeDragItem.value)
  if (index !== -1) {
    layoutItems.value[index].x = Math.floor(panelPositions.value[activeDragItem.value].x / 50)
    layoutItems.value[index].y = Math.floor(panelPositions.value[activeDragItem.value].y / 50)
  }
}

const stopDrag = () => {
  activeDragItem.value = null
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 调整大小功能
const startResize = (event: MouseEvent, itemId: string, direction: string) => {
  event.preventDefault()
  event.stopPropagation()

  isResizing.value = true
  resizingItem.value = itemId
  resizeDirection.value = direction

  dragStart.value = {
    x: event.clientX,
    y: event.clientY
  }

  // 初始化或使用现有大小
  if (!panelSizes.value[itemId]) {
    const item = layoutItems.value.find(item => item.i === itemId)
    if (item) {
      panelSizes.value[itemId] = {
        width: item.w * 50,
        height: item.h * 50
      }
    }
  }

  initialSize.value = {...panelSizes.value[itemId]}

  // 初始化或使用现有位置
  if (!panelPositions.value[itemId]) {
    const item = layoutItems.value.find(item => item.i === itemId)
    if (item) {
      panelPositions.value[itemId] = {
        x: item.x * 50,
        y: item.y * 50
      }
    }
  }

  initialPosition.value = {...panelPositions.value[itemId]}

  document.addEventListener('mousemove', onResize)
  document.addEventListener('mouseup', stopResize)
}

const onResize = (event: MouseEvent) => {
  if (!isResizing.value || !resizingItem.value) return

  const dx = event.clientX - dragStart.value.x
  const dy = event.clientY - dragStart.value.y

  const direction = resizeDirection.value
  const itemId = resizingItem.value

  const minWidth = 200
  const minHeight = 150

  let newWidth = initialSize.value.width
  let newHeight = initialSize.value.height
  let newX = initialPosition.value.x
  let newY = initialPosition.value.y

  // 根据拖拽方向调整大小和位置
  if (direction.includes('e')) {
    newWidth = Math.max(minWidth, initialSize.value.width + dx)
  }
  if (direction.includes('w')) {
    const widthChange = Math.min(initialSize.value.width - minWidth, dx)
    newWidth = initialSize.value.width - widthChange
    newX = initialPosition.value.x + widthChange
  }
  if (direction.includes('s')) {
    newHeight = Math.max(minHeight, initialSize.value.height + dy)
  }
  if (direction.includes('n')) {
    const heightChange = Math.min(initialSize.value.height - minHeight, dy)
    newHeight = initialSize.value.height - heightChange
    newY = initialPosition.value.y + heightChange
  }

  panelSizes.value[itemId] = {
    width: newWidth,
    height: newHeight
  }

  panelPositions.value[itemId] = {
    x: newX,
    y: newY
  }

  // 更新布局项大小和位置（网格单位）
  const index = layoutItems.value.findIndex(item => item.i === itemId)
  if (index !== -1) {
    layoutItems.value[index].w = Math.ceil(newWidth / 50)
    layoutItems.value[index].h = Math.ceil(newHeight / 50)
    layoutItems.value[index].x = Math.floor(newX / 50)
    layoutItems.value[index].y = Math.floor(newY / 50)
  }
}

const stopResize = () => {
  isResizing.value = false
  resizingItem.value = null
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
}

// 布局更新处理
const onLayoutUpdated = (newLayout: LayoutItem[]) => {
  // 避免不必要的大量计算
  console.log('Layout updated')
}

// 组件操作
const removeItem = (itemId: string) => {
  layoutItems.value = layoutItems.value.filter(item => item.i !== itemId)
  if (selectedItem.value && selectedItem.value.i === itemId) {
    selectedItem.value = null
  }

  // 删除对应的位置和大小记录
  delete panelPositions.value[itemId]
  delete panelSizes.value[itemId]
}

const configureItem = (item: LayoutItem) => {
  selectedItem.value = item
  if (item && item.itemJson) {
    selectedItemConfig.title = item.itemJson.title || item.name
    selectedItemConfig.displayCount = item.itemJson.displayCount || 10
    selectedItemConfig.refreshInterval = item.itemJson.refreshInterval || '0'
    selectedItemConfig.dataPermissions = item.itemJson.dataPermissions || []
  }
}

const closeConfigPanel = () => {
  selectedItem.value = null
}

const saveItemConfig = () => {
  if (!selectedItem.value) return

  // 更新选中项的配置
  const index = layoutItems.value.findIndex(item => item.i === selectedItem.value?.i)
  if (index !== -1) {
    layoutItems.value[index].itemJson = {
      ...layoutItems.value[index].itemJson,
      title: selectedItemConfig.title,
      displayCount: selectedItemConfig.displayCount,
      refreshInterval: selectedItemConfig.refreshInterval,
      dataPermissions: selectedItemConfig.dataPermissions
    }
    layoutItems.value[index].name = selectedItemConfig.title
    message.success('组件配置已更新')
  }
}

// 获取组件
const getComponentById = (itemId: string) => {
  const item = customList.value?.find(item => item.name?.name === itemId)
  return item?.name || 'div'
}

// 保存门户配置
const savePortalConfig = async () => {
  try {
    // 确保配置项中保存了面板的实际位置和大小
    layoutItems.value.forEach(item => {
      if (panelPositions.value[item.i]) {
        item.x = Math.floor(panelPositions.value[item.i].x / 50)
        item.y = Math.floor(panelPositions.value[item.i].y / 50)
      }

      if (panelSizes.value[item.i]) {
        item.w = Math.ceil(panelSizes.value[item.i].width / 50)
        item.h = Math.ceil(panelSizes.value[item.i].height / 50)
      }
    })

    await savePortalLayout({
      layouts: layoutItems.value,
      permissions: permissionGroups.value.reduce((acc: string[], group) => {
        return [...acc, ...group.selectedPermissions]
      }, [])
    })
    message.success('门户配置保存成功')
  } catch (error: any) {
    message.error('保存失败: ' + (error.message || '未知错误'))
  }
}

// 预览门户
const previewPortal = () => {
  // 确保所有组件的位置和大小都是最新的
  layoutItems.value.forEach(item => {
    if (panelPositions.value[item.i]) {
      item.x = Math.floor(panelPositions.value[item.i].x / 50)
      item.y = Math.floor(panelPositions.value[item.i].y / 50)
    }

    if (panelSizes.value[item.i]) {
      item.w = Math.ceil(panelSizes.value[item.i].width / 50)
      item.h = Math.ceil(panelSizes.value[item.i].height / 50)
    }
  })

  // 显示预览对话框
  previewVisible.value = true
}

// 返回门户
const returnToPortal = () => {
  router.push('/portal')
}

// 加载已有配置
const loadPortalConfig = async () => {
  try {
    const config = await getPortalConfig()
    if (config.layouts) {
      // 确保数据结构符合预期
      layoutItems.value = Array.isArray(config.layouts) ? config.layouts : []

      // 初始化面板位置和大小
      layoutItems.value.forEach(item => {
        panelPositions.value[item.i] = {
          x: item.x * 50,
          y: item.y * 50
        }

        panelSizes.value[item.i] = {
          width: item.w * 50,
          height: item.h * 50
        }
      })
    }
    if (config.permissions && Array.isArray(config.permissions)) {
      // 设置权限组
      permissionGroups.value.forEach(group => {
        const prefix = group.name.split('权限')[0].toLowerCase()
        group.selectedPermissions = config.permissions.filter((p: string) =>
          p.startsWith(prefix)
        )
      })
    }
  } catch (error: any) {
    console.error('加载门户配置失败:', error)
  }
}

// 重置布局
const resetLayout = () => {
  layoutItems.value = []
  panelPositions.value = {}
  panelSizes.value = {}
  message.success('布局已重置')
}

// 添加测试面板
const addTestPanel = () => {
  const newItemId = `test_${Date.now()}`
  const newItem: LayoutItem = {
    i: newItemId,
    x: 2,
    y: 2,
    w: 8,
    h: 6,
    name: '测试面板',
    itemId: 'testPanel',
    category: 'test',
    itemJson: {
      title: '测试面板',
      displayCount: 10,
      refreshInterval: '0',
      dataPermissions: []
    }
  }

  // 设置位置和大小
  panelPositions.value[newItemId] = {
    x: 100,
    y: 100
  }

  panelSizes.value[newItemId] = {
    width: 400,
    height: 300
  }

  layoutItems.value.push(newItem)
  lastAddedItem.value = newItem
  message.success('已添加测试面板')
}

// 清除事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  if (ruleFrequencyChartInstance) {
    ruleFrequencyChartInstance.dispose()
  }
  if (correctionRateChartInstance) {
    correctionRateChartInstance.dispose()
  }
})

onMounted(() => {
  // 使用setTimeout避免立即加载导致的性能问题
  setTimeout(() => {
    loadPortalConfig()
  }, 100)

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.portal-customization-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .header-actions {
    padding: 15px 20px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .title {
      h2 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .subtitle {
        color: #909399;
        font-size: 14px;
      }
    }

    .debug-info {
      font-size: 12px;
      display: flex;
      gap: 10px;
      align-items: center;
      > div {
        margin-bottom: 5px;
      }
    }

    .actions {
      display: flex;
      gap: 10px;
    }

  }

  .main-container {
    display: flex;
    flex: 1;
    padding: 20px;
    gap: 10px;
    height: calc(100vh - 100px);
    overflow: hidden;

    .sidebar {
      width: 280px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      overflow-y: auto;

      .component-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 10px;

        .component-item {
          cursor: grab;
          padding: 10px;
          border-radius: 4px;
          background-color: #f5f7fa;
          width: calc(50% - 5px);
          display: flex;
          flex-direction: column;
          align-items: center;

          &:hover {
            background-color: #e6f1fc;
          }
        }
      }

      .permission-settings {
        padding: 15px;

        .permission-group {
          margin-bottom: 20px;

          h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #303133;
          }

          .permission-items {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }

    .layout-container {
      width: 1252px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      position: relative;
      overflow: auto;
      height: 100%;
      min-height: 1080px;

      // 调试网格
      .debug-grid {
        position: absolute;
        top: 0;
        left: 0;
        width: 1252px;
        height: 100%;
        display: grid;
        grid-template-columns: repeat(25, 50px);
        grid-template-rows: repeat(20, 50px);
        pointer-events: none;

        .grid-cell {
          border: 1px dashed rgba(0, 0, 0, 0.1);
        }
      }

      // 空状态
      .empty-layout {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
      }

      // 可拖拽面板样式
      .draggable-panel-wrapper {
        position: relative;
        width: 0;
        height: 0;
        overflow: visible;
      }

      .draggable-panel {
        position: absolute;
        background-color: #fff;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: box-shadow 0.3s ease;
        cursor: move;
        z-index: 10;

        &.dragging {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          z-index: 100;
        }

        .panel-title {
          width: calc(100% - 32px);
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          border-bottom: 1px solid #eee;
          padding: 0 16px;
          background-color: #f9f9f9;
          user-select: none;
          cursor: move;

          .icon {
            width: 4px;
            height: 22px;

            .top {
              width: 100%;
              height: 11px;
              background: #cecece;
            }

            .bottom {
              width: 100%;
              height: 11px;
              background: #3c91d6;
            }
          }

          .cn {
            display: flex;
            font-size: 16px;
            font-weight: 700;
            color: #060001;
            margin-left: 16px;

            .num {
              box-sizing: border-box;
              min-width: 22px;
              height: 22px;
              padding: 0 5px;
              border-radius: 500px;
              vertical-align: middle;
              background: #1e87f0;
              color: #fff !important;
              font-size: 12px;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              margin-left: 8px;
            }
          }

          .eng {
            color: #b2b3b5;
            font-size: 14px;
            margin-left: 16px;
            margin-right: auto;
          }

          .panel-actions {
            display: flex;
            gap: 8px;
          }
        }

        .panel-content {
          padding: 16px;
          height: calc(100% - 50px);
          overflow: auto;

          .panel-placeholder {
            color: #909399;
            text-align: center;
            padding: 20px 0;
          }
        }

        .resize-handle {
          position: absolute;
          width: 10px;
          height: 10px;
          background-color: transparent;
          z-index: 10;

          &.resize-handle-n {
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            cursor: n-resize;
            width: 100%;
            height: 6px;
          }

          &.resize-handle-e {
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            cursor: e-resize;
            width: 6px;
            height: 100%;
          }

          &.resize-handle-s {
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            cursor: s-resize;
            width: 100%;
            height: 6px;
          }

          &.resize-handle-w {
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            cursor: w-resize;
            width: 6px;
            height: 100%;
          }

          &.resize-handle-se {
            bottom: 0;
            right: 0;
            cursor: se-resize;
          }

          &.resize-handle-sw {
            bottom: 0;
            left: 0;
            cursor: sw-resize;
          }

          &.resize-handle-ne {
            top: 0;
            right: 0;
            cursor: ne-resize;
          }

          &.resize-handle-nw {
            top: 0;
            left: 0;
            cursor: nw-resize;
          }
        }
      }
    }

    .config-panel {
      width: 320px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      overflow-y: auto;

      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 15px;
        border-bottom: 1px solid #ebeef5;

        h3 {
          margin: 0;
          color: #303133;
        }
      }

      .panel-content {
        padding: 15px;
      }
    }
  }

  .preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      height: 56px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fff;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .close-btn {
        cursor: pointer;
        font-size: 14px;
        color: #606266;

        &:hover {
          color: #409EFF;
        }
      }
    }

    .preview-content {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f5f7fa;
      position: relative;
      overflow: auto;

      .empty-preview {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        :deep(.el-empty__image) {
          width: 160px;
          height: 160px;
        }

        :deep(.el-empty__description) {
          margin-top: 20px;
          color: #909399;
        }
      }

      .cost-optimization-dashboard {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .dashboard-header {
          padding: 20px;
          background-color: #fff;
          border-bottom: 1px solid #ebeef5;

          h2 {
            margin: 0;
            font-size: 20px;
            color: #303133;
          }
        }

        .tab-container {
          display: flex;
          justify-content: space-between;
          padding: 20px;
          background-color: #fff;

          .tabs {
            display: flex;
            gap: 20px;

            .tab {
              cursor: pointer;
              font-size: 16px;
              font-weight: 500;
              color: #606266;
            }

            .active {
              color: #409EFF;
            }
          }
        }

        .sub-tabs {
          display: flex;
          gap: 20px;
          padding: 20px;
          background-color: #fff;

          .sub-tab {
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #606266;
          }

          .active {
            color: #409EFF;
          }
        }

        .dashboard-content {
          flex: 1;
          padding: 20px;
          background-color: #fff;

          .tracking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: #fff;
            border-bottom: 1px solid #ebeef5;

            .title {
              font-size: 16px;
              font-weight: 500;
              color: #303133;
            }

            .filters {
              display: flex;
              gap: 20px;

              .el-select {
                width: 200px;
              }

              .date-range {
                display: flex;
                align-items: center;
                gap: 10px;

                .el-date-picker {
                  width: 150px;
                }
              }
            }
          }

          .stats-cards {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background-color: #fff;

            .stat-card {
              text-align: center;

              .value {
                font-size: 24px;
                font-weight: 700;
                color: #303133;

                &.blue {
                  color: #409EFF;
                }

                &.orange {
                  color: #E6A23C;
                }

                &.green {
                  color: #67C23A;
                }
              }

              .label {
                font-size: 14px;
                color: #909399;
              }
            }
          }

          .charts-container {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            background-color: #fff;

            .chart-box {
              width: 48%;
              height: 300px;
              background-color: #fff;
              border-radius: 4px;
              box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
              overflow: hidden;

              .chart-title {
                padding: 12px 16px;
                background-color: #f9f9f9;
                border-bottom: 1px solid #ebeef5;
                font-size: 16px;
                font-weight: 500;
                color: #303133;
              }

              .chart {
                width: 100%;
                height: calc(100% - 50px);
              }
            }
          }
        }
      }
    }
  }
}
:deep(.el-tabs){
  padding-left: 10px;
}

.single-transfer-panel {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  width: 100%;

  .panel-header {
    background-color: #f5f7fa;
    padding: 4px 12px!important;
    border-bottom: 1px solid #dcdfe6;
    font-weight: 500;
    color: #606266;
  }

  .checkbox-list {
    height: 240px;
    padding: 10px;
    overflow-y: auto;

    .el-checkbox {
      display: block;
      margin-right: 0;
      margin-bottom: 8px;
    }
  }
}
</style>
