<template>
  <div class="portal_page" style="height: 700px; overflow-y: hidden;">
    <div class="page-work" id="WorkCenter">
      <div class="uk-container uk-container-large uk-container-center">
        <div class="uk-grid uk-grid-medium clearfix">
          <div class="siderbar">
            <ul>
              <li class="p_item" v-for="(dm, didx) in YHZXList" :key="didx">
                <a :class="dm.isOpen ? 'open m_a expend' : 'm_a expend'" href="#" @click="menu_click(didx)">
                  <span class="text">{{ dm.NavName }}</span>
                </a>
                <ul class="ul_sub ul_sub_sort" :style="{ height: dm.isOpen ? '40px' : 0 }">
                  <li>
                    <a target="_blank" style="font-size: 15px;" v-text="dm.ColName" @click="handelhref(dm.URL, dm)"></a>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
          <div class="iframe-container">
            <iframe
              v-if="currentIframeUrl"
              :src="currentIframeUrl"
              frameborder="0"
              width="100%"
              height="100%"
              class="content-iframe">
            </iframe>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as portal from '@/api/portal'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'
const { push } = useRouter();

const useStore = useAppStore();
const userStore = useUserStore();
const YHZXList = ref([])
const token = ref('');
const baseUrl = ref('');
const currentIframeUrl = ref('');

onMounted(() => {
  loadUserCenter();
  token.value = getAccessToken();
  baseUrl.value = getCurrentInstance()?.appContext.config.globalProperties.$baseUrl
  initView()
})

//左侧菜单
const menu_click = (index) => {
  // 切换当前菜单项的展开状态
  YHZXList.value[index].isOpen = !YHZXList.value[index].isOpen;

  // 关闭其他菜单项
  YHZXList.value.forEach((item, idx) => {
    if (idx !== index) {
      item.isOpen = false;
    }
  });
}

const handelhref = (url, item) => {
  let toUrl
  const urlPrefix = 'http://***********:8080'

  // 处理基本信息菜单项
  if (item && (item.ColName === '基本信息')) {
    // 基本信息使用指定的链接格式，替换{UserID}为当前用户ID
    toUrl = urlPrefix + '/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ad6600b0158e44dba21b2031505e54c4&ID=' + userStore.getUser.id
  } else if (url && url.trim()) {
    // 其他菜单项处理
    if (url.includes('http://') || url.includes('https://')) {
      // 如果URL已经包含协议，直接使用
      toUrl = url
    } else {
      // 如果URL不包含协议，添加前缀
      toUrl = urlPrefix + url
    }
  } else {
    // 如果没有URL，使用基本信息的默认链接
    toUrl = urlPrefix + '/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ad6600b0158e44dba21b2031505e54c4&ID=' + userStore.getUser.id
  }

  // 添加token参数
  if (toUrl) {
    if (toUrl.includes('?')) {
      toUrl = toUrl + '&token=' + token.value
    } else {
      toUrl = toUrl + '?token=' + token.value
    }

    // 替换多余的斜杠
    toUrl = toUrl.replace(/([^:]\/)\/+/g, '$1')

    // 设置iframe的URL
    currentIframeUrl.value = toUrl
  }
}
// 初始化界面
const initView = () => {
  const urlPrefix = 'http://***********:8080'
  // 默认加载基本信息页面
  let defaultUrl = urlPrefix + '/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ad6600b0158e44dba21b2031505e54c4&ID=' + userStore.getUser.id

  // 添加token参数
  if (defaultUrl.includes('?')) {
    defaultUrl = defaultUrl + '&token=' + token.value
  } else {
    defaultUrl = defaultUrl + '?token=' + token.value
  }

  // 替换多余的斜杠
  defaultUrl = defaultUrl.replace(/([^:]\/)\/+/g, '$1')

  // 设置默认iframe URL
  currentIframeUrl.value = defaultUrl
}
//左侧导航
const loadUserCenter = () => {
  portal.yhzx_list({}).then((ret) => {
    console.log("个人中心菜单===========>",ret)
    // 为每个菜单项添加isOpen属性，默认为false（折叠状态）
    YHZXList.value = ret.map(item => ({
      ...item,
      isOpen: false
    }));
  });
}
</script>

<style lang="scss" scoped>
@import url('../../assets/css/uk.min.css');
@import url('../../assets/css/master.css');
@import url('../../assets/css/bootstrap.min.css');
@import url('../../assets/css/workcenter.css');

.db_list {
  width: 1104px;
  background: #fff;
  min-height: 700px;
  margin-left: auto;
}

.iframe-container {
  width: calc(100% - 260px);
  height: 700px;
  float: right;
  background: #fff;
}

.content-iframe {
  border: none;
  width: 100%;
  height: 100%;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.siderbar {
  position: relative;
  width: 260px;
  float: left;
  max-height: 60%;
  min-height: 560px;
  border-radius: 3px;
  background: #fff;
  color: #666;
  box-shadow: 0 5px 15px rgba(0, 0, 0, .08);
  padding: 40px 0;

  ul {
    margin: 0 !important;
    padding: 0 !important;
  }

  .p_item {
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-bottom: 8px;

    .m_a {
      display: inline-block;
      color: black;
      font-weight: 600;
      text-decoration: none;
      display: flex;
      align-items: center;
      height: 36px;
      padding: 8px 0;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &.expend::after {
        background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Cpolyline%20fill%3D%22none%22%20stroke%3D%22%23666%22%20stroke-width%3D%221.1%22%20points%3D%2210%201%204%207%2010%2013%22%20%2F%3E%0A%3C%2Fsvg%3E);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        transform: rotate(180deg);
        transition: transform 0.4s ease-out;
        width: 1.5em;
        height: 1.5em;
        content: "";
        margin-left: 65px;
      }

      &.open::after {
        transform: rotate(90deg);
      }

      .icon {
        margin-left: 40px;
      }

      .text {
        font-size: 15px;
        margin-left: 50px;
        color: #000;
      }
    }

    .ul_sub {
      transition: height 0.3s ease-out;
      overflow: hidden;
      margin-left: -30px;
      display: flex;
      flex-direction: column;
      margin: 0 !important;
      padding: 0 !important;

      a {
        height: 32px;
        line-height: 32px;
        width: 100%;
        display: inline-block;
        color: #999;
        font-weight: normal;
        padding-left: 64px;
        text-decoration: none;
        cursor: pointer;
        border-left: 2px solid #fff;
        font-size: 14px;

        &:hover {
          color: #333;
          background-color: #f8f9fa;
        }

        &.actived {
          border-left: 2px solid #0070ff;
          background-color: #f4faff;
          color: #0070ff;
          font-weight: 600;
        }
      }
    }
  }
}

.work-top .uk-card .uk-card-header div:first-child>.active {
  // background: url("@/assets/icons/portal/index_icon_yfw.png") no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div:nth-child(2)>.active {
  // background: url("@/assets/icons/portal/index_icon_ytz.png") no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div:nth-child(3)>.active {
  // background: url("@/assets/icons/portal/index_icon_ygg.png") no-repeat center center;
  background-size: 90% auto;
}


.work-center .uk-card .uk-card-header>div:first-child::before {
  content: "";
  width: 20px;
  height: 24px;
  //background: url("@/assets/icons/portal/index_icon_task.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div:nth-child(2)::before {
  content: "";
  width: 20px;
  height: 24px;
  // background: url("@/assets/icons/portal/index_icon_taskok.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div:nth-child(3)::before {
  content: "";
  width: 20px;
  height: 24px;
  // background: url("@/assets/icons/portal/index_icon_msg.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div.active:nth-child(1)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_task_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div.active:nth-child(2)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_taskok_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div.active:nth-child(3)::before {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_msg_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.swiper-pagination {
  border: 2px solid blue !important;
}

.khidi_banner {
  width: 460px;
  height: 330px;

  .img {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0
  }

  .desc {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 4;
    height: 30px;
    font-size: 14px;
    line-height: 30px;
    box-sizing: border-box;
    padding: 0 10px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
  }
}

.swiper-pagination-bullet {
  width: 30px !important;
  height: 30px;
  background-color: #fff;
  opacity: 0.5;
  border: 1px solid #000;
}

.swiper-pagination-bullet-active {
  background-color: #000;
  opacity: 1;
}
</style>
