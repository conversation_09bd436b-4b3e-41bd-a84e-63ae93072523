<template>
  <div class="portal_page">
    <breadcrumb title="办公中心"/>
    <div class="siderbar" :style="{left: is_open ? '0' : '-240px'}">
      <div class="isShow-work" @click="closeOpen" :style="{left: is_open ? '240px' : '0'}">
        <ArrowLeftBold v-if="is_open" class="icon" size="12px"/>
        <ArrowRightBold v-else class="icon" size="12px"/>
      </div>
      <ul>
        <li class="p_item">
          <div class="custom-left-navigation-title">
            <div class="title" @click="toIndex">
              <div class="icon icon-work"></div>我的工作
            </div>
          </div>
          <div class="ul_sub">
            <router-link
              :class="menu_actived == 'newtask' ? 'actived' : ''"
              :to="'myWork'"
              @click="db_open('newtask')"
            >
              我的待办
            </router-link>
            <!--            <div class="l"></div>-->
            <router-link
              :class="menu_actived == 'completetask' ? 'actived' : ''"
              :to="'myWork'"
              @click="db_open('completetask')"
            >
              我的已办
            </router-link>
            <!--            <div class="l"></div>-->
            <router-link
              :class="menu_actived == 'apply' ? 'actived' : ''"
              :to="'myWork'"
              @click="db_open('apply')"
            >
              我的申请
            </router-link>
            <!--            <div class="l"></div>-->
            <router-link
              :class="menu_actived == 'myfile' ? 'actived' : ''"
              :to="'myWork'"
              @click="db_open('myfile')"
            >
              我的文件
            </router-link>
            <!--            <div class="l"></div>-->
            <router-link
              :class="menu_actived == 'focus' ? 'actived' : ''"
              :to="'myWork'"
              @click="db_open('focus')"
            >
              我的关注
            </router-link>
          </div>
        </li>
        <li class="p_item">
          <div class="custom-left-navigation-title">
            <div class="title">
              <div class="icon icon-ranking"></div>
              点击排行
            </div>
          </div>
          <ul class="ul_sub ul_sub_sort" style="min-height: 312px;">
            <li v-for="(dm, didx) in CYRKList?.slice(0, 6) || []" :key="didx"
                @click="handelhref(dm.Url, dm)">
              <a target="_blank" v-text="dm.FuncName"></a>
              <!--              <div class="l"></div>-->
            </li>
          </ul>
        </li>
      </ul>
    </div>
    <router-view/>
  </div>
</template>
<script>
import * as portal from '@/api/portal'
import * as LogApi from '@/api/system/pageAndFuncLog'
import {useAppStore} from '@/store/modules/app'
import {getAccessToken} from '@/utils/auth'
import {CATEGORY, useLayoutCache} from '@/hooks/web/categoryCache'
import breadcrumb from '@/layoutNew/components/breadcrumb.vue'
import {ArrowRightBold, ArrowLeftBold} from '@element-plus/icons-vue'

const {wsCache} = useLayoutCache()
//获取全局
export default {
  components: {ArrowRightBold, ArrowLeftBold, breadcrumb},
  data() {
    return {
      CYRKList: [],
      is_open: false,
      token: '',
      baseUrl: ''
    }
  },
  computed: {
    menu_actived() {
      return useAppStore().work_menu
    }
  },

  mounted() {
    this.loadCYRK()
    this.token = getAccessToken()
    this.baseUrl = getCurrentInstance()?.appContext.config.globalProperties.$baseUrl
  },

  methods: {
    closeOpen() {
      this.is_open = !this.is_open
    },
    //菜单点击
    db_open(tag) {
      let cate = ''
      switch (tag) {
        case 'newtask':
          cate = '我的待办'
          break
        case 'completetask':
          cate = '我的已办'
          break
        case 'apply':
          cate = '我的申请'
          break
        case 'myfile':
          cate = '我的文件'
          break
        case 'focus':
          cate = '我的关注'
          break
      }

      const objdata = {
        pagename: cate,
        pageurl: '/Portal/workCenter/myWork',
        tag: '我的工作'
      }
      LogApi.pageLog(objdata)
      useAppStore().set_work_menu(tag) //设置全局变量
    },
    toIndex() {
      if (wsCache.get(CATEGORY.IsLayout)) {
        return '/Portal/newsLayout'
      } else {
        return '/Portal/WorkCenter/workIndex'
      }
    },
    handelhref(url, item) {
      let toUrl
      if (url.includes('http://') || url.includes('https://')) {
        if (url.includes('?')) {
          toUrl = url + '&token=' + this.token
        } else {
          toUrl = url + '?token=' + this.token
        }
      } else {
        if (url.includes('?')) {
          toUrl = this.baseUrl + url + '&token=' + this.token
        } else {
          toUrl = this.baseUrl + url + '?token=' + this.token
        }
      }
      //替换//
      toUrl = toUrl.replace(/([^:]\/)\/+/g, '$1')
      const objdata = {
        funcname: item.FuncName,
        funcurl: item.Url,
        MenuId: item.MenuId
      }
      LogApi.funcLog(objdata)
      window.open(toUrl, '_blank')
    },

    //左侧导航
    loadCYRK: function () {
      portal.djph_list({}).then((ret) => {
        this.CYRKList = ret
      })
    }
  }
}
</script>
<style scoped lang="scss">
.wxts_dialog_close {
  position: absolute;
  right: 10px;
  bottom: 0;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden;
  cursor: pointer;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes slideOutUp {
  from {
    opacity: 0;
    transform: translate3d(0, 0, -100%);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.slideOutUp {
  animation-name: slideOutUp;
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    opacity: 0;
    transform: translate3d(0, 0, -100%);
  }
}

.slideOutDown {
  animation-name: slideOutDown;
}

.portal_page {
  position: relative;
  margin: auto;
}

.isShow-work {
  position: fixed;
  left: 240px;
  top: 50%;
  width: 18px;
  height: 80px;
  background-color: #FFF;
  /* box-shadow: 0 0 2px 0 rgba(18, 126, 248, 0.3); */
  border-radius: 0 20px 20px 0;
  cursor: pointer;
  line-height: 80px;
  padding-left: 2px;
  border: 1px solid rgba(18, 126, 248, 0.3);
  border-left: 0;

  .icon {
    font-size: 14px;
    font-weight: bold;
  }
}

.isShow-work:hover {
  .icon {
    color: #3370ff;
  }
}

.siderbar {
  position: fixed;
  top: 210px;
  left: 0;
  z-index: 999;
  height: 600px;
  box-shadow: 0 0 12px 0 rgba(18, 126, 248, 0.3);
  width: 240px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 0 8px 8px 0;
  background-color: #fff;
  ul {
    margin: 0 !important;
    padding: 0 !important;
    list-style: none;
  }

  .p_item {
    white-space: nowrap;
    text-overflow: ellipsis;

    .m_a {
      color: black;
      font-weight: 600;
      text-decoration: none;
      display: flex;
      align-items: center;
      height: 30px;

      &.expend::after {
        background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Cpolyline%20fill%3D%22none%22%20stroke%3D%22%23666%22%20stroke-width%3D%221.1%22%20points%3D%2210%201%204%207%2010%2013%22%20%2F%3E%0A%3C%2Fsvg%3E);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        transform: rotate(180deg);
        transition: transform 0.4s ease-out;
        width: 1.5em;
        height: 1.5em;
        content: '';
        margin-left: 126px;
      }

      &.open::after {
        transform: rotate(90deg);
      }

      .icon {
        margin-left: 12px;
      }

      .text {
        font-size: 15px;
        margin-left: 6px;
        color: #000;
      }
    }

    .ul_sub {
      transition: height 0.3s ease-out;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      margin: 0 !important;
      padding: 0 !important;
      //background: linear-gradient(to bottom, #fff, #f0f9ff);

      li {
        cursor: pointer;
      }

      li:hover {
        a {
          color: rgb(0, 112, 255);
        }
      }

      .l {
        background: #d9e0e5;
        width: 190px;
        height: 1px;
        margin-left: 20px;
      }

      a {
        height: 42px;
        line-height: 42px;
        display: inline-block;
        font-weight: normal;
        padding-left: 44px;
        text-decoration: none;
        width: calc(100% - 0px);
        //border-left: 2px solid #fff;

        &:hover {
          color: #0070ff;
          background: linear-gradient(-90deg, rgba(44, 163, 255, 0.18) 0%, rgba(0, 113, 254, 0.18) 100%);
        }

        &.actived {
          border-left: 2px solid #0070ff;
          //background-color: #f4faff;
          background-color: rgba(30, 94, 186, 0.1);
          color: #0070ff;
        }
      }
    }
    .ul_sub_sort{
      li:hover{
        background: linear-gradient(-90deg, rgba(44, 163, 255, 0.18) 0%, rgba(0, 113, 254, 0.18) 100%);
      }
    }
  }
}

.close-icon {
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  left: 160px;
  top: 0;
  width: 40px;
  height: 38px;
  text-align: center;
  line-height: 50px;
  background: rgb(62, 163, 247);
}

.sm_side {
  height: 120px;
  width: 42px;
  position: fixed;
  cursor: pointer;
  left: 0;
  top: 400px;
  z-index: 1000;

  .align-center {
    display: flex;
    width: 42px;
    height: 42px;
    background: #fff;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
    justify-content: center;
    align-items: center;

    .w-36 {
      width: 36px;
    }

    .h-36 {
      height: 36px;
    }
  }

  .hide_luntan {
    width: 42px;
    height: 120px;
    background-image: url("@/assets/imgs/home/<USER>");
    background-size: 100% 100%;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.custom-left-navigation-title {
  padding: 4px;
  //background-image: url('@/assets/imgs/home/<USER>');
  background-size: 100% 100%;

  .title {
    font-size: 16px;
    height: 30px;
    width: 190px;
    display: flex;
    align-items: center;
    font-weight: bold;
    .icon {
      width: 22px;
      height: 22px;
      margin-right: 8px;
    }
    .icon-work::before {
      content: "💼";
    }

    .icon-ranking::before {
      content: "🏆";
    }
  }

  .eng {
    color: #91dbff;
    font-size: 12px;
    width: 190px;
    text-align: left;
    padding-left: 60px;
    margin-top: 5px;
  }
}
</style>
