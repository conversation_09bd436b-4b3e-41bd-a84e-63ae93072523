<template>
  <div class="body" v-loading="loading">
    <div class="row">
      <banner :width="600" :height="378" :list="tpxwList"/>
      <div class="col" style="width: 630px; position: relative;">
        <el-tabs v-model="activeNameTZ" class="demo-tabs" @tab-click="handleClickTZ">
          <el-tab-pane label="院发文" name="YNFW">
            <template #label>
              <span class="custom-tabs-label">
                <span>院发文</span>
              </span>
            </template>
            <div v-if="activeNameTZ=='YNFW'" class="list" style="width: 100%;">
              <div
                class="item"
                v-for="(item, index) in YNXXList?.slice(0, 8) || []"
                :key="index"
                @click="openHref('YNFW', item.ID, '院发文', item)">
                <div class="date">
                  <div class="day">{{
                      dateFormat(item.CreateTime, 'month')
                    }}-{{ dateFormat(item.CreateTime, 'day') }}
                  </div>
                </div>
                <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop" class="isTop"
                     style="left: 56px;"/>
                <div :title="item.Title" class="href1" :class="[item.IsTop ? 'remind' : '']">
                  {{ item.Title }}
                </div>
              </div>
              <el-empty v-if="YNXXList.length === 0" description="数据为空"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="院通知" name="YNTZ">
            <template #label>
              <span class="custom-tabs-label">
                <span>院通知</span>
              </span>
            </template>
            <div v-if="activeNameTZ=='YNTZ'" class="list" style="width: 100%;">
              <div
                class="item"
                v-for="(item, index) in YNXXList?.slice(0, 8) || []"
                :key="index"
                @click="openHref('YNFW', item.ID, '院发文', item)">
                <div class="date">
                  <div class="day">{{
                      dateFormat(item.CreateTime, 'month')
                    }}-{{ dateFormat(item.CreateTime, 'day') }}
                  </div>
                </div>
                <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop" class="isTop"
                     style="left: 56px;"/>
                <div :title="item.Title" class="href1" :class="[item.IsTop ? 'remind' : '']">
                  {{ item.Title }}
                </div>
              </div>
              <el-empty v-if="YNXXList.length === 0" description="数据为空"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="院公告" name="YNGG">
            <template #label>
              <span class="custom-tabs-label">
                <span>院公告</span>
              </span>
            </template>
            <div v-if="activeNameTZ=='YNGG'" class="list" style="width: 100%;">
              <div
                class="item"
                v-for="(item, index) in YNXXList?.slice(0, 8) || []"
                :key="index"
                @click="openHref('YNFW', item.ID, '院发文', item)">
                <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop" class="isTop"
                     style="left: 56px;"/>
                <div class="date">
                  <div class="day">{{
                      dateFormat(item.CreateTime, 'month')
                    }}-{{ dateFormat(item.CreateTime, 'day') }}
                  </div>
                </div>
                <div :title="item.Title" class="href1" :class="[item.IsTop ? 'remind' : '']">
                  {{ item.Title }}
                </div>
              </div>
              <el-empty v-if="YNXXList.length === 0" description="数据为空"/>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="custom-buttons" @click="openDetails(ynParameter.text)">
          <div class="work-more"> 更多</div>
        </div>
      </div>
      <div class="col" style="width: 630px;display: none;">
        <myPanel title="通知公告" class="panel-0" :height="378"
                 @moreClick="openDetails(ynParameter.text)">
          <div class="tabs" style="width: 600px">
            <div
              class="tab"
              :class="[ynParameter.tzIndex == 0 ? 'active' : '']"
              @click="loadYNXX('YNFW', 0, '院发文')">
              院发文
            </div>
            <div
              class="tab"
              :class="[ynParameter.tzIndex == 1 ? 'active' : '']"
              @click="loadYNXX('YNTZ', 1, '院通知')">
              院通知
            </div>
            <div
              class="tab"
              :class="[ynParameter.tzIndex == 2 ? 'active' : '']"
              @click="loadYNXX('YNGG', 2, '院公告')">
              院公告
            </div>
          </div>
          <div class="list" style="width: 600px">
            <div
              class="item"
              v-for="(item, index) in YNXXList?.slice(0, 7) || []"
              :key="index"
              @click="openHref('YNFW', item.ID, '院发文', item)">
              <div class="date">
                <div class="day">{{
                    dateFormat(item.CreateTime, 'month')
                  }}-{{ dateFormat(item.CreateTime, 'day') }}
                </div>
              </div>
              <img src="@/assets/imgs/home/<USER>" v-if="item.IsTop" class="isTop"/>
              <div :title="item.Title" class="href1" :class="[item.IsTop ? 'remind' : '']">
                {{ item.Title }}
              </div>
            </div>
            <el-empty v-if="YNXXList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
    </div>
    <div class="row">
      <div class="col bborder" style="width: 100%; position: relative;">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="我的待办" name="newtask">
            <template #label>
              <span class="custom-tabs-label label-1">
                <span>我的待办</span>
                <span v-if="newtaskTaskNumber>0" class="num">{{ newtaskTaskNumber }}</span>
              </span>
            </template>
            <div v-loading="todoLoading && is4Alogin">
              <div v-if="activeName=='newtask'" class="list" style="width: 100%">
                <div
                  class="item todo"
                  v-for="(item, index) in taskList"
                  :key="index"
                  @click="OpenView(item, '我的待办')"
                >
                  <div class="icon">
                    <img
                      title="取消关注"
                      v-if="item.IsFocus == 'true'"
                      src="@/assets/icons/portal/icon-tuding-copy.png"
                    />
                    <img
                      title="关注任务"
                      v-else-if="item.IsFocus == 'false'"
                      src="@/assets/icons/portal/icon-tudingwb.png"
                    />
                  </div>
                  <div
                    :title="item.ActivityName"
                    class="title ellipsis1"
                    :style="{ color: item.ExtsField }"
                  >
                    {{ item.ActivityName }}
                  </div>
                  <div class="cate">
                    <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                    <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                    <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
                  </div>
                  <div class="people-info">
                    <span class="dept" :title="item.FromDeptName" v-if="item.FromDeptName">
                      {{ item.ShortDeptName }}：
                    </span>
                    <span class="dept" v-else></span>
                    <span class="name" v-if="item.FromUserNames">{{ item.FromUserNames }}</span>
                    <span class="name" v-else></span>
                    <span class="time">{{ item.CreateTime.substring(0, 16) }}</span>
                  </div>
                </div>
                <el-empty v-if="taskList.length === 0" description="数据为空"/>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="我的已办" name="completetask">
            <template #label>
              <span class="custom-tabs-label label-1">
                <span>我的已办</span>
              </span>
            </template>
            <div v-loading="todoLoading && is4Alogin">
              <div v-if="activeName=='completetask'" class="list" style="width: 100%;">
                <div
                  class="item todo"
                  v-for="(item, index) in taskList"
                  :key="index"
                  @click="OpenView(item, '我的已办')"
                >
                  <div class="icon">
                    <img
                      title="取消关注"
                      v-if="item.IsFocus == 'true'"
                      @click.stop="FocusFlow(0, item, taskList.DataType)"
                      src="@/assets/icons/portal/icon-tuding-copy.png"
                    />
                    <img
                      title="关注任务"
                      v-else-if="item.IsFocus == 'false'"
                      @click.stop="FocusFlow(1, item, taskList.DataType)"
                      src="@/assets/icons/portal/icon-tudingwb.png"
                    />
                  </div>
                  <div class="title ellipsis1" :title="item.TaskName">{{ item.TaskName }}</div>
                  <div class="cate">
                    <span v-if="item.GoodWaySoft === 'RIIT'">信息院综合管理系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_SRMSFlow'">科技系统</span>
                    <span v-else-if="item.GoodWaySoft === 'JiuQiCaiWu'">财务系统</span>
                    <span v-else-if="item.GoodWaySoft === 'PC_PRPFlow'">EPC系统</span>
                    <span v-else :title="item.FlowName">{{ item.FlowName }}</span>
                  </div>
                  <div class="info" style="width: 200px;">
                    <span class="dept" v-if="item.CreateDeptName" :title="item.CreateDeptName">
                      {{ item.CreateUserDeptName }}：</span
                    >
                    <span class="name">{{ item.CreateUserName }}</span>
                    <span class='time'>{{ item.ExecTime.substring(0, 16) }}</span>
                  </div>
                </div>
                <el-empty v-if="taskList.length === 0" description="数据为空"/>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="我的关注" name="focus">
            <template #label>
              <span class="custom-tabs-label label-3">
                <span>我的关注</span>
              </span>
            </template>
            <div v-loading="todoLoading && is4Alogin">
              <div v-if="activeName=='focus'" class="list" style="width: 100%">
                <div
                  class="item todo"
                  v-for="(item, index) in taskList"
                  :key="index"
                  @click="OpenView(item, '我的关注')">
                  <div class="icon">
                    <img src="@/assets/icons/portal/icon-tuding-copy.png"/>
                  </div>
                  <div class="title ellipsis1" :title="item.FlowName">{{ item.FlowName }}</div>
                  <div class="apply-flow info" style="width: 360px;">
                    <div class="dept">
                      <div class="dept-type">
                        <span>审批状态：</span>
                        <span :title="item.FlowState">{{ item.FlowState }}</span>
                      </div>
                      <div class="dept-name">
                        <span>关注日期：</span>
                        <span v-if="item && item.CreateTime">{{
                            item.CreateTime.split(' ')[0]
                          }}</span>
                      </div>
                    </div>

                    <div class="time">
                      <span>当前审批步骤：</span>
                      <span :title="item.CurrentStep">{{ item.CurrentStep }}</span>
                    </div>
                  </div>
                </div>
                <el-empty v-if="taskList.length === 0" description="数据为空"/>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="custom-buttons" @click="openDetails(tabName)">
          <div class="work-more"> 更多</div>
        </div>
      </div>
    </div>
    <div class="row">
      <my-panel title="功能收藏" :height="'auto'" @more-click="openFunCollect">
        <div class="list" style="width: 100%">
          <div class="row-list" style="justify-content: flex-start !important;gap: 8px;">
            <div
              class="c2btn btn"
              v-for="(item, index) in CYGNList?.slice(0, 21) || []"
              :key="index"
              @click="OpenRes(item.Url, item.Name, item.ID, '功能收藏')"
            >
              <img :src="item.IconUrl" class="icon"/>
              <div class="text" v-text="item.Alias"></div>
            </div>
            <el-empty v-if="CYGNList.length === 0" description="数据为空"/>
          </div>
        </div>
      </my-panel>
    </div>
    <div class="row">
      <div class="col" style="width: 600px">
        <myPanel
          title="我的消息"
          :number="true"
          :number-num="MsgCount"
          :height="378"
          @more-click="OpenMoreMsg()">
          <div class="list" style="width: 100%">
            <div
              class="item todo"
              v-for="(item, index) in MsgList?.slice(0, 10) || []"
              :key="index"
              style="justify-content: flex-start !important"
              @click="OpenMsg(item)">
              <div class="icon" style="margin-right: 6px">
                <img src="@/assets/icons/portal/icon-msg.png"/>
              </div>
              <div :title="item.Title" class="title ellipsis1" style="flex: 1">
                {{ item.Title }}
              </div>
              <div class="apply-flow info" style="width: 140px">
                <div>
                  <span style="margin-right: 5px; color: #a9b4c7">发送人</span>
                  <span>{{ item.SenderName }}</span>
                </div>
                <div>
                  <span style="margin-right: 5px; color: #a9b4c7">时间</span>
                  <span v-if="item && item.SendTime">{{ item.SendTime.split(' ')[0] }}</span>
                </div>
              </div>
            </div>
            <el-empty v-if="MsgList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
      <div class="col" style="width: 600px">
        <myPanel title="我的文件" :height="378" @moreClick="openMoreFile()">
          <div class="list" style="width: 100%;">
            <div
              class="item todo"
              v-for="(item, index) in FileList?.slice(0, 10) || []"
              :key="index">
              <div class="icon">
                <img :src="instance.proxy.$getFullUrl(item.Icon)"/>
              </div>
              <div
                :title="item.FileName.substring(item.FileName.indexOf('_') + 1)"
                class="title ellipsis1">
                {{ item.FileName.substring(item.FileName.indexOf('_') + 1) }}
              </div>
              <div class="info" style="width: 60px;">
                <img
                  @click.stop="DownLoadAndPreviewFile(item, 0)"
                  title="下载"
                  src="@/assets/icons/portal/icon-download.png"/>
                <img @click.stop="CancelSc(item)" title="取消收藏" src="@/assets/imgs/sc_a.png"/>
              </div>
            </div>
            <el-empty v-if="FileList.length === 0" description="数据为空"/>
          </div>
        </myPanel>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import $ from 'jquery'
import * as LogApi from '@/api/system/pageAndFuncLog'
import myPanel from '@/components/Panel/src/index.vue'
import banner from '@/views/portal/components/banner.vue'
import {getCurrentInstance, ref} from 'vue'
import * as PortalApi from '@/api/system/portal'
import * as portal from '@/api/portal'
import type {TabsPaneContext} from 'element-plus'
import gnsc_img1 from '@/assets/icons/portal/01.png'
import gnsc_img2 from '@/assets/icons/portal/02.png'
import gnsc_img3 from '@/assets/icons/portal/03.png'
import gnsc_img4 from '@/assets/icons/portal/04.png'
import gnsc_img5 from '@/assets/icons/portal/05.png'
import gnsc_img6 from '@/assets/icons/portal/06.png'
import gnsc_img7 from '@/assets/icons/portal/07.png'
import gnsc_img8 from '@/assets/icons/portal/08.png'
import gnsc_img9 from '@/assets/icons/portal/09.png'
import gnsc_img10 from '@/assets/icons/portal/10.png'
import {replaceUrl} from '@/assets/js/NK'
import {getAccessToken} from '@/utils/auth'

import {useAppStore} from '@/store/modules/app'
import {openTask} from '@/layout/portal/admin'
import {useUserStore} from '@/store/modules/user'
import {getIsOpen, handlerSend, messageInfo, toggleConnectStatus} from '@/utils/websocket'
import {Storage, CACHE_KEY, useCache} from "@/hooks/web/useCache";
import emitter from "@/utils/mitt";
import {cmaj} from "@/utils/legacy-ajax"

const {wsCache} = Storage()
const {Cookies} = useCache()
const ynParameter = ref<any>({
  tzIndex: 0,
  type: 'YNFW',
  text: '院发文'
})
const activeNameTZ = ref('YNFW')
const userStore = useUserStore()
const tpxwList = ref<Array<any> | null>([])
const YNXXList = ref<Array<any> | null>([])
const taskList = ref<Array<any> | null>([])
const CYGNList = ref<Array<any> | null>([])
const MsgList = ref<Array<any> | null>([])
const FileList = ref<Array<any> | null>([])
const newtaskTaskNumber = ref<Number | null>(0)
const completeTaskNumber = ref<Number | null>(0)
const focusTaskNumber = ref<Number | null>(0)
const MsgCount = ref<Number | null>(0)
const FileCount = ref<Number | null>(0)
const taskDataType = ref<String | null>('newtask')
const tabName = ref<String | null>('我的待办')
const tabNameTZ = ref<String | null>('院发文')
const is4Alogin = ref<Boolean | null>(false)
const todoLoading = ref<Boolean | null>(true)
const loading = ref<Boolean | null>(true)
const show_loading = ref<Boolean | null>(true)
const instance = getCurrentInstance() // 获取当前组件实例
const router = useRouter() // 直接获取 router 实例
const downLoadUrl = import.meta.env.VITE_DOWNLOAD_URL
const baseUrl = import.meta.env.VITE_TOURL_PREFIX
const activeName = ref('newtask')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  tabName.value = tab.props.label
  activeName.value = tab.paneName
  taskList.value = []
  console.log(tab.paneName)
  loadTask(tab.paneName)
}
const handleClickTZ = (tab: TabsPaneContext, event: Event) => {
  tabNameTZ.value = tab.props.label
  activeNameTZ.value = tab.paneName
  if (tab.paneName == 'YNFW') {
    loadYNXX(activeNameTZ.value, 0, tabNameTZ.value)
  } else if (tab.paneName == 'YNTZ') {
    loadYNXX(activeNameTZ.value, 1, tabNameTZ.value)
  } else if (tab.paneName == 'YNGG') {
    loadYNXX(activeNameTZ.value, 2, tabNameTZ.value)
  }

}
const loadTpxw = function () {
  PortalApi.newsCenterGetNewsList({code: 'TPXW', pageSize: 8}).then((result) => {
    tpxwList.value = result.records
  })
}
const index = ref<Number>(0)
const message = useMessage()
const FocusFlow = (type, item, tabName) => {
  let Title = '';
  if (tabName == 'newtask') {
    Title = item.ActivityName
  } else if (tabName == 'completetask') {
    Title = item.TaskName
  } else if (tabName == 'apply') {
    Title = item.Title
  } else if (tabName == 'focus') {
    Title = item.FlowName
  }

  let msg = type == 0 ? '是否取消关注【' + Title + '】？' : '是否关注【' + Title + '】？'

  message.confirm(msg, '提示').then(() => {
    PortalApi.execMyFocus({
      type: type,
      tabName: tabName,
      myFlow: JSON.stringify(item)
    }).then((ret) => {
      if (ret) {
        ElMessage({
          message: type == 0 ? '已经取消关注！' : '关注成功，可在【我的关注】列表查看。',
          type: 'success',
          offset: '10'
        })
        loadTask(tabName)
      } else {
        ElNotification({
          title: 'Success',
          message: 'This is a success message',
          type: 'error'
        })
      }
    })
  })
}
const loadTask = function (type) {
  todoLoading.value = true
  index.value++
  if (type === 'newtask') {
    //我的待办
    portal.wddb_list({
      page: 1,
      pageSize: 8,
      Activity: ''
    }).then((res) => {
      let ret = res.data
      if (ret) {
        taskList.value = ret.records
        newtaskTaskNumber.value = ret.total > 99 ? '99+' : ret.total
        taskDataType.value = type
      }
      todoLoading.value = false
    }).catch(() => {
      todoLoading.value = false
    })
  } else if (type === 'completetask') {
    portal.getCompleteTasks({
      page: 1,
      pageSize: 8,
      Activity: ''
    }).then((res) => {
      let ret = res.data
      if (ret) {
        taskList.value = ret.records
        completeTaskNumber.value = ret.total > 99 ? '99+' : ret.total
        taskDataType.value = type
      }
      todoLoading.value = false
    })
  } else {
    portal.wdgz_list({
      type: type,
      page: 1,
      pageSize: 5,
      key: index.value
    })
      .then((res) => {
        let ret = res.data
        if (ret && res.key == index.value) {
          taskList.value = ret.records
          if (type === 'completetask') {
            completeTaskNumber.value = ret.total > 99 ? '99+' : ret.total
          } else if (type === 'focus') {
            focusTaskNumber.value = ret.total > 99 ? '99+' : ret.total
          }
          taskDataType.value = type
        }
        todoLoading.value = false
      })
      .catch(() => {
        todoLoading.value = false
      })
  }
}
const loadCYGN = function () {
  portal.gnsc_list({}).then((ret) => {
    //默认图标数组
    var defaultIconArray = [
      gnsc_img1,
      gnsc_img2,
      gnsc_img3,
      gnsc_img4,
      gnsc_img5,
      gnsc_img6,
      gnsc_img7,
      gnsc_img8,
      gnsc_img9,
      gnsc_img10
    ]
    var defaultIconIndex = 0
    ret.forEach(function (item, index, arr) {
      //没有别名更改为名称
      if (item.Alias === null || item.Alias === '') {
        item.Alias = item.Name
      }
      //图标处理逻辑
      if (item.IconUrl === null) {
        if (defaultIconIndex === 10) {
          defaultIconIndex = 0
        }
        item.IconUrl = defaultIconArray[defaultIconIndex]
        defaultIconIndex += 1
      } else {
        item.IconUrl =
          import.meta.env.VITE_TOURL_PREFIX +
          '/BasicApplication/DownloadFile?FileID=' +
          item.IconUrl
      }
    })
    CYGNList.value = ret
  })
}
const loadMsgAndFile = function (type) {
  show_loading.value = true
  //我的待办
  portal.wdgz_list({
    type: type,
    page: 1,
    pageSize: type == 'msg' ? 5 : 8
  }).then((ret) => {
    show_loading.value = false
    ret = ret.data
    if (type == 'msg') {
      MsgList.value = ret.records
      MsgCount.value = ret.total
      if (ret.Count > 99) {
        MsgCount.value = '99+'
      }
    } else {
      FileList.value = ret.records
      FileCount.value = ret.total
    }
  })
    .catch(() => {
      show_loading.value = false
    })
}
onMounted(() => {
  loading.value = true
  emitter.on('set4AcookieOK', () => {
    is4Alogin.value = true
  })
  is4Alogin.value = Cookies.get(CACHE_KEY.login4A)
  if (!getIsOpen.value) {
    toggleConnectStatus()
  }
  loadTpxw()
  loadYNXX('YNFW', 0, '院发文')
  loadTask('newtask')
  loadCYGN()
  loadMsgAndFile('msg') //我的消息
  loadMsgAndFile('myfile') //我的文件
  loading.value = false
})
watch(
  () => messageInfo.value,
  (val) => {
    console.log('messageInfo.value:' + messageInfo.value)
    if (val) {
      handleSocket(messageInfo.value)
    }
  }
)
const handleSocket = (obj) => {
  if ('msg' == obj.type) {
    loadMsgAndFile('msg') //我的消息
  }

  if ('task' == obj.type) {
    loadTask('newtask')
  }

  if ('completetask' == obj.type) {
    loadTask(0, 'completetask') //更新任务
  }
  if ('focus' == obj.type) {
    loadTask('focus')
  }

  if ('myfile' == obj.type) {
    loadMsgAndFile('myfile') //我的文件
  }

  if ('news' == obj.type) {
    loadTpxw()
    loadYNXX('YNFW', 0, '院发文')
  }
}
const loadYNXX = async function (type, index, text) {
  ynParameter.value = {
    tzIndex: index,
    type: type,
    text: text
  }
  if (type === 'YNFW') {
    await loadYfw()
  } else if (type === 'YNTZ') {
    await loadYtz()
  } else if (type === 'YNGG') {
    await loadYgg()
  }
}
const loadYfw = async function () {
  portal
    .yfw_list({
      page: 1,
      pageSize: 10
    })
    .then((ret) => {
      YNXXList.value = ret.records
    })
}
const loadYtz = async function () {
  portal
    .ytz_list({
      page: 1,
      pageSize: 10
    })
    .then((ret) => {
      YNXXList.value = ret.records
    })
}
const loadYgg = async function () {
  portal
    .ygg_list({
      page: 1,
      pageSize: 10
    })
    .then((ret) => {
      YNXXList.value = ret.records
    })
}
const openHref = function (code, id, cate, item) {
  if (['TPXW', 'YNGG', 'YNTZ', 'YNFW', 'ZDBD', 'ZCYCQ', 'SWGL', 'CWZJ'].indexOf(code) > -1) {
    const url =
      '/Portal/NewsCenter/NewsDetail?Code=' + code + '&ID=' + id + '&navigation=' + encodeURI(cate)
    let objdata = {
      pagename: item.Title,
      pageurl: url,
      tag: cate,
      id: item.ID
    }
    LogApi.pageLog(objdata)
    window.open(url, '_blank')
  }
}
const openDetails = function (tabName) {
  let objdata = {
    workno: userStore.user.workNo,
    pagename: '更多',
    pageurl: '',
    tag: ''
  }
  let url = ''
  if (tabName == '我的待办') {
    useAppStore().set_work_menu('newtask') //设置全局变量
    url = '/Portal/workCenter/myWork'
    objdata.tag = '我的待办'
    router.push({path: 'myWork'})
  } else if (tabName == '我的已办') {
    useAppStore().set_work_menu('completetask') //设置全局变量
    url = '/Portal/workCenter/myWork'
    objdata.tag = '我的已办'
    router.push({path: 'myWork'})
  } else if (tabName == '我的关注') {
    useAppStore().set_work_menu('focus') //设置全局变量
    url = '/Portal/workCenter/myWork'
    objdata.tag = '我的关注'
    router.push({path: 'myWork'})
  } else if (tabName == '院发文') {
    url = '/Portal/NewsCenter/PublicInfoList?code=YNFW'
    objdata.tag = '院发文'
    window.open(url)
  } else if (tabName == '院通知') {
    url = '/Portal/NewsCenter/PublicInfoList?code=YNTZ'
    objdata.tag = '院通知'
    window.open(url)
  } else if (tabName == '院公告') {
    url = '/Portal/NewsCenter/PublicInfoList?code=YNGG'
    objdata.tag = '院公告'
    window.open(url)
  }
  objdata.pageurl = url
  LogApi.pageLog(objdata)
}
const moreClick = (type) => {
  window.open('/Portal/NewsCenter/PublicInfoList?code=' + type, '_blank')
}
const OpenRes = function (url, name, menuId, pageName) {
  //记录功能日志
  if (url.indexOf('?') > 0) url += '&'
  else url += '?'
  url = replaceUrl({url: url})
  // 相对路径修改为绝对路径
  if (!url.startsWith('http')) {
    url = instance?.proxy.$getFullUrl(url)
  }
  if (url.indexOf('http') == 0 && url.indexOf('token') == -1) {
    url += 'token=' + getAccessToken()
  }
  window.open(url)

  //写日志
  const objdata = {
    workno: userStore.user.workNo,
    pagename: pageName,
    pageurl: url,
    funcname: name,
    funcurl: url,
    menuId: menuId
  }
  PortalApi.FuncLog(objdata)
}
const openFunCollect = function () {
  let url =
    '/UIBuilder/UIViewer/EditerListViewer?TempletCode=EditerList_ad5d00e2e6d442b4a6bde149818a986e&token=' +
    getAccessToken()
  url = import.meta.env.VITE_TOURL_PREFIX + url
  window.open(url, '_blank')
}
const OpenMsg = function (item) {
  let url =
    import.meta.env.VITE_TOURL_PREFIX +
    `/BasicApplication/InfoManage/Msg/MsgView?FuncType=Receive&ID=${item.ID}&Type=${item.Type}`
  window.open(url)
}
const OpenMoreMsg = function () {
  let url = import.meta.env.VITE_TOURL_PREFIX + '/BasicApplication/InfoManage/Msg/MsgTab?token=' + getAccessToken()
  const objdata = {
    pagename: '更多',
    pageurl: url,
    tag: '我的消息',
    id: null
  }
  LogApi.pageLog(objdata)
  window.open(url)
}
const OpenView = async function (item, typeName) {
  let url = ''
  const objdata = {
    funcname: item.ActivityName,
    funcurl: item.URL,
    menuId: item.ID
  }
  LogApi.funcLog(objdata)
  if (typeName == '我的关注') {
    PortalApi.GetMyFocusByID({ID: item.ID}).then((ret) => {
      if (!ret.newtask) {
        if (item.ExecURL.indexOf('{token}')) {
          url = item.ExecURL.replace('{token}', getAccessToken())
        } else {
          url = item.ExecURL + '&token=' + getAccessToken()
        }
        window.open(baseUrl.value + url)
      } else {
        openTask(ret.newtask)
      }
    })
  } else if (typeName == '我的待办') {
    openTask(item)
  } else if (typeName == '我的已办') {
    window.open(item.ViewUrl)
  }
}
const dateFormat = (date: string, type: string) => {
  const d = new Date(date)
  if (type === 'year') {
    return d.getFullYear()
  }
  if (type === 'month') {
    return d.getMonth() > 9 ? d.getMonth() + 1 : '0' + (d.getMonth() + 1)
  }
  if (type === 'day') {
    return d.getDate() > 9 ? d.getDate() : '0' + d.getDate()
  }
  return d.getMonth() + 1 + '-' + d.getDate()
}

async function DownLoadAndPreviewFile(file, type) {
  var url = file.FileKey.substring(file.FileKey.indexOf('|'), file.FileKey.length)
  var _pre = ''
  var _correctfilekey = function (filekey) {
    if (filekey && filekey.indexOf('|') != -1) {
      return filekey.split('|')[1]
    }
    return filekey
  }
  if (url.indexOf('http:') == 0 && url.indexOf('|') > 0) {
    _pre = url.split('|')[1]
    url = url.split('|')[0]
  } else {
    if (url.indexOf('|') == 0) {
      url = url.substring(1, url.length)
    }
  }
  switch (file.Cgroup) {
    case 'kd':
      //获取文件token
      const result = await PortalApi.GetFileToken({fileId: _correctfilekey(file.FileKey)})
      if (result) {
        if (url.indexOf('http:') < 0) {
          url = ''
        }
        if (type === 0) {
          window.open(downLoadUrl + '?FileToken=' + result)
        } else if (type === 1) {
          window.open(url + '/UIBuilder/UIViewer/PreviewPage?FileToken=' + result)
        }
      } else {
        alert('该文件验证失败，暂不能下载，请联系管理员！')
      }
      break

    case 'mini':
      window.open('http://*********:8080/FileStore/Download.aspx?FileId=' + encodeURI(url))
      break

    default:
      if (url) {
        var _alias = file.AliasName
        if (_alias) {
          switch (_alias) {
            case 'attachmentold':
              downLoadFile(url, 2)
              break
            case 'attachmentnew':
              downLoadFile(url, 1)
              break
          }
        } else {
          if (url.indexOf('javascript:') == 0) {
            // |javascript:POBrowser.openWindowModeless('http://***********:8001/PageOffice/ViewPDF.aspx?zxw=1&fileID=262376_员工职业资格取证及证书管理规定（2022年版）.pdf')
            if (url.indexOf('fileID') != -1) {
              let idurl = url.substring(url.indexOf('fileID'))
              if (idurl.indexOf('&') != -1) {
                idurl = idurl.substring(idurl.indexOf('&'))
              } else {
                idurl = idurl.replace("')", '').replace('fileID=', '')
              }
              downLoadFile(idurl, 1)
            }
          } else {
            if (type === 0) {
              if (url.indexOf('/BasicApplication/KindEditor/DownloadFile?path') != -1) {
                url = url.substring(url.indexOf('/BasicApplication'))
                url = baseUrl + url + '&token=' + getAccessToken()
              }
              window.open(url)
            } else if (type === 1) {
              window.open(url)
            } else {
              alert('该文件无法预览，请下载查看！')
            }
          }
        }
      }
      break
  }
}

const downLoadFile = async (fileName, type) => {
  if (parseInt(type) === 1) {
    // var newWindow = window.open()
    //获取文件token
    const result = await PortalApi.GetFileToken({fileId: fileName})
    if (result) {
      // FileApi.downloadFile(result)
      window.open(downLoadUrl + '?FileToken=' + result, "'_self'")
    } else {
      alert('该文件验证失败，暂不能下载，请联系管理员！')
    }
  } else {
    //老的中心网下载
    window.location.href = 'http://10.10.1.29/Portal/Home/download?name=' + escape(fileName)
  }
}
//取消收藏
const CancelSc = function (file) {
  var serverroot00 = 'http://***********:8097/' + 'files/cancelscmyfav/'
  var ev = window.event
  if (!ev) {
    return
  }
  var _fileinfo = [file.FileKey, '', '']
  var _lk = _fileinfo[0]
  var _opfalg = _scgetopflag()

  PortalApi.CancelSc()
  cmaj(
    null,
    'post',
    serverroot00 + 'files/cancelscmyfav/',
    {
      fileKey: encodeURI(_lk),
      userId: wsCache.get(CACHE_KEY.USER).user.id,
      deptId: wsCache.get(CACHE_KEY.USER).user.deptId,
      opflag: JSON.stringify(_opfalg)
    },
    null,
    null,
    function (dat) {
      var _tar = ev.target
      $(_tar).closest('li').remove()
    },
    function (err) {
      console.log(err)
    }
  )
}
const _scgetopflag = () => {
  var rtn = {}
  var _skey = _getquerystring('sKey')
  var _stepkey = _getquerystring('StepKey')
  var _id = _getquerystring('Id') || _getquerystring('ID') || _getquerystring('id')
  var _templetcode = _getquerystring('TempletCode')
  if (_skey) {
    rtn['sKey'] = _skey
  }
  if (_stepkey) {
    rtn['StepKey'] = _stepkey
  }
  if (_id) {
    rtn['Id'] = _id
  }
  if (_templetcode) {
    rtn['TempletCode'] = _templetcode
  }
  return rtn
}
const openMoreFile = () => {
  useAppStore().set_work_menu('myfile') //设置全局变量
  router.push({path: 'myWork'})
}

const _getquerystring = (name, url) => {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)

  // 如果提供了url参数，则从url中解析
  if (arguments.length > 1 && url && typeof url === 'string') {
    var _urlarr = url.split('?')
    if (_urlarr.length > 1) {
      var _search = _urlarr[1]
      r = _search.match(reg)
    }
  }

  if (r != null) return decodeURI(r[2])
  return null
}
</script>
<style scoped lang="scss">
@import url('@/assets/css/default.css');
@import url('@/assets/css/newsCenterMaster.css');

.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}

:deep(.swiper) {
  width: 100%;
  height: 100%;
}

.tabs {
  width: 400px;
  height: 34px;
  display: flex;

  .tab {
    height: 30px;
    line-height: 30px;
    margin-right: 15px;
    cursor: pointer;
    padding: 0 10px;
    font-size: 16px;
  }

  .active {
    color: rgb(255, 255, 255);
    position: relative;
    background: rgb(62, 163, 247);
    //border-bottom: 2px solid rgb(62, 163, 247);
    //color:rgb(62, 163, 247) ;
  }

  .active::after {
    content: '';
    position: absolute;
    left: 43%;
    bottom: -10px;
    transform: rotate(90deg);
    width: 0px;
    height: 0px;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    border-left: 7px solid rgb(62, 163, 247);
  }
}

:deep(.el-tabs__header) {
  margin: 0 !important;
}

:deep(.el-tabs__item) {
  height: 65px;
  font-size: 16px !important;
  font-weight: 700 !important;
}

:deep(.custom-tabs-label) {
  span {
    color: #060001;
  }
}

:deep(.is-active) {
  .custom-tabs-label {
    span {
      color: #409eff;
    }
  }
}

.apply-flow {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-left: 20px;
  border-left: 1px solid #eee;
  font-size: 14px;

  .dept {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .step {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.todo {
  cursor: pointer;
  justify-content: flex-start !important;
  align-items: center !important;
  padding: 10px !important;
  font-size: 14px;
  height: auto !important;

  .title {
    flex: 1;
    align-items: center;
  }

  .cate {
    width: 200px;
  }

  .people-info {
    margin-left: auto;
    width: 374px;
    text-align: right !important;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .dept {
      display: inline-block;
      width: 144px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .name {
      margin-left: 10px;
      width: 70px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-align: left;
    }

    .time {
      margin-left: 10px;
      width: 140px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .info {
    display: flex;
    justify-content: space-between;
    width: 114px;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .icon {
    img {
      vertical-align: middle;
      height: 14px;
      margin-right: 6px;
    }
  }
}

.todo:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;

  .title {
    color: #0070ff !important;
  }

  .cate {
    span {
      color: #0070ff !important;
    }

  }

  .people-info {
    span {
      color: #0070ff !important;
    }
  }

  .date {
    color: #0070ff !important;
  }
}


:deep(.el-tab-pane) {
  height: 332px;
  overflow: auto;
}

.bborder {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 8px;
}

.custom-buttons {
  position: absolute;
  right: 0;
  top: 23px;
  cursor: pointer;

  .work-more {
    color: #939293;
    font-size: 14px;
    border: 1px solid #dcdcdc;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    position: relative;
    cursor: pointer;
    margin-left: auto;
    transition: all 0.3s;
  }

  .work-more:hover {
    color: #3ca1f7;
    border: 1px solid #3ca1f7;
  }

  .work-more:hover:after {
    background: #3ca1f7;
  }

  .work-more:after {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    background: #dcdcdc;
    position: absolute;
    right: -5px;
    top: 4px;
    border: 3px solid #fff;
    transition: all 0.3s;
  }
}

.custom-tabs-label {
  display: flex;
}

:deep(.is-active) {
  .label-1::before {
    content: '';
    width: 20px;
    height: 26px;
    background: url('@/assets/imgs/index_icon_task_active.png') no-repeat center center;
    background-size: auto 70%;
    background-position: 0 2px;
    display: block;
  }

  .label-2::before {
    content: '';
    width: 20px;
    height: 26px;
    background: url('@/assets/imgs/index_icon_taskok_active.png') no-repeat center center;
    background-size: auto 70%;
    background-position: 0 2px;
    display: block;
  }

  .label-3::before {
    content: '';
    width: 20px;
    height: 26px;
    background: url('@/assets/imgs/index_icon_msg_active.png') no-repeat center center;
    background-size: auto 70%;
    background-position: 0 2px;
    display: block;
  }
}

.num {
  box-sizing: border-box;
  min-width: 22px;
  height: 22px;
  padding: 0 5px;
  border-radius: 500px;
  vertical-align: middle;
  background: #1e87f0;
  color: #fff !important;
  font-size: 12px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
  margin-left: 4px;
}

:deep(.el-tabs__header) {
  margin-bottom: 0 !important;
}

.swiper-button-prev,
.swiper-button-next {
  color: white; /* 按钮颜色 */
  background: rgba(0, 0, 0, 0.5); /* 背景色 */
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 20px; /* 箭头大小 */
}

.swiper-button-prev {
  left: 10px;
}

.swiper-button-next {
  right: 10px;
}
</style>
