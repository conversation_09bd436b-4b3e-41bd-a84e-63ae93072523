<template>
  <div class="body">
    <div class="info-list">
      <div style="width: 236px;">
        <div class="uk-card uk-card-small">
          <div class="custom-left-navigation-title">
            <div class="title">资讯导航</div>
            <div class="eng">Information Navigation</div>
          </div>
          <ul class="custom-left-menu-list" style="padding-left: 0;">
            <template v-for="(item, index) in leftMenuDataList" :key="index">
              <el-popover
                placement="right"
                :width="250"
                trigger="hover"
                :disabled="item.SubMenuList.length <= 0"
                :teleported="false">
                <ul class="uk-nav uk-dropdown-nav second-level-nav">
                  <li
                    v-for="(subItem, subIndex) in item.SubMenuList"
                    :key="subIndex"
                    @click="onSecondLevelMenu($event, subItem.Code, index, subIndex, subItem)"
                    style="border-left: none; margin-left: 0">
                    <div
                      :class="subItem.Code === activeSubCode ? 'parentFontColor' : ''"
                      style="font-size: 16px">
                      {{ subItem.CatalogName }}
                    </div>
                  </li>
                </ul>
                <template #reference>
                  <li
                    v-if="item.Code !=='Search'"
                    :class="[item.Code === activeCode ? 'parentActive' : '']"
                    @click="ListPageLog(item)">
                    <div
                      class="title-content"
                      @click="onFirstLevelMenu($event, item.Code, index)">
                      <div
                        :class="[
                            'left-menu-category-name',
                            item.Code === activeCode ? 'parentFontColor' : ''
                          ]">
                        {{ item.CatalogName }}
                      </div>
                      <div class="left-menu-right-icon" v-show="item.SubMenuList.length > 0">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg">
                          <polyline
                            fill="none"
                            stroke="#000"
                            stroke-width="1.03"
                            points="7 4 13 10 7 16"/>
                        </svg>
                      </div>
                      <div class="l"></div>
                    </div>
                  </li>
                </template>
              </el-popover>
            </template>
          </ul>
        </div>
      </div>
      <div style="width: calc(100% - 266px);">
        <my-panel height="auto" :show-more="false" style="border-bottom:0;">
          <template #title>
            <el-breadcrumb separator-icon="ArrowRight" style="display: inline-block">
              <el-breadcrumb-item>首页</el-breadcrumb-item>
              <el-breadcrumb-item>资讯中心</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentLocation }}</el-breadcrumb-item>
            </el-breadcrumb>
          </template>
          <div
            v-loading="show_loading"
            class="uk-card uk-card-small">
            <ul class="content">
              <li
                v-for="(item, index) in list"
                :key="index"
                @click="onGoDetail(item, item.ID, item.OtherWebAddres)">
                <div class="content-li">
                  <div class="uk-text-truncate list-content-title">{{ item.Title }}</div>
                  <div class="content-createtime">{{ item.CreateTime }}</div>
                </div>
              </li>
            </ul>
          </div>
          <div class="uk-card uk-card-small uk-card-body list-page">
            <div class="block">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 30, 40, 50]"
                :page-size="pageSize"
                class="mt-4"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="rightListTotalCount"/>
            </div>
          </div>
        </my-panel>
      </div>
    </div>
  </div>
</template>

<script>
import * as PortalApi from '@/api/system/portal'
import {pageLog} from '@/api/system/pageAndFuncLog'
import $ from 'jquery'
import {searchStore} from '@/store/modules/search'
import myPanel from "@/components/Panel/src/index.vue"

export default {
  components: {
    myPanel
  },
  data() {
    return {
      gSearchStore: searchStore(),
      // requestLeftMenuPath: '/Portal/NewsCenter/GetZXLeftMenu',
      leftMenuDataList: [],
      currentCode: null,
      activeCode: null,
      activeSubCode: '',
      requestRightListCountPath: '/Portal/NewsCenter/GetPublicInfoCount',
      rightListTotalCount: 0,
      // requestRightListPath: '/Portal/NewsCenter/GetPublicInformData',
      list: [],
      pageIndex: 1,
      pageSize: 10,
      keyword: null,
      currentLocation: null,
      popupLoading: null,
      show_loading: false
    }
  },
  watch: {
    deep: true,
    'gSearchStore.searchValue': function (n) {
      this.onSearch(n)
    }
  },
  mounted: function () {
    const route = useRoute()
    // const params = route.params; // 路由参数对象
    // 直接访问URL参数
    const query = route.query // 获取查询字符串
    var code = query.code
    if (code !== undefined || code !== null) {
      this.currentCode = code
      this.activeCode = code
    }

    this.loadLeftData()
    this.loadRightListData()
  },
  methods: {
    loadLeftData: async function () {
      let result = await PortalApi.publicInfoListGetZXLeftMenu()
      var leftMenuList = []
      var leftSubMenuList = []
      result.forEach((item, index) => {
        if (item.Type === 'Parent') {
          leftMenuList.push(item)
        } else if (item.Type === 'child') {
          leftSubMenuList.push(item)
        }
        //确认当前位置
        if (item.Code === this.currentCode) {
          this.currentLocation = item.CatalogName
        }
      })

      leftMenuList.forEach(function (item, index) {
        var subMenuList = []
        leftSubMenuList.forEach(function (subItem, subIndex) {
          if (item.ID === subItem.ParentID) {
            subMenuList.push(subItem)
          }
        })
        item.SubMenuList = subMenuList
      })
      this.leftMenuDataList = leftMenuList
    },

    //加载文章列表
    loadRightListData: async function (isSwitchMenu) {
      // this.loginLoading()
      this.show_loading = true
      if (isSwitchMenu) {
        this.pageIndex = 1
        this.pageSize = 10
      }
      var param = {
        code: this.currentCode,
        page: this.pageIndex,
        pageSize: this.pageSize,
        searchContent: this.keyword
      }
      let result = await PortalApi.publicInfoListGetPublicInformData(param)
      this.rightListTotalCount = parseInt(result.total)
      result.records.forEach((item, index) => {
        if (this.currentCode !== 'TPXW') {
          item.Content = null
        } else {
          item.Content = $(item.Content).text()
        }
        item.CreateTime = this.timestampToTime(item.CreateTime)
      })
      this.list = result.records

      // this.loadRightListTotalCount()
      // this.popupLoading.close()
      this.show_loading = false
    },
    // loadRightListTotalCount: function () {
    //   var param = { code: this.currentCode, searchContent: this.keyword }
    //   $.post(this.requestRightListCountPath, param, function (result) {
    //     this.rightListTotalCount = parseInt(result)
    //   })
    //     .fail(function (response) {
    //       this.$message({
    //         showClose: true,
    //         message: response.responseText,
    //         type: 'error'
    //       })
    //     })
    //     .done(function (response) {
    //       vm.$data.popupLoading.close()
    //     })
    // },
    // 时间戳：1637244864707
    /* 时间戳转换为时间 */
    timestampToTime(timestamp) {
      timestamp = timestamp ? timestamp : null
      let date = new Date(timestamp) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let Y = date.getFullYear() + '-'
      let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
      let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      // return Y + M + D + h + m + s
      return Y + M + D
    },

    handleSizeChange: function (val) {
      this.pageSize = parseInt(val)
      this.loadRightListData()
    },
    handleCurrentChange: function (val) {
      this.pageIndex = parseInt(val)
      this.loadRightListData()
    },
    onFirstLevelMenu: function (e, code, index) {
      //隐藏查询结果
      if (code != 'Search') {
        $('#app .custom-left-menu-bar .custom-left-menu-list')
          .find('li:first-child')
          .css('display', 'none')
        $(".sys_tp_search input[type='text']").val('')
        this.keyword = null
      }
      if (this.leftMenuDataList[index].SubMenuList.length > 0) {
        return false
      }
      this.currentCode = code
      if (this.leftMenuDataList[index].SubMenuList.length <= 0) {
        this.activeCode = code
        this.currentLocation = this.leftMenuDataList[index].CatalogName
      }
      //加载数据
      this.loadRightListData(true)
      //window.location.href = "/Portal/NewsCenter/PublicInfoList?code=" + code;
    },
    onSecondLevelMenu: function (e, code, index, subIndex, subItem) {
      this.currentCode = code
      this.activeCode = this.leftMenuDataList[index].Code
      this.activeSubCode = code
      this.currentLocation = this.leftMenuDataList[index].SubMenuList[subIndex].CatalogName
      //加载数据
      this.loadRightListData(true)
      //window.location.href = "/Portal/NewsCenter/PublicInfoList?code=" + code;
    },
    onGoDetail: function (item, id, otherWebAddres) {
      let detailUrl
      var newWindow = window.open()
      if (this.currentCode === 'SZXW') {
        newWindow.location.href = otherWebAddres
      } else {
        detailUrl =
          '/Portal/NewsCenter/NewsDetail?ID=' +
          id +
          '&Code=' +
          this.currentCode +
          '&navigation=' +
          encodeURI(this.currentLocation) +
          '&pageIndex=' +
          this.pageIndex +
          '&pageSize=' +
          this.pageSize
        newWindow.location.href = detailUrl
      }
      let objdata = {
        pagename: item.Title,
        pageurl: this.currentCode === 'SZXW' ? otherWebAddres : detailUrl,
        tag: '咨询列表'
      }
      const log = pageLog(objdata)
    },
    loginLoading: function () {
      this.popupLoading = this.$loading({
        lock: true,
        text: '数据加载中,请耐心等待...',
        spinner: 'element-icons el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    },
    getQueryString: function (name) {
      let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      let r = window.location.search.substr(1).match(reg)
      if (r != null) {
        return decodeURIComponent(r[2])
      }
      return null
    },
    onSearch: function (e) {
      this.activeCode = 'Search'
      this.currentCode = 'Search'
      this.currentLocation = '查询结果'
      //动态加载查询结果列
      var searchName = $('#app .custom-left-menu-bar .custom-left-menu-list')
        .find('li:first-child .left-menu-category-name')
        .text()
      if (searchName == '查询结果') {
        $('#app .custom-left-menu-bar .custom-left-menu-list')
          .find('li:first-child')
          .css('display', 'list-item')
      }
      this.keyword = e
      this.loadRightListData(true)
    },
    ListPageLog(item) {
      console.log('item===================>', item)
      let objdata = {
        pagename: item.CatalogName,
        pageurl: '/Portal/publicInfoList',
        tag: '咨询列表'
      }
      const log = pageLog(objdata)
    }
  }
}
</script>

<style lang="scss" scoped>
@import url('@/assets/css/uk.min.css');
@import url('@/assets/css/newsCenterMaster.css');
@import url('@/assets/css/uikit.min.css');
@import url('@/assets/css/default.css');

.info-list {
  display: flex;
  gap: 30px;
  margin-top: 12px;
}

.title-content {
  .l {
    background: #d9e0e5;
    width: 190px;
    height: 1px;
    margin-left: 20px;
  }
}

.content {
  padding-left: 0;
  margin-top: 0;
}

.custom-left-menu-bar {
  min-width: 236px;
  /*  position: fixed;
        z-index: 999;*/
}

.custom-left-navigation-title {
  height: 80px;
  padding-top: 15px;
  background-image: url('@/assets/imgs/home/<USER>');
  background-size: 100% 100%;

  .title {
    font-size: 20px;
    line-height: 30px;
    height: 30px;
    width: 190px;
    text-align: center;
    color: #fff;
    background: linear-gradient(to right, #3ca1f7, #51b8f4);
  }

  .eng {
    color: #91dbff;
    font-size: 12px;
    width: 190px;
    text-align: center;
    margin-top: 5px;
    font-weight: bold;
  }
}

.custom-left-navigation-title img {
  width: 30px;
  height: 20px;
  padding-right: 10px;
  margin-bottom: 6px;
}

.custom-left-navigation-title span {
  font-size: 16px;
  color: #333;
}

.custom-left-menu-list {
  background: linear-gradient(to bottom, #fff, #f0f9ff);
  padding-top: 0;
  margin-top: 0;
}

.left-menu-category-name {
  color: #17273a;
  font-size: 16px;
  display: inline-block;
  cursor: pointer;
  width: 180px;
  line-height: 42px;
  margin: 10px 0;
  padding-left: 45px;
}

.left-menu-right-icon {
  display: inline-block;
  width: 10px;
}

.second-level-nav li {
  padding: 10px;
  cursor: pointer;
}

.parentActive {
  position: relative;
}

.parentActive:before {
  width: 4px;
  height: 24px;
  background: #3da3f7;
  position: absolute;
  content: '';
  left: 0;
  top: 19px;
}

.parentFontColor {
  color: rgb(0, 112, 255);
  font-size: 16px !important;
}

.current-location-title {
  position: relative;
  top: -2px;
  font-size: 16px;
}

.list-content-title {
  display: inline-block;
  width: 90%;
  font-size: 16px;
  color: #666;
}

.list-content {
  font-size: 16px;
  color: #666;
  margin: 10px 0;
}

.content-createtime {
  display: inline-block;
  width: 90px;
  font-size: 16px;
  text-align: right;
  color: #999;
}

.list-page {
  margin: 10px 0 150px 0;
}

.custom-right-menu-content {
  margin-bottom: 150px;
}

.header {
  height: 54px;
  border-top: 1px solid #dcdcdc;
  border-bottom: 1px solid #dcdcdc;
  background: #f7f9ff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 28px;

  .title {
    font-size: 18px;
    color: #363636;
  }

  .date {
    font-size: 18px;
    color: #363636;
  }
}

.content-li {
  padding-left: 4px;
  border-bottom: 1px solid #d9d9d9;
  position: relative;
  cursor: pointer;
  height: 54px;
  line-height: 54px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .list-content-title {
    line-height: 54px;
    color: #363636;
  }

  .content-createtime {
    line-height: 54px;
    color: #363636;
  }
}

:deep(.el-breadcrumb__inner) {
  color: #363636 !important;
}

.content-li:hover {
  cursor: pointer;

  .list-content-title {
    color: #3da3f7;
  }

  .content-createtime {
    color: #3da3f7;
  }

}

.uk-dropdown {
  padding: 10px 25px;
}

.el-message {
  margin-top: 80px;
}

.el-breadcrumb > span {
  font-size: 16px;
}
</style>
