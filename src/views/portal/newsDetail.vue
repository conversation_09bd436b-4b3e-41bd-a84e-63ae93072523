<template>
  <div class="uk-section-default">
    <div class="uk-container uk-container-large">
      <div class="uk-grid uk-grid-medium" id="app">
        <div class="uk-width-6-6 custom-right-menu-content">
          <div class="uk-card uk-card-small uk-card-body">
            <el-breadcrumb separator-icon="ArrowRight" style="display: inline-block">
              <el-breadcrumb-item>首页</el-breadcrumb-item>
              <el-breadcrumb-item>资讯中心</el-breadcrumb-item>
              <el-breadcrumb-item>{{ model.Lable }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="uk-card uk-card-small uk-card-body" style="margin: 10px 0 150px 0">
            <template v-if="model != null">
              <div class="text-main border" style="max-width: 1134px">
                <div class="title" style="
                    text-align: center;
                    font-size: 24px;
                    font-family: '宋体';
                    color: #e8343d;
                    font-weight: bold;
                  ">
                  <pre class="qkgt">{{ detail.title }}</pre>
                </div>
                <hr />
                <div class="info" style="margin: 15px; text-align: center">
                  <span class="f" id="Source"
                    style="font-size: 12px; font-family: 宋体; color: #656565; line-height: 28px">
                    来源: {{ detail.unitName }}
                  </span>
                  <span class="f" id="WordsAuthor"
                    style="font-size: 12px; font-family: 宋体; color: #656565; line-height: 28px">
                    作者: {{ detail.senderName }}
                  </span>
                  <span class="f" id="ImageAuthor"
                    style="font-size: 12px; font-family: 宋体; color: #656565; line-height: 28px">
                    创建时间: {{ createTime }}
                  </span>
                  <span class="f" id="ReleaseDate"
                    style="font-size: 12px; font-family: 宋体; color: #656565; line-height: 28px">
                    浏览次数:{{ detail.readCount }}
                  </span>
                  <span class="fontZoom f"><span>【字体：</span><span class="font"
                      @click="fontZoom(1)">大</span><span>&nbsp;</span> <span class="font"
                      @click="fontZoom(2)">中</span><span>&nbsp;</span> <span class="font"
                      @click="fontZoom(3)">小</span><span>&nbsp;</span>
                    <span>】</span>
                  </span>
                </div>
                <div class="text-content news-center-detail-content" id="divContent"
                  style="font-size: 16px; font-family: 微软雅黑; text-indent:32pt" @click="handleProxyClick">
                  <template v-if="detail.lk && 'YNTZ' != model.Code">
                    <div style="margin-left: 60%; text-align: center; line-height: 2em">
                      <ul class="bm" v-html="detail.lk"> </ul>
                    </div>
                  </template>
                </div>
                <div class="text-content" id="detail">
                  <template v-if="detail.attachments || detail.oldAttachments">
                    <div class="uk-heading-bullet" style="font-size: 16px; margin: 10px 0">相关附件</div>
                  </template>
                </div>
                <hr />
                <div id="prevAndNext" class="text-any">
                  <!-- <p id="prev" class="prev" v-if="model.ProviousLocation > 0">{{ model.Previous }}</p>
                  <p id="next" class="next" v-if="model.NextLocation > 0">{{ model.Next }}</p> -->
                  <p id="prev" class="prevAndNext" v-if="model.ProviousLocation > 0"><a :href="preHref">
                      {{ prevText }}
                    </a></p>
                  <p id="next" class="prevAndNext" v-if="model.NextLocation > 0"><a :href="nextHref">
                      {{ nextText }}
                    </a></p>
                </div>
              </div>
            </template>

            <div class="emptybox" v-if="show_loading"
              style="margin-top:10%;margin-bottom: 10%; text-align: center; color: red;">
              <div class="line-scale loading_icon">
                <!-- 背景样式失效，添加加载颜色背景 -->
                <div style="background-color: #888;"></div>
                <div style="background-color: #888;"></div>
                <div style="background-color: #888;"></div>
                <div style="background-color: #888;"></div>
                <div style="background-color: #888;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as PortalApi from '@/api/system/portal'
import { useUserStore } from '@/store/modules/user'
import portal_header from '@/views/portal/components/portal_header.vue'
import portal_bottomer from '@/views/portal/components/portal_bottomer.vue'
import { masterJS } from '@/assets/js/master.js'
import $ from 'jquery'
import { getCurrentInstance } from 'vue'
import * as FileApi from '@/api/infra/file'
import { ElMessageBox } from 'element-plus'
import { getAccessToken } from '@/utils/auth'
// import { POBrowser } from "js-pageoffice";

const model = ref({})
const detail = ref({})
const nextText = ref('')
const prevText = ref('')
const nextHref = ref('')
const preHref = ref('')

const createYear = ref('')
const createMonth = ref('')
const createDay = ref('')
const createHours = ref('')
const createMinutes = ref('')
const createSeconds = ref('')
const createTime = ref('')
const proxy = ref({})

const route = useRoute()
// const params = route.params; // 路由参数对象
// 直接访问URL参数
const query = route.query // 获取查询字符串
//下载路径
const downLoadUrl = import.meta.env.VITE_DOWNLOAD_URL
const baseUrl = import.meta.env.VITE_TOURL_PREFIX

const show_loading = ref(false)

const dateFormat = (time) => {
  if (time != null) {
    const tempTime = new Date(time)
    return `${tempTime.getFullYear()}年${(tempTime.getMonth() + 1).toString().padStart(2, '0')}月${tempTime.getDate().toString().padStart(2, '0')}日`
  } else {
    return null
  }
}

async function getData() {
  show_loading.value = true
  try {
    let result = await PortalApi.newsCenterGetNewsDetail({
      id: query?.ID,
      code: query?.Code,
      navigation: query?.navigation,
      pageIndex: query?.pageIndex == null ? '1' : query?.pageIndex,
      pageSize: query?.pageSize == null ? '10' : query?.pageSize
    })
    Object.assign(model.value, result)
    Object.assign(detail.value, result.detail)
    if (detail.value.lk != null && detail.value.wjdate != 'null') {

      detail.value.lk = detail.value.lk + `<li>` + dateFormat(detail.value.wjdate) + `</li>`
    }

    const tempTime = new Date(detail.value.createTime)
    createTime.value = `${tempTime.getFullYear()}年${(tempTime.getMonth() + 1).toString().padStart(2, '0')}月${tempTime.getDate().toString().padStart(2, '0')}日 ${tempTime.getHours().toString().padStart(2, '0')}:${tempTime.getMinutes().toString().padStart(2, '0')}:${tempTime.getSeconds().toString().padStart(2, '0')}`
    // 获取的数据通过jQuery生成html
    $('#divContent').prepend(detail.value.content)

    if (model.value.ProviousLocation > 0) {
      // $('#prevAndNext').append(`<p class="prev" style="text-indent: 2em !important;">` + model.Previous + `</p>`)
      prevText.value = $(model.value.Previous).text()
      preHref.value = $(model.value.Previous).attr('href') + '&pageIndex=1&pageSize=10'
    }
    if (model.value.NextLocation > 0) {
      // $('#prevAndNext').append(`<p class="next">` + model.Next + `</p>`)
      nextText.value = $(model.value.Next).text()
      nextHref.value = $(model.value.Next).attr('href') + '&pageIndex=1&pageSize=10'
    }
  } finally {
    show_loading.value = false
  }
}

const newsDetailStart = () => {
  const userStore = useUserStore()
  var _si = setInterval(function () {
    if (userStore.getUser.id) {
      var _filesc = new GsFileSc($('#divContent').get(0))
      _filesc.build()
      clearInterval(_si)
    }
  }, 100)

  var _si1 = setInterval(function () {
    if (userStore.getUser.id) {
      var _filesc1 = new GsFileSc($('#detail').get(0))
      _filesc1.build()
      clearInterval(_si1)
    }
  }, 100)
}

const newsDetailEnd = () => {
  var newsCode = '@ViewBag.Code'
  if (newsCode === 'TPXW') {
    $('.text-content img').attr({ width: '600px' })
  } else {
    $('.text-content img').attr({ 'min-width': '600px' })
  }
  //设置所有有img元素为块元素
  $('img').css('display', 'inline-block')
  $('#divContent').find('p,div,span').css({ 'font-size': '14px', color: '#656565' })
  $('#divContent').find('a').css({ 'font-size': '14px', 'text-decoration': 'none' })
  //更改内容的字体
  $('.news-center-detail-content').children().css('font-family', '宋体')
  $('.news-center-detail-content').children().find('span').css('font-family', '宋体')
  var el = $('p')
  if (!el.hasClass('align')) {
    //对图片上的父元素p标签赋属性和值
    el.children().find('img').closest('p').attr('align', 'center')
    //修改图片上的父元素p标签赋属性和值
    el.find('img').parent().css({ 'text-align': 'center', 'text-indent': '21pt' })
    el.children().find('img').closest('p').css({ 'text-align': 'center', 'text-indent': '21pt' })

    //h4为父结点元素时处理逻辑
    $('h4')
      .children()
      .find('img')
      .closest('h4')
      .css({ 'text-align': 'center', 'text-indent': '21pt' })
  }

  //根据QueryString参数名称获取值
  function getQueryString(name) {
    var result = location.search.match(new RegExp('[\?\&]' + name + '=([^\&]+)', 'i'))
    if (result == null || result.length < 1) {
      return ''
    }
    return result[1]
  }
}



const newsDetailFile = () => {
  var nowAttachments = detail.value.attachments
  var oldAttachments = detail.value.oldAttachments

  //显示附件
  var nowHtml = ''
  var oldHtml = ''
  //新
  nowHtml += '<ul style="padding:0;" class="uk-list custom-attachment-list-now">'
  if (nowAttachments !== null && nowAttachments !== '') {
    var nowFileList = nowAttachments.split(',')
    for (var i = 0; i < nowFileList.length; i++) {
      var attachment = nowFileList[i]
      var showAttachmentTitle = attachment.substring(
        attachment.lastIndexOf('\_') + 1,
        attachment.length
      )
      if (attachment !== '') {
        nowHtml +=
          '<li style="padding:0;list-style:none;"><a style="font-size: 14px;" class="custom-attachment-now-' +
          i +
          '" data-attachment=' +
          attachment +
          '>附件' +
          (i + 1) +
          ':' +
          showAttachmentTitle +
          '</a></li>'
      }
    }
  }
  nowHtml += '</ul>'
  $('#detail>.uk-heading-bullet').after(nowHtml)
  //旧
  oldHtml += '<ul style="padding:0;" class="uk-list custom-attachment-list-old">'
  if (oldAttachments !== null && oldAttachments !== '') {
    var oldFileList = oldAttachments.split(',')
    for (var i = 0; i < oldFileList.length; i++) {
      var attachment = oldFileList[i]
      var showAttachmentTitle = attachment.substring(
        attachment.lastIndexOf('\/') + 1,
        attachment.length
      )
      if (attachment !== '') {
        oldHtml +=
          '<li style="padding:0;list-style:none;"><a style="font-size: 14px;" class="custom-attachment-old-' +
          i +
          '" data-attachment=' +
          attachment +
          '>附件' +
          (i + 1) +
          ':' +
          showAttachmentTitle +
          '</a></li>'
      }
    }
  }
  oldHtml += '</ul>'
  $('#detail>.uk-heading-bullet').after(oldHtml)

  $('.custom-attachment-list-now li').each(function (idx, element) {
    $('.custom-attachment-now-' + idx).on('click', function (fileNmae) {
      var fileName = $(this).data('attachment')
      downLoadFile(fileName, 1)
    })
  })

  $('.custom-attachment-list-old li').each(function (idx, element) {
    $('.custom-attachment-old-' + idx).on('click', function (fileNmae) {
      var fileName = $(this).data('attachment')
      downLoadFile(fileName, 2)
    })
  })
}

//下载附件
async function downLoadFile(fileName, type) {
  console.log(fileName)
  if (parseInt(type) === 1) {
    //改为pageoffice查看

    if (fileName.indexOf(".pdf") || fileName.indexOf(".docx") || fileName.indexOf(".xlsx")) {
      let url = baseUrl + "/PageOffice/Preview/PreviewPage?fileID=" + fileName + "&token=" + getAccessToken();
      window.open(url)
    } else {
      //获取文件token
      const result = await PortalApi.GetFileToken({ "fileId": fileName });

      if (result) {
        // FileApi.downloadFile(result)
        // console.log(downLoadUrl + '?FileToken=' + result)
        window.open(downLoadUrl + '?FileToken=' + result)
      } else {
        alert('该文件验证失败，暂不能下载，请联系管理员！')
      }
    }
  } else {
    //老的中心网下载
    window.location.href = 'http://10.10.1.29/Portal/Home/download?name=' + escape(fileName)
  }
}

const fontZoom = (v) => {
  var text_main = $('#app .text-main')[0]
  var currentZoom = parseFloat(window.getComputedStyle(text_main).getPropertyValue('zoom'))

  if (v == 1) {
    text_main.style.zoom = currentZoom + 0.1
  }

  if (v == 2) {
    text_main.style.zoom = 1.2
  }

  if (v == 3) {
    text_main.style.zoom = currentZoom - 0.1
  }

  localStorage.setItem('font-zoom', text_main.style.zoom) //保存当前缩放值
}

// 下载文件
const downloadFile = async (fileName) => {

  const data = await FileApi.downloadFileByName(fileName);
  const parts = fileName.split('_')[1];
  download.downloadFile(data, parts)
}


//修改页面字体和文字颜色
const changeFontAndColor = (el, color) => {
  el.style.fontFamily = '微软雅黑'
  el.style.color = color
  if (el.children != undefined) {
    for (var i = 0; el.children[i] != undefined; i++) {
      changeFontAndColor(el.children[i], color)
    }
  }
}

const newsDetail = () => {
  //初始化页面大小
  var text_main = $('#app .text-main')[0]
  var font_size = localStorage.getItem('font-zoom')

  function isIE11() {
    var userAgent = window.navigator.userAgent
    return userAgent.indexOf('Trident') !== -1 && userAgent.indexOf('rv:11') !== -1
  }

  function isIE() {
    var userAgent = window.navigator.userAgent
    return userAgent.indexOf('Trident') !== -1
  }

  if (isIE()) {
    // IE11 特殊处理代码
    $('.fontZoom').hide()
    text_main.style.zoom = 1.2
  } else {
    // 非 IE11 浏览器代码
    $('.fontZoom').show()

    if (font_size) {
      text_main.style.zoom = localStorage.getItem('font-zoom')
    } else {
      text_main.style.zoom = 1.2
    }
  }

  //修改页面字体和文字颜色
  changeFontAndColor($('#app .text-main')[0], '#444')
  changeFontAndColor($('#divContent')[0], '#444')
  changeFontAndColor($('#app .text-main .title')[0], 'rgb(232, 52, 61)')
}

onMounted(async () => {
  const { proxy } = getCurrentInstance()
  proxy.value = proxy

  await getData()
  newsDetailStart()
  newsDetailEnd()
  newsDetailFile()
  newsDetail()
})
//从父标签里面获取到子标签点击信息
const handleProxyClick = (event) => {
  var target = event.target
  if (target.tagName != "A") {
    return;
  }
  console.log(target)
  if (target.classList.length != 0) {
    if (target.classList[0].indexOf("javascript:POBrowser.openWindowModeless") != -1) {
      // POBrowser.setProxyBaseAPI("/PageOffice");
      eval(target.classList[0])
    }
  }
}

</script>
<style scoped lang="scss">
:deep(.el-breadcrumb__inner) {
  color: #000000 !important;
}
</style>
<style type="text/css">
@import url('@/assets/css/uk.min.css');
/* @import url('@/assets/css/master.css'); */
@import url('@/assets/css/uikit.min.css');

p {
  text-indent: 2em !important;
  font-family: 宋体;
  color: #656565;
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  font-size: 14px !important;
  line-height: 2em;
}

.prevAndNext {
  font-size: 16px !important;
  font-family: 微软雅黑 !important;
  color: rgb(68, 68, 68);
}

p span {
  font-size: 16px !important;
}

.fontZoom {
  font-size: 12px;
  font-family: '宋体';
  color: rgb(101, 101, 101);
  line-height: 28px;
}

.fontZoom span {
  cursor: pointer;
  font-size: 12px !important;
  font-family: '宋体';
}

.fontZoom .font {
  display: inline-block;
  width: 10px;
}

.info .f {
  margin: 0 10px;
}

.qkgt {
  margin: 0;
  padding: 0;
  text-align: center;
  font-size: 24px;
  font-family: '宋体';
  color: #e8343d;
  font-weight: bold;
  background: none;
  border: none;
}

.bm {
  list-style: none;
  font-size: 14px;
}

a {
  color: rgb(68, 68, 68);
}

a:focus,
a:hover {
  text-decoration: underline;
  color: rgb(68, 68, 68);
}
</style>
