<template>
  <div class="container" id="svgContainer">
    <el-card style="width: 98%; margin-top: 20px">
      <template #header>
          <el-menu
            :default-active="activeIndex"
            class="el-menu-demo"
            mode="horizontal"
            text-color="#485365"
            active-text-color="#0975ff"
            @select="handleSelect"
          >
            <el-menu-item v-for="item in dataList" :key="item.id" :index="item.id">
              <span style="font-size: 16px; font-weight: bold;">
              {{item.name}}
              </span>
            </el-menu-item>
          </el-menu>
      </template>
      <table  class="layout-table">
        <thead>
        <tr style="color: #0975ff">
          <th style="border-right: 0">项目阶段</th>
          <th style="border-right: 0; border-left: 0">生命周期</th>
          <th style="border-right: 0; border-left: 0">关键环节</th>
          <th style="border-left: 0">应用模块</th>
        </tr>
        </thead>
        <tbody id="myTableBody" style="position: relative;">
        <tr v-for="(item, index) in items.children" :key="item.id">
          <td :rowspan="items.children.length" v-if="index == 0"  >
            <div :id="'root-node'+ items.id" @click="clickNode(items)" :class="setStyle(items)">{{ items.name }}</div>
          </td>

          <td><div style="z-index: 100" :id="'children-node1:' + item.id" :class="setStyle(item)" @click="clickNode(item)">{{item.name}}</div></td>

          <td width="200px">
            <div  v-for="temp in item.children" :key="temp.id">
              <el-card style="text-align:left; width: 100%; margin-bottom: 20px; position: relative; background-color: transparent;box-shadow: none; border: 0px;" >
                <el-row>
                  <div :id="'children-node2:' + temp.id" style="position: absolute; top: 50%; transform: translateY(-50%);  place-items: center; width: 100%" :class="setStyleItem(temp)" @click="clickNode(temp)">{{temp.name}}</div>
                  <el-col :span="6" v-for="info in temp.children" :key="info.id"  >
                    <el-link  :underline="false" type="primary" @click="openUrl(info.url)" >
                      <div>&nbsp;</div>
                    </el-link>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </td>
          <td width="54%">
            <div style="margin: auto" v-for="temp in item.children" :key="temp.id">
              <el-card :id="'children-node3:' + temp.id" style="text-align:left; width: 100%; margin-bottom: 20px;" v-if="temp.children.length > 0 " >
                <el-row>
                  <el-col style="z-index: 100; margin-left: 5px" :span="5" v-for="info in temp.children" :key="info.id"  >
                    <el-link  :underline="false" type="primary" @click="openUrl(info.url)" >
                      <el-text style="size: 14px; color: #0070ff" type="primary" class="w-100px mb-2" truncated prefix=">>">
                        · &nbsp;{{info.name}}
                      </el-text>
                      <el-text style="size: 14px; color: #0070ff" type="primary" class="w-100px mb-2">
                        >>
                      </el-text>
                    </el-link>
                  </el-col>
                </el-row>
              </el-card>
              <div v-if="temp.children.length == 0 " style="width: 100%; height: 40px; margin-bottom: 20px">
              </div>
            </div>
          </td>

        </tr>
        </tbody>
      </table>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import * as ProjectCenterApi from "@/api/system/portal";
import {searchStore} from "@/store/modules/search";
const mySearchStore = searchStore();
watch(() => mySearchStore.searchValue, (newValue) => {
  loadData(newValue);
});
const loading = ref(true) // 列表的加载中
const dataList = ref([]) //总数据
const items =  ref({}) //当前页面数据
const listThree =  ref([]) //当前页面需要遍历的数据
const activeIndex = ref()
const isRendered = ref(false)
const handleSelect = (key: string, keyPath: string[]) => {
  dataList.value.forEach((item) => {
    if (item.id === key) {
      items.value = item
    }
  })
   nextTick(() => {
     createLine()
   });
}

let searchNode = [];
let searchCount = 0;
let searchValue = '';
let historyNode = [];
let isLoad = true;
const loadData = async (value) => {
  if (!value){
    return;
  }
  const myTableBody = document.getElementById(`myTableBody`);
  const childrenList = myTableBody.querySelectorAll('*');
    //筛选出需要渲染的元素
    if (searchValue != value){
      childrenList.forEach(item => {
        item.classList.remove("search-type1");
        item.classList.remove("search-type");
      })
      //获取当前页面所有节点
      searchNode = [];
      searchCount = 0;
      searchValue = value;
      childrenList.forEach(item => {
        if (item.textContent.includes(value) && item.children.length == 0){
          if (item.tagName.includes("SPAN")){
            item.parentElement.parentElement.parentElement.classList.add("search-type1")
            searchNode.push(item.parentElement.parentElement.parentElement)
          } else {
            item.classList.add("search-type1");
            searchNode.push(item)
          }
        }
      })
      console.log("searchNode", searchNode)
      searchNode[searchCount].classList.remove("search-type1");
      searchNode[searchCount].classList.add("search-type");
      searchNode[searchCount].scrollIntoView({
        behavior: 'auto', // 可以选择 'auto' 或 'smooth'
        block: 'center',     // 可以选择 'start', 'center', 'end', 'nearest'
        inline: 'center'   // 可以选择 'start', 'center', 'end', 'nearest'
      });
    } else {
      for (let i = 0; i < searchNode.length; i++) {
        searchNode[i].classList.add("search-type1");
        if (i == searchCount) {
          searchNode[searchCount].classList.remove("search-type1");
          searchNode[searchCount].classList.add("search-type");
          searchNode[searchCount].scrollIntoView({
            behavior: 'auto', // 可以选择 'auto' 或 'smooth'
            block: 'center',     // 可以选择 'start', 'center', 'end', 'nearest'
            inline: 'center'   // 可以选择 'start', 'center', 'end', 'nearest'
          });
        }
      }
    }
    if (searchCount >= searchNode.length - 1){
      searchCount = 0;
    }else {
      searchCount = searchCount + 1;
    }
}

//获取数据
const getDataList = async () => {
  loading.value = true
  try {
    const data = await ProjectCenterApi.getXmzxUnitInfos()
    dataList.value = data[0]?.children;
    activeIndex.value = data[0]?.children[0].id;
    items.value = data[0]?.children[0];
  } finally {
    loading.value = false
  }
}

const openUrl = (url: string) => {
  window.open(url)
}
const clickNode = (item) => {
  if (item.children.length > 0){
    return
  } else {
    alert('业务功能待建设')
  }
}
const setStyle = (item) => {
  return item.children.length > 0? "bg-color-title1" : "bg-color-title2"
}
const setStyleItem = (item) => {
  return item.children.length > 0? "bg-color-item1" : "bg-color-item2"
}

//创建连线
const createLine = () => {
  const svgLine = document.getElementById("svgLine")
  if (svgLine){
    svgLine.remove();
  }
  //采用svg绘制线条
  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  const myTableBody = document.getElementById(`myTableBody`);
  const tableRect = myTableBody.getBoundingClientRect();
  svg.setAttribute("width", tableRect.width.toString());
  svg.setAttribute("height", tableRect.height.toString());
  svg.setAttribute("id", "svgLine");
  svg.style.position = "absolute";
  svg.style.top = 0;
  svg.style.left = 0;
  svg.style.zIndex = 0;

  // 获取页面的滚动偏移
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  //找到根节点
  const root = document.querySelector(`[id*='root-node']`)
  //找到子节点
  const childrenNode1 = document.querySelectorAll(`[id*='children-node1:']`)
  const childrenNode2 = document.querySelectorAll(`[id*='children-node2:']`)
  const childrenNode3 = document.querySelectorAll(`[id*='children-node3:']`)
  //计算根节点的坐标
  const rootRect = root.getBoundingClientRect();
  const rootX = rootRect.left + scrollLeft - tableRect.left;
  const rootY = rootRect.top + scrollTop - tableRect.top;
  const rootXEnd = rootX + rootRect.width + 10;
  const line = drawDashedLine(rootX + rootRect.width,rootY + rootRect.height / 2, rootXEnd, rootY + rootRect.height / 2)
  svg.appendChild(line);
  //遍历子节点1 画出根节点到子节点的线
  childrenNode1.forEach(item => {
    //计算子节点的坐标
    const childRect = item.getBoundingClientRect();
    const childX = childRect.left + scrollLeft - tableRect.left;
    const childY = childRect.top + scrollTop - tableRect.top;
    //绘制线条
    const line1 = drawDashedLine(rootXEnd, rootY + rootRect.height / 2, rootXEnd, childY + childRect.height / 2);
    const line2 = drawDashedLine(rootXEnd, childY + childRect.height / 2, childX, childY + childRect.height / 2);
    svg.appendChild(line1);
    svg.appendChild(line2);
  })
  //遍历子节点1 画出子节点1 到子节点2的线
  childrenNode1.forEach(item => {
    //计算子节点的坐标
    const child1Rect = item.getBoundingClientRect();
    const child1X = child1Rect.left - tableRect.left;
    const child1Y = child1Rect.top - tableRect.top;
    const child1XEnd = child1X + child1Rect.width + 10;

    //过滤出当前节点下的子节点
    const childNodes = items.value.children.filter(a => item.getAttribute("id").includes(a.id));
    //筛选当前节点下的子节点
    let node2List = [];
    childNodes[0].children.forEach(temp => {
      childrenNode2.forEach(a => {
        if (a.getAttribute("id").includes(temp.id)){
          node2List.push(a);
        }
      })
    })
    //绘制线条
    if (node2List.length > 0) {
      const line = drawDashedLine(child1X + child1Rect.width, child1Y + child1Rect.height / 2, child1XEnd, child1Y + child1Rect.height / 2)
      svg.appendChild(line);
    }

    //画线
    node2List.forEach(temp => {
      const child2Rect = temp.getBoundingClientRect();
      const child2X = child2Rect.left + scrollLeft - tableRect.left;
      const child2Y = child2Rect.top + scrollTop - tableRect.top;
      const line1 = drawDashedLine(child1XEnd, child1Y + child1Rect.height / 2, child1XEnd, child2Y + child2Rect.height / 2);
      const line2 = drawDashedLine(child1XEnd, child2Y + child2Rect.height / 2, child2X, child2Y + child2Rect.height / 2);
      svg.appendChild(line1);
      svg.appendChild(line2);
    })
  })
  //遍历子节点2 画出子节点2到子节点3的线
  childrenNode2.forEach(item => {
    //计算子节点的坐标
    const child2Rect = item.getBoundingClientRect();
    const child2X = child2Rect.right + scrollLeft - tableRect.left;
    const child2Y = child2Rect.top + scrollTop - tableRect.top;
    childrenNode3.forEach(temp => {
      const child3Rect = temp.getBoundingClientRect();
      const child3X = child3Rect.left + scrollLeft - tableRect.left;
      if (item.getAttribute("id").includes(temp.getAttribute("id").replace("children-node3:",""))){
        const line1 = drawDashedLine(child2X, child2Y + child2Rect.height / 2, child3X,child2Y + child2Rect.height / 2);
        svg.appendChild(line1);
      }

    })
  })

  myTableBody.appendChild(svg)
}
//绘制线条
const drawDashedLine = (x1, y1, x2, y2) => {
  const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
  line.setAttribute("x1", x1);
  line.setAttribute("y1", y1);
  line.setAttribute("x2", x2);
  line.setAttribute("y2", y2);
  //虚线样式
  line.setAttribute("stroke", "#757373");
  line.setAttribute("stroke-width", "0.5");
  line.setAttribute("stroke-dasharray", `8 3`);

  return line;
}
const debounce = (func, delay) => {
  let timeoutId;
  return function () {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, arguments), delay);
  };
}
const handleResize = debounce(() => {
    createLine();
  });
onMounted(async () => {
  await getDataList()
  createLine();
  window.addEventListener('resize', handleResize);
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
})
</script>

<style lang="scss" scoped>
.search-type{
  background: linear-gradient(90deg, rgba(255, 255, 0, 0.9) 0%, rgba(255, 255, 0, 0.9) 100%) !important;
  color: #444 !important;
}
.search-type1{
  background: linear-gradient(90deg, rgba(255, 255, 0, 0.3) 0%, rgba(255, 255, 0, 0.3) 100%) !important;
  color: #444 !important;
}
.container {
  width: 80%;
  margin: 0 auto;
  margin-top: 71px;
  background-color: #ffffff;
}

.layout-table {
  width: 100%;
  border-collapse: collapse;
}

.layout-table th,
.layout-table td {
  padding: 8px;
  text-align: center;
  border: 1px solid #0070ff;
}

.layout-table tr:nth-child(even) {
  background-color: #f2f2f2;
  text-align: center;
}

.el-menu-demo {
  display: flex; /* 使内部元素成为 flex 子元素 */
  justify-content: center; /* 水平居中子元素 */
  align-items: center; /* 垂直居中子元素 */
  border-radius: 5px;
  border: none;
  z-index: 100;
}

.el-menu-demo .el-menu-item {
  margin: 0 10px; /* 添加左右边距以增加间距 */
}
:deep(.el-card__header) {
  padding: 0px !important;
}
.bg-color-title1 {
  background: linear-gradient(to right,#4898ff 0%, #0975ff 100%);
  padding: 6px;
  border-radius: 20px;
  color: #f2f2f2;
  font-size: 16px;
  text-align: center;
  width: 84%;
  z-index: 100;
}
.bg-color-title2 {
  background: linear-gradient(to right, #c3ddff 0%, #9ec7ff 100%);
  padding: 6px;
  border-radius: 20px;
  color: #f2f2f2;
  font-size: 16px;
  text-align: center;
  width: 84%;
  z-index: 100;

}
.bg-color-title2:hover {
  cursor: pointer;
  z-index: 100;
}

.bg-color-item1 {
  background: linear-gradient(to right, #6dabff 0%, #5590ff 100%);
  padding: 6px;
  border-radius: 20px;
  color: #f2f2f2;
  text-align: center;
  z-index: 100;
}
.bg-color-item2 {
  background: linear-gradient(to right, #9ec7ff 0%, #6dabff 100%);
  padding: 6px;
  border-radius: 20px;
  color: #f2f2f2;
  text-align: center;
  width: 100%;
  z-index: 100;
}
.bg-color-item2:hover {
  cursor: pointer;
}
</style>
