<template>
  <Dialog class="cardHeight" v-model="dialogVisible" title="回复消息" width="90%" top="5%" :max-height="600" :scroll=true >
    <div style="display: flex; justify-content: space-between;">
      <div>
        <el-select
          multiple
          placeholder="常用联系人"
          style="width: 240px"
        >
          <el-option
            v-for="item in normalLinkMan"
            :key="item.ID"
            :label="item.Name"
            :value="item.ID"
          />
        </el-select>
        <el-checkbox style="margin-left: 30px" label="关联下级" size="large" />
        <el-checkbox style="margin-left: -20px" label="显示在线" size="large" />
      </div>
        <div>
          <el-input style="width: 200px" placeholder="请输入用户名查询"/>
          <span style="margin-left: 20px">红色：在线  黑色：离线</span>
        </div>
    </div>

    <el-divider />
    <el-form>
      <el-form-item>
        <el-table-v2
          :columns="columns"
          :data="treeData"
          :width="1300"
          :expand-column-key="expandColumnKey"
          :height="300"
          fixed
          @row-expand="onRowExpanded"
          @expanded-rows-change="onExpandedRowsChange"
        />
      </el-form-item>
      <el-divider />
      <el-form-item>
        <el-checkbox label="重要" size="large" />
        <el-checkbox style="margin-left: -20px" label="已读回执" size="large" />
        <el-button style="margin-left: 30px">发送</el-button>
        <a style="margin-left: 20px">共 0 个接收人</a>
      </el-form-item>
      <el-form-item label="附件" prop="content">
        <UploadFile
          :limit="1"
          class="min-w-80px"
        />
      </el-form-item>
      <el-form-item prop="content">
        <Editor height="150px" />
      </el-form-item>

    </el-form>
  </Dialog>
</template>
<script lang="ts" setup>
import * as msgApi from "@/api/portal/msgList";
import type {Column,ExpandedRowsChangeHandler, RowExpandHandler} from 'element-plus'
import {ElCheckbox} from 'element-plus'
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false)
const formLoading = ref(false) // 表单的加载中：提交的按钮禁用
const treeData = ref([]) // 树数据
const normalLinkMan = ref([]) // 常用联系人
const formData = ref({}) //表单数据
const formRef = ref() // 表单 Ref
const formRules = reactive({ //表单校验
  ip: [{ required: true, message: 'IP 地址不能为空', trigger: 'blur' }]
})
const columns: Column<any>[] = [
  { key: 'column-0',
    dataKey: 'name',
    title: '组织名称',
    width: 400,
    cellRenderer: ({ cellData: cellData }) => {
      if (cellData){
        return h(ElCheckbox, { type: 'success' }, cellData);
      }
    }
  },
  { key: 'id',
    dataKey: 'userFields',
    title: '人员',
    width: 800,
    cellRenderer: ({ cellData: cellData }) => {
    if (cellData){
    return h('div', { class: 'checkbox-row' }, cellData.map(item => h(ElCheckbox, { type: 'success' }, item.Name)))
    }
    }
  },
];

const open = () => {
  getNormalLinkManTree();
  getOrgTree();
  dialogVisible.value = true;
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    //formData.value.result = await AreaApi.getAreaByIp(formData.value.ip!.trim())
    message.success('查询成功')
  } finally {
    formLoading.value = false
  }
}

const expandColumnKey = 'column-0'


const getOrgTree = async () =>{
  let params = {};
  treeData.value = await msgApi.getOrgTree(params)
}
const getNormalLinkManTree = async () =>{
  let params = {};
  normalLinkMan.value = await msgApi.getNormalLinkManTree(params)
}

const expandedRowKeys = ref<string[]>([])

const onRowExpanded = ({ expanded }: Parameters<RowExpandHandler<any>>[0]) => {
  console.log('Expanded:', expanded)
}

const onExpandedRowsChange = (
  expandedKeys: Parameters<ExpandedRowsChangeHandler>[0]
) => {
  console.log(expandedKeys)
}
</script>

<style lang="scss" scoped>
.checkbox-row {
  display: flex;
  flex-wrap: wrap; /* 允许子元素换行 */
  max-width: calc(100% / 6 * 6); /* 假设每个复选框占用 1/8 的宽度 */
}
.checkbox-row .el-checkbox {
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
