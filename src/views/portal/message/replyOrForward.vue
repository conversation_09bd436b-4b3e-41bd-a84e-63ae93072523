<template>
  <Dialog class="cardHeight" v-model="dialogVisible" title="回复消息" width="90%" top="5%" :max-height="600" :scroll=true>

    <el-button @click="submitForm">发送</el-button>
    <el-button @click="dialogVisible = false">取消</el-button>
    <el-checkbox style="margin-left: 30px" label="重要" size="large" />
    <el-checkbox label="已读回执" size="large" />
    <el-divider />
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules">
      <el-form-item label="接收人">
        <el-input clearable v-model="formData.ReceiverNames">
          <template #append>
            <el-button @click="openReceiverMan()">...</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="接收部门">
        <el-input clearable v-model="formData.ReceiverDeptNames">
          <template #append>
            <el-button @click="openReceiverOrg()">...</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="公告内容">
        <Editor height="150px" v-model="formData.Content" />
      </el-form-item>
      <el-form-item label="附件">
        <el-upload
        v-model:file-list="fileList"
        class="upload-demo"
        drag
        action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
        multiple
        >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          点击或拖动文件到此上传
        </div>
        <template #tip>
          <div class="el-upload__tip">
            jpg/png files with a size less than 500kb
          </div>
        </template>
        </el-upload>
      </el-form-item>
    </el-form>
  </Dialog>
  <ReceiverMan ref="receiverManRef" @custom-event="setReceiverMan"/>
  <ReceiverOrg ref="receiverOrgRef" @custom-event="setReceiverOrg"/>
</template>
<script lang="ts" setup>
import ReceiverMan from './receiverMan.vue'
import ReceiverOrg from "@/views/portal/message/receiverOrg.vue";
import * as msgApi from "@/api/portal/msgList";
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false)
const formLoading = ref(false) // 表单的加载中：提交的按钮禁用
const receiverManRef = ref(); // 接收人弹窗
const receiverOrgRef = ref(); // 接收部门
const fileList = ref([
  {
    name: 'food.jpeg',
    url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
  },
  {
    name: 'food2.jpeg',
    url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
  },
])
const formData = ref({
  AlreadyRead: '',
  AttachFileIDs: '',
  Content: '',
  ContentText: '',
  FirstViewTime: '',
  ID: '',
  Importance: '',
  IsDeleted: '',
  IsRemind: '',
  IsSystemMsg: '',
  LinkUrl: '',
  ParentID: '',
  ReceiverID: '',
  ReceiverIDs: '',
  ReceiverNames: '',
  ReceiverDeptIDs: '',
  ReceiverDeptNames: '',
  SendTime: '',
  SenderID: '',
  SenderName: '',
  Title: '',
  Type: '',
  UserID: ''
});
const formRules = reactive({ //表单校验
})
const formRef = ref() // 表单 Ref
const open = (row) => {
  formData.value = row;
  dialogVisible.value = true;
}
const setReceiverMan = (data) => {
  if (data) {
    if (!formData.value.ReceiverIDs) {
      formData.value.ReceiverNames = '';
      formData.value.ReceiverIDs = '';
    }
    formData.value.ReceiverNames = formData.value.ReceiverNames + data.map((item) => {
      if (!formData.value.ReceiverNames.includes(item.name)) {
        return item.name;
      }
    }) + ',';
    formData.value.ReceiverIDs = formData.value.ReceiverIDs + data.map((item) => {
      if (!formData.value.ReceiverIDs.includes(item.id)) {
        return item.id;
      }
    }) + ',';
    console.log("人员id",formData.value.ReceiverDeptIDs)

  }
}
const setReceiverOrg = (data) => {

  if (data) {
    if (!formData.value.ReceiverDeptIDs) {
      formData.value.ReceiverDeptNames = '';
      formData.value.ReceiverDeptIDs = '';
    }
    formData.value.ReceiverDeptNames +=  data.map((item) => {
      if (!formData.value.ReceiverDeptNames.includes(item.name)) {
        return item.name;
      }
    }) + ',';

    formData.value.ReceiverDeptIDs = formData.value.ReceiverDeptIDs + data.map((item) => {
      if (!formData.value.ReceiverDeptIDs.includes(item.id)) {
        return item.id;
      }
    }) + ',';
  }
}
const openReceiverMan = async () => {
  if (receiverManRef.value) {
    receiverManRef.value.open();
  }
}
const openReceiverOrg = async () => {
  if (receiverOrgRef.value) {
    receiverOrgRef.value.open();
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    //formData.value.result = await AreaApi.getAreaByIp(formData.value.ip!.trim())
    const data = msgApi.saveMsg(formData.value)
    message.success('操作成功')
  } finally {
    formLoading.value = false
    dialogVisible.value = false;
  }
}
</script>
<style lang="scss" scoped>
.upload-demo {
  display: block;
  padding: 0 !important;
  width: 100%;
  margin-top: 20px;
}
.el-upload-dragger{
  height: 0px !important;
}
.el-icon--upload{
  height: 0px !important;
  padding: 0px !important;
}
.el-upload__text{
  padding: 0px !important;
  margin-top: -30px !important;
}
</style>
