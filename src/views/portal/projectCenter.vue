<template>
  <div class="body" id="svgContainer">
    <my-panel :is-icon="false" :title="''" :height="'auto'" :show-more="false"
              class="project-center">
      <template #subtitle>
        <div style="width: 1130px;position: relative;">
          <el-menu
            :default-active="activeIndex"
            class="el-menu-demo"
            mode="horizontal"
            text-color="#485365"
            active-text-color="#1890ff"
            @select="handleSelect">
            <el-menu-item v-for="item in dataList" :key="item.id" :index="item.id">
              <span style="font-size: 16px; font-weight: bold;">
              {{ item.name }}
              </span>
            </el-menu-item>
          </el-menu>
        </div>
      </template>
      <div class="layout-table">
        <div id="myTableBody">
          <div class="content-title">
            <div class="col-box">
              <div class="text">项目中心</div>
            </div>
            <div class="col-box">
              <div class="text">生命周期</div>
            </div>
            <div class="col-box">
              <div class="text">关键环节</div>
            </div>
            <div class="col-box">
              <div class="text">应用模块</div>
            </div>
          </div>
          <div class="content-row">
            <div
              v-if="items.children"
              :id="'root-node'+ items.id"
              @click="clickNode(items)"
              class="level1-column"
              :class="setStyle(items)">
              {{ items.name }}
            </div>
            <div style="flex: 1;z-index: 1000;">
              <div class="row-list" v-for="(item, index) in items.children" :key="item.id">
                <div
                  :key="index"
                  :id="'children-node1:' + item.id"
                  class="level2-column"
                  :class="setStyle(item)"
                  @click="clickNode(item)">
                  {{ item.name }}
                </div>
                <div class="level3-column" :key="index">
                  <div class="level3-column-row" v-for="temp in item.children" :key="temp.id">
                    <div style="width: 200px;">
                      <el-card shadow="never" class="level3-card">
                        <el-row>
                          <div
                            :id="'children-node2:' + temp.id"
                            class="level3-column-item"
                            :class="setStyleItem(temp)"
                            @click="clickNode(temp)">
                            {{ temp.name }}
                          </div>
                          <el-col :span="6" v-for="info in temp.children" :key="info.id">
                            <el-link :underline="false" type="primary" @click="openUrl(info.url)">
                              <div>&nbsp;</div>
                            </el-link>
                          </el-col>
                        </el-row>
                      </el-card>
                    </div>
                    <div
                      :id="'children-node3:' + temp.id"
                      class="level4-column-list"
                      v-if="temp.children.length > 0 ">
                      <div
                        class="c5btn"
                        v-for="info in temp.children"
                        @click="openUrl(info.url)"
                        :key="info.id">
                        <img v-if="info.icon" :src="info.icon" class="icon"/>
                        <img v-else src="@/assets/imgs/home/<USER>" class="icon"/>
                        <div class="text">
                          <div class="tit">{{ info.name }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </my-panel>
  </div>
</template>

<script lang="ts" setup>
import * as ProjectCenterApi from "@/api/system/portal";
import {searchStore} from "@/store/modules/search";
import MyPanel from "@/components/Panel/src/index.vue";

const mySearchStore = searchStore();
watch(() => mySearchStore.searchValue, (newValue) => {
  loadData(newValue);
});
const loading = ref(true) // 列表的加载中
const dataList = ref([]) //总数据
const items = ref({}) //当前页面数据
const activeIndex = ref()
const handleSelect = (key: string, keyPath: string[]) => {
  dataList.value.forEach((item) => {
    if (item.id === key) {
      items.value = item
    }
  })
  nextTick(() => {
    createLine()
  });
}

let searchNode = [];
let searchCount = 0;
let searchValue = '';
const loadData = async (value) => {
  if (!value) {
    return;
  }
  const myTableBody = document.getElementById(`myTableBody`);
  const childrenList = myTableBody.querySelectorAll('*');
  //筛选出需要渲染的元素
  if (searchValue != value) {
    childrenList.forEach(item => {
      item.classList.remove("search-type1");
      item.classList.remove("search-type");
    })
    //获取当前页面所有节点
    searchNode = [];
    searchCount = 0;
    searchValue = value;
    childrenList.forEach(item => {
      if (item.textContent.includes(value) && item.children.length == 0) {
        if (item.tagName.includes("SPAN")) {
          item.parentElement.parentElement.parentElement.classList.add("search-type1")
          searchNode.push(item.parentElement.parentElement.parentElement)
        } else {
          item.classList.add("search-type1");
          searchNode.push(item)
        }
      }
    })
    console.log("searchNode", searchNode)
    searchNode[searchCount].classList.remove("search-type1");
    searchNode[searchCount].classList.add("search-type");
    searchNode[searchCount].scrollIntoView({
      behavior: 'auto', // 可以选择 'auto' 或 'smooth'
      block: 'center',     // 可以选择 'start', 'center', 'end', 'nearest'
      inline: 'center'   // 可以选择 'start', 'center', 'end', 'nearest'
    });
  } else {
    for (let i = 0; i < searchNode.length; i++) {
      searchNode[i].classList.add("search-type1");
      if (i == searchCount) {
        searchNode[searchCount].classList.remove("search-type1");
        searchNode[searchCount].classList.add("search-type");
        searchNode[searchCount].scrollIntoView({
          behavior: 'auto', // 可以选择 'auto' 或 'smooth'
          block: 'center',     // 可以选择 'start', 'center', 'end', 'nearest'
          inline: 'center'   // 可以选择 'start', 'center', 'end', 'nearest'
        });
      }
    }
  }
  if (searchCount >= searchNode.length - 1) {
    searchCount = 0;
  } else {
    searchCount = searchCount + 1;
  }
}

//获取数据
const getDataList = async () => {
  loading.value = true
  try {
    const data = await ProjectCenterApi.getXmzxUnitInfos()
    dataList.value = data[0]?.children;
    activeIndex.value = data[0]?.children[0].id;
    items.value = data[0]?.children[0];
  } finally {
    loading.value = false
  }
}

const openUrl = (url: string) => {
  window.open(url)
}
const clickNode = (item) => {
  if (item.children.length > 0) {
    return
  } else {
    alert('业务功能待建设')
  }
}
const setStyle = (item) => {
  return item.children.length > 0 ? "bg-color-title1" : "bg-color-title2"
}
const setStyleItem = (item) => {
  return item.children.length > 0? "bg-color-item1" : "bg-color-item2"
}

//创建连线
const createLine = () => {
  const svgLine = document.getElementById("svgLine")
  if (svgLine){
    svgLine.remove();
  }
  //采用svg绘制线条
  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  const myTableBody = document.getElementById(`myTableBody`);
  const tableRect = myTableBody.getBoundingClientRect();
  svg.setAttribute("width", tableRect.width.toString());
  svg.setAttribute("height", tableRect.height.toString());
  svg.setAttribute("id", "svgLine");
  svg.style.position = "absolute";
  svg.style.top = 0;
  svg.style.left = 0;
  svg.style.zIndex = 0;

  // 获取页面的滚动偏移
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  //找到根节点
  const root = document.querySelector(`[id*='root-node']`)
  //找到子节点
  const childrenNode1 = document.querySelectorAll(`[id*='children-node1:']`)
  const childrenNode2 = document.querySelectorAll(`[id*='children-node2:']`)
  const childrenNode3 = document.querySelectorAll(`[id*='children-node3:']`)
  //计算根节点的坐标
  const rootRect = root.getBoundingClientRect();
  const rootX = rootRect.left + scrollLeft - tableRect.left;
  const rootY = rootRect.top + scrollTop - tableRect.top;
  const rootXEnd = rootX + rootRect.width + 10;
  const line = drawDashedLine(rootX + rootRect.width, rootY + rootRect.height / 2, rootXEnd, rootY + rootRect.height / 2)
  svg.appendChild(line);
  //遍历子节点1 画出根节点到子节点的线
  childrenNode1.forEach(item => {
    //计算子节点的坐标
    const childRect = item.getBoundingClientRect();
    const childX = childRect.left + scrollLeft - tableRect.left;
    const childY = childRect.top + scrollTop - tableRect.top;
    //绘制线条
    const line1 = drawDashedLine(rootXEnd, rootY + rootRect.height / 2, rootXEnd, childY + childRect.height / 2);
    const line2 = drawDashedLine(rootXEnd, childY + childRect.height / 2, childX, childY + childRect.height / 2);
    svg.appendChild(line1);
    svg.appendChild(line2);
  })
  //遍历子节点1 画出子节点1 到子节点2的线
  childrenNode1.forEach(item => {
    //计算子节点的坐标
    const child1Rect = item.getBoundingClientRect();
    const child1X = child1Rect.left - tableRect.left;
    const child1Y = child1Rect.top - tableRect.top;
    const child1XEnd = child1X + child1Rect.width + 10;

    //过滤出当前节点下的子节点
    const childNodes = items.value.children.filter(a => item.getAttribute("id").includes(a.id));
    //筛选当前节点下的子节点
    let node2List = [];
    childNodes[0].children.forEach(temp => {
      childrenNode2.forEach(a => {
        if (a.getAttribute("id").includes(temp.id)) {
          node2List.push(a);
        }
      })
    })
    //绘制线条
    if (node2List.length > 0) {
      const line = drawDashedLine(child1X + child1Rect.width, child1Y + child1Rect.height / 2, child1XEnd, child1Y + child1Rect.height / 2)
      svg.appendChild(line);
    }

    //画线
    node2List.forEach(temp => {
      const child2Rect = temp.getBoundingClientRect();
      const child2X = child2Rect.left + scrollLeft - tableRect.left;
      const child2Y = child2Rect.top + scrollTop - tableRect.top;
      const line1 = drawDashedLine(child1XEnd, child1Y + child1Rect.height / 2, child1XEnd, child2Y + child2Rect.height / 2);
      const line2 = drawDashedLine(child1XEnd, child2Y + child2Rect.height / 2, child2X, child2Y + child2Rect.height / 2);
      svg.appendChild(line1);
      svg.appendChild(line2);
    })
  })
  //遍历子节点2 画出子节点2到子节点3的线
  childrenNode2.forEach(item => {
    //计算子节点的坐标
    const child2Rect = item.getBoundingClientRect();
    const child2X = child2Rect.right + scrollLeft - tableRect.left;
    const child2Y = child2Rect.top + scrollTop - tableRect.top;
    childrenNode3.forEach(temp => {
      const child3Rect = temp.getBoundingClientRect();
      const child3X = child3Rect.left + scrollLeft - tableRect.left;
      const child3Y = child3Rect.top + scrollTop - tableRect.top;
      if (item.getAttribute("id").includes(temp.getAttribute("id").replace("children-node3:", ""))) {
        const line1 = drawDashedLine(child2X - 26, child2Y + child2Rect.height / 2, child3X, child2Y + child2Rect.height / 2);
        svg.appendChild(line1);
      }
    })
  })

  myTableBody.appendChild(svg)
}
//绘制线条
const drawDashedLine = (x1, y1, x2, y2) => {
  const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
  line.setAttribute("x1", x1);
  line.setAttribute("y1", y1);
  line.setAttribute("x2", x2);
  line.setAttribute("y2", y2);
  //虚线样式
  line.setAttribute("stroke", "#1890ff");
  line.setAttribute("stroke-width", "1");
  line.setAttribute("stroke-dasharray", `6 4`);

  return line;
}
const debounce = (func, delay) => {
  let timeoutId;
  return function () {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, arguments), delay);
  };
}
const handleResize = debounce(() => {
  createLine();
});
onMounted(async () => {
  await getDataList()
  createLine();
  window.addEventListener('resize', handleResize);
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
})
</script>

<style lang="scss" scoped>
@import url('@/assets/css/default.css');

.project-center {
  :deep(.panel-title) {
    border-bottom: 0 !important;
  }
}

.row-list {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

#myTableBody {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 50%, #e6f7ff 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
  }
}

.content-title {
  display: flex;
  gap: 20px;
  align-items: center;
  height: 50px;

  .col-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
      width: 1px;
      height: 24px;
      background: linear-gradient(180deg, rgba(24, 144, 255, 0.6) 80%, rgba(24, 144, 255, 0.6) 80%, transparent 100%);
    }

    .text {
      font-size: 16px;
      font-weight: 600;
      color: #1890ff;
      text-shadow: 0 1px 2px rgba(24, 144, 255, 0.1);
      transition: all 0.3s ease;

      &:hover {
        color: #096dd9;
        transform: translateY(-1px);
      }
    }
  }

  .col-box:nth-child(1) {
    width: 144px;
  }

  .col-box:nth-child(2) {
    width: 144px;
  }

  .col-box:nth-child(3) {
    width: 200px;
  }

  .col-box:nth-child(4) {
    flex: 1;
  }
}

.content-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.level1-column {
  width: 120px;
}

.level2-column {
  z-index: 100;
  width: 120px;
}

.level3-column {
  flex: 1;

  .level3-column-row {
    display: flex;
    gap: 20px;
  }

  .level4-column-list {
    flex: 1;
    margin-left: 20px;
    padding: 15px;
    display: flex;
    align-items: center;
    flex-flow: wrap;
    gap: 15px;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #f8fbff 0%, #f0f9ff 100%);
    border-radius: 12px;
    border: 1px solid rgba(24, 144, 255, 0.1);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.05);
  }

  .level3-card {
    text-align: left;
    width: 100%;
    margin-bottom: 20px;
    position: relative;
    background-color: transparent;
    box-shadow: none;
    border: 0;

    .level3-column-item {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      place-items: center;
      width: 100%
    }
  }
}

.level4-column {
  flex: 1;
}

#myTableBody {
  position: relative;
}

.search-type {
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.8) 0%, rgba(64, 169, 255, 0.8) 100%) !important;
  color: #ffffff !important;
  border: 2px solid #1890ff !important;
  box-shadow: 0 0 12px rgba(24, 144, 255, 0.5) !important;
}

.search-type1 {
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.2) 0%, rgba(64, 169, 255, 0.2) 100%) !important;
  color: #1890ff !important;
  border: 1px solid rgba(24, 144, 255, 0.3) !important;
}


.el-menu-demo {
  justify-content: center; /* 水平居中子元素 */
  align-items: center; /* 垂直居中子元素 */
  border: none;
  top: 5px;

  :deep(.is-active) {
    z-index: 100;
    border-bottom: 4px solid #0072ff;
    background: #ecf5ff;
  }
}

.el-menu-demo .el-menu-item {
  margin: 0 10px; /* 添加左右边距以增加间距 */
}

// 重写 c5btn 样式以匹配图片中的圆角按钮样式
.level4-column-list .c5btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 18px;
  padding: 8px 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

  &:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    border-color: rgba(24, 144, 255, 0.4);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
    transform: translateY(-1px);
  }

  //.icon {
  //  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
  //}

  .text {
    .tit {
      color: #1890ff;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

:deep(.el-card__header) {
  padding: 0px !important;
}

.bg-color-title1 {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 50%, #0050b3 100%);
  padding: 8px 12px;
  border-radius: 24px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  z-index: 100;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.bg-color-title2 {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 50%, #237804 100%);
  padding: 8px 12px;
  border-radius: 24px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  z-index: 100;
  box-shadow: 0 3px 8px rgba(82, 196, 26, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.bg-color-title2:hover {
  cursor: pointer;
  z-index: 100;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
  transition: all 0.3s ease;
}

.bg-color-item1 {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 50%, #096dd9 100%);
  padding: 8px 16px;
  border-radius: 20px;
  color: #ffffff;
  font-weight: 500;
  text-align: center;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.bg-color-item2 {
  background: linear-gradient(135deg, #73d13d 0%, #52c41a 50%, #389e0d 100%);
  padding: 8px 16px;
  border-radius: 20px;
  color: #ffffff;
  font-weight: 500;
  text-align: center;
  width: 100%;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.bg-color-item1:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.bg-color-item2:hover {
  cursor: pointer;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
}

/* 为主标题添加悬停效果 */
.bg-color-title1:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  transition: all 0.3s ease;
}

/* 优化链接样式 */
.el-link {
  transition: all 0.3s ease;
}

.el-link:hover {
  transform: translateX(3px);
}
</style>
