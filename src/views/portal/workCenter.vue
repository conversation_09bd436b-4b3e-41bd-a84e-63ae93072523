<template>
  <div class="portal_page">
    <div class="page-work" id="WorkCenter">
      <div class="uk-container uk-container-large uk-container-center">
        <div class="uk-grid uk-grid-medium">
          <div class="siderbar">
            <ul>
              <li class="p_item">
                <a class="m_a" :href="toIndex()" title="回到办公中心">
                  <img class="icon" src="@/assets/icons/portal/icon-mywork.png" />
                  <span class="text">我的工作</span>
                </a>

                <div class="ul_sub">
                  <router-link
                    :class="menu_actived == 'newtask' ? 'actived' : ''"
                    :to="'myWork'"
                    @click="db_open('newtask')"
                    >我的待办</router-link
                  >
                  <router-link
                    :class="menu_actived == 'completetask' ? 'actived' : ''"
                    :to="'myWork'"
                    @click="db_open('completetask')"
                    >我的已办</router-link
                  >
                  <router-link
                    :class="menu_actived == 'apply' ? 'actived' : ''"
                    :to="'myWork'"
                    @click="db_open('apply')"
                    >我的申请</router-link
                  >
                  <router-link
                    :class="menu_actived == 'myfile' ? 'actived' : ''"
                    :to="'myWork'"
                    @click="db_open('myfile')"
                    >我的文件</router-link
                  >
                  <router-link
                    :class="menu_actived == 'focus' ? 'actived' : ''"
                    :to="'myWork'"
                    @click="db_open('focus')"
                    >我的关注</router-link
                  >
                </div>
              </li>

              <li class="p_item">
                <a :class="is_open ? 'open m_a expend' : 'm_a expend'" href="#" @click="menu_click">
                  <img class="icon" src="@/assets/icons/portal/icon-cyrk.png" />
                  <span class="text">点击排行</span>
                </a>

                <ul class="ul_sub ul_sub_sort" :style="{ height: is_open ? '360px' : 0 }">
                  <li v-for="(dm, didx) in CYRKList" :key="didx">
                    <a target="_blank" v-text="dm.FuncName" @click="handelhref(dm.Url, dm)"></a>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as PortalApi from '@/api/system/portal'
import * as portal from '@/api/portal'
import * as LogApi from '@/api/system/pageAndFuncLog'
import { useAppStore } from '@/store/modules/app'
import { getAccessToken } from '@/utils/auth'
import { CATEGORY, useLayoutCache } from '@/hooks/web/categoryCache'
import { searchStore } from '@/store/modules/search'
const { wsCache } = useLayoutCache()
//获取全局
export default {
  data() {
    return {
      CYRKList: [],
      is_open: true,
      token: '',
      baseUrl: ''
    }
  },

  computed: {
    menu_actived() {
      return useAppStore().work_menu
    }
  },

  mounted() {
    this.loadCYRK()
    this.token = getAccessToken()
    this.baseUrl = getCurrentInstance()?.appContext.config.globalProperties.$baseUrl
  },

  methods: {
    //菜单点击
    db_open(tag) {
      const url = '/Portal/workCenter/myWork'
      let pagename
      switch (tag) {
        case 'newtask':
          pagename = '我的待办'
          break
        case 'completetask':
          pagename = '我的已办'
          break
        case 'apply':
          pagename = '我的申请'
          break
        case 'myfile':
          pagename = '我的文件'
          break
        case 'focus':
          pagename = '我的关注'
          break
        default:
          pagename = '我的任务'
          break
      }
      let objdata = {
        pagename: pagename,
        pageurl: url,
        tag: '我的任务'
      }
      const log = LogApi.pageLog(objdata)
      nextTick(() => {
        searchStore().setToolbarValue(tag)
      })
      useAppStore().set_work_menu(tag) //设置全局变量
    },

    //左侧菜单
    menu_click() {
      this.is_open = !this.is_open
    },
    toIndex() {
      if (wsCache.get(CATEGORY.IsLayout)) {
        return '/Portal/newsLayout'
      } else {
        return '/Portal/WorkCenter/Index'
      }
    },
    handelhref(url, item) {
      console.log('点击了超链接==========>', item)
      let toUrl
      if (url.includes('http://') || url.includes('https://')) {
        if (url.includes('?')) {
          toUrl = url + '&token=' + this.token
        } else {
          toUrl = url + '?token=' + this.token
        }
      } else {
        if (url.includes('?')) {
          toUrl = this.baseUrl + url + '&token=' + this.token
        } else {
          toUrl = this.baseUrl + url + '?token=' + this.token
        }
      }

      //替换//
      toUrl = toUrl.replace(/([^:]\/)\/+/g, '$1')
      const objdata = {
        menuId: item.MenuId,
        funcname: item.FuncName,
        funcurl: item.Url
      }
      const log = LogApi.funcLog(objdata)
      window.open(toUrl, '_blank')
    },

    //左侧导航
    loadCYRK: function () {
      portal.djph_list({}).then((ret) => {
        this.CYRKList = ret
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import url('../../assets/css/uk.min.css');
@import url('../../assets/css/master.css');
@import url('../../assets/css/bootstrap.min.css');
@import url('../../assets/css/workcenter.css');

.db_list {
  width: 1104px;
  background: #fff;
  min-height: 700px;
  margin-left: auto;
}

.siderbar {
  position: fixed;
  z-index: 999;
  // margin-top: 80px;
  width: 260px;
  border-radius: 3px;
  background: #fff;
  color: #666;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  padding: 40px 0;

  ul {
    margin: 0 !important;
    padding: 0 !important;
  }

  .p_item {
    white-space: nowrap;
    text-overflow: ellipsis;

    .m_a {
      display: inline-block;
      color: black;
      font-weight: 600;
      text-decoration: none;
      display: flex;
      align-items: center;
      height: 30px;

      &.expend::after {
        background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Cpolyline%20fill%3D%22none%22%20stroke%3D%22%23666%22%20stroke-width%3D%221.1%22%20points%3D%2210%201%204%207%2010%2013%22%20%2F%3E%0A%3C%2Fsvg%3E);
        background-repeat: no-repeat;
        background-position: 50% 50%;
        transform: rotate(180deg);
        transition: transform 0.4s ease-out;
        width: 1.5em;
        height: 1.5em;
        content: '';
        margin-left: 65px;
      }

      &.open::after {
        transform: rotate(90deg);
      }

      .icon {
        margin-left: 40px;
      }

      .text {
        font-size: 15px;
        margin-left: 6px;
        color: #000;
      }
    }

    .ul_sub {
      transition: height 0.3s ease-out;
      overflow: hidden;
      margin-left: -30px;
      display: flex;
      flex-direction: column;
      margin: 0 !important;
      padding: 0 !important;

      a {
        height: 36px;
        line-height: 36px;
        width: 100%;
        width: 100%;
        height: 100%;
        display: inline-block;
        color: #999;
        font-weight: normal;
        padding-left: 64px;
        text-decoration: none;
        cursor: pointer;
        border-left: 2px solid #fff;

        &:hover {
          color: #333;
        }

        &.actived {
          border-left: 2px solid #0070ff;
          background-color: #f4faff;
          color: #0070ff;
          font-weight: 600;
        }
      }
    }
  }
}

.work-top .uk-card .uk-card-header div:first-child > .active {
  // background: url("@/assets/icons/portal/index_icon_yfw.png") no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div:nth-child(2) > .active {
  // background: url("@/assets/icons/portal/index_icon_ytz.png") no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div:nth-child(3) > .active {
  // background: url("@/assets/icons/portal/index_icon_ygg.png") no-repeat center center;
  background-size: 90% auto;
}

.work-center .uk-card .uk-card-header > div:first-child::before {
  content: '';
  width: 20px;
  height: 24px;
  //background: url("@/assets/icons/portal/index_icon_task.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div:nth-child(2)::before {
  content: '';
  width: 20px;
  height: 24px;
  // background: url("@/assets/icons/portal/index_icon_taskok.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div:nth-child(3)::before {
  content: '';
  width: 20px;
  height: 24px;
  // background: url("@/assets/icons/portal/index_icon_msg.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(1)::before {
  content: '';
  width: 20px;
  height: 24px;
  background: url('@/assets/imgs/index_icon_task_active.png') no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(2)::before {
  content: '';
  width: 20px;
  height: 24px;
  background: url('@/assets/imgs/index_icon_taskok_active.png') no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header > div.active:nth-child(3)::before {
  content: '';
  width: 20px;
  height: 24px;
  background: url('@/assets/imgs/index_icon_msg_active.png') no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.swiper-pagination {
  border: 2px solid blue !important;
}

.khidi_banner {
  width: 460px;
  height: 330px;

  .img {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }

  .desc {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 4;
    height: 30px;
    font-size: 14px;
    line-height: 30px;
    box-sizing: border-box;
    padding: 0 10px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
  }
}

.swiper-pagination-bullet {
  width: 30px !important;
  height: 30px;
  background-color: #fff;
  opacity: 0.5;
  border: 1px solid #000;
}

.swiper-pagination-bullet-active {
  background-color: #000;
  opacity: 1;
}
</style>
