<template>
  <el-input v-model="filterText" style="width: 100%;margin-bottom: 20px" placeholder="请输入节点名称">
    <template #append>
      <el-button :icon="CloseBold" @click="filterText = ''" />
    </template>
  </el-input>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <el-tree ref="treeRef" style="max-width: 600px;" :height="100" class="filter-tree" :data=props.data
      :props="defaultProps" accordion :filter-node-method="filterNode" @node-click="handleNodeClick" />
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus';
import { defaultProps } from '@/utils/tree'
import {  CloseBold } from '@element-plus/icons-vue'
const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()

const props = defineProps({
  data: {
    type: Array,
    required: true,
  }
});

// 树组件
interface Tree {
  name: string
  children?: Tree[]
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})


//树点击事件
const handleNodeClick = (node) => {
  // 在这里添加你的逻辑代码
  alert(node.label)
}

const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.name.includes(value)
}

</script>