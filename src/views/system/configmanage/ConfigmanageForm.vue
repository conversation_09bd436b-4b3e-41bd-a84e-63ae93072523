<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="父ID" prop="parentID">
        <el-input v-model="formData.parentID" placeholder="请输入父ID" />
      </el-form-item>
      <el-form-item label="全ID" prop="fullID">
        <el-input v-model="formData.fullID" placeholder="请输入全ID" />
      </el-form-item>
      <el-form-item label="排序索引" prop="sortIndex">
        <el-input v-model="formData.sortIndex" placeholder="请输入排序索引" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="code" prop="code">
        <el-input v-model="formData.code" placeholder="请输入code" />
      </el-form-item>
      <el-form-item label="节点类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择节点类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="iconcls" prop="iconCls">
        <el-input v-model="formData.iconCls" placeholder="请输入iconcls" />
      </el-form-item>
      <el-form-item label="主界面地址" prop="url">
        <el-input v-model="formData.url" placeholder="请输入主界面地址" />
      </el-form-item>
      <el-form-item label="控制类型" prop="ctrlType">
        <el-select v-model="formData.ctrlType" placeholder="请选择控制类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="权限，可以为页面控件ID，数据的查询条件" prop="auth">
        <el-input v-model="formData.auth" placeholder="请输入权限，可以为页面控件ID，数据的查询条件" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="子系统编号" prop="systemCode">
        <el-input v-model="formData.systemCode" placeholder="请输入子系统编号" />
      </el-form-item>
      <el-form-item label="创建人" prop="createUser">
        <el-input v-model="formData.createUser" placeholder="请输入创建人" />
      </el-form-item>
      <el-form-item label="创建用户id" prop="createUserID">
        <el-input v-model="formData.createUserID" placeholder="请输入创建用户id" />
      </el-form-item>
      <el-form-item label="修改用户" prop="modifyUser">
        <el-input v-model="formData.modifyUser" placeholder="请输入修改用户" />
      </el-form-item>
      <el-form-item label="修改用户id" prop="modifyUserID">
        <el-input v-model="formData.modifyUserID" placeholder="请输入修改用户id" />
      </el-form-item>
      <el-form-item label="修改时间" prop="modifyTime">
        <el-date-picker
          v-model="formData.modifyTime"
          type="date"
          value-format="x"
          placeholder="选择修改时间"
        />
      </el-form-item>
      <el-form-item label="节点状态（未发布，已发布）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="节点编辑权限" prop="editAuth">
        <el-input v-model="formData.editAuth" placeholder="请输入节点编辑权限" />
      </el-form-item>
      <el-form-item label="节点编辑权限" prop="editAuthUser">
        <el-input v-model="formData.editAuthUser" placeholder="请输入节点编辑权限" />
      </el-form-item>
      <el-form-item label="节点的连接页面" prop="configUrl">
        <el-input v-model="formData.configUrl" placeholder="请输入节点的连接页面" />
      </el-form-item>
      <el-form-item label="主数据库连接" prop="mainDBConn">
        <el-input v-model="formData.mainDBConn" placeholder="请输入主数据库连接" />
      </el-form-item>
      <el-form-item label="relateId" prop="relateID">
        <el-input v-model="formData.relateID" placeholder="请输入relateId" />
      </el-form-item>
      <el-form-item label="关联表" prop="relateTable">
        <el-input v-model="formData.relateTable" placeholder="请输入关联表" />
      </el-form-item>
      <el-form-item label="是否主界面" prop="isMainUrl">
        <el-input v-model="formData.isMainUrl" placeholder="请输入是否主界面" />
      </el-form-item>
      <el-form-item label="是否删除" prop="isDeleted">
        <el-input v-model="formData.isDeleted" placeholder="请输入是否删除" />
      </el-form-item>
      <el-form-item label="isStandard" prop="isStandard">
        <el-input v-model="formData.isStandard" placeholder="请输入isStandard" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ConfigmanageApi, ConfigmanageVO } from '@/api/system/configmanage'

/** 系统配置结构树 表单 */
defineOptions({ name: 'ConfigmanageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  parentID: undefined,
  fullID: undefined,
  sortIndex: undefined,
  name: undefined,
  code: undefined,
  type: undefined,
  iconCls: undefined,
  url: undefined,
  ctrlType: undefined,
  auth: undefined,
  description: undefined,
  systemCode: undefined,
  createUser: undefined,
  createUserID: undefined,
  modifyUser: undefined,
  modifyUserID: undefined,
  modifyTime: undefined,
  status: undefined,
  editAuth: undefined,
  editAuthUser: undefined,
  configUrl: undefined,
  mainDBConn: undefined,
  relateID: undefined,
  relateTable: undefined,
  isMainUrl: undefined,
  isDeleted: undefined,
  isStandard: undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ConfigmanageApi.getConfigmanage(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ConfigmanageVO
    if (formType.value === 'create') {
      await ConfigmanageApi.createConfigmanage(data)
      message.success(t('common.createSuccess'))
    } else {
      await ConfigmanageApi.updateConfigmanage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    parentID: undefined,
    fullID: undefined,
    sortIndex: undefined,
    name: undefined,
    code: undefined,
    type: undefined,
    iconCls: undefined,
    url: undefined,
    ctrlType: undefined,
    auth: undefined,
    description: undefined,
    systemCode: undefined,
    createUser: undefined,
    createUserID: undefined,
    modifyUser: undefined,
    modifyUserID: undefined,
    modifyTime: undefined,
    status: undefined,
    editAuth: undefined,
    editAuthUser: undefined,
    configUrl: undefined,
    mainDBConn: undefined,
    relateID: undefined,
    relateTable: undefined,
    isMainUrl: undefined,
    isDeleted: undefined,
    isStandard: undefined,
  }
  formRef.value?.resetFields()
}
</script>
