<template>
  <Dialog class="cardHeight" v-model="dialogVisible" title="角色多选" width="95%" height="400px">
      <el-row>
        <el-col :span="6">
          <ContentWrap class="h-1/1">
            <Tree v-if="treeDataLoaded" :data="treeData" @node-clicked="nodeClicked" :height="600"/>
          </ContentWrap>
        </el-col>
        <el-col :span="8">
          <ContentWrap>
          <el-tabs v-model="activeName" type="border-card" :tab-change="getList" >
            <el-tab-pane name="AllRole" label="全部角色"/>
            <el-tab-pane name="SystemRole" label="系统角色"/>
            <el-tab-pane name="OrgRole" label="组织角色"/>
            <el-tab-pane name="VariableRole" label="动态角色"/>
            <el-tab-pane name="AutoRole" label="自动更新角色"/>
            <el-table style="margin-top: 20px" v-loading="loading" :data="list" :height="530">
              <el-table-column align="center" label="角色名称" prop="name"/>
              <el-table-column align="center" label="选择">
                <template #default="scope">
                  <el-button
                    link
                    type="danger"
                    @click="selectRow(scope.row)">
                    选择
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tabs>
          </ContentWrap>
        </el-col>
        <el-col :span="4">
          <ContentWrap>
            <el-table style="margin-top: 20px" v-loading="loading" :data="authToRoleList" :height="600">
              <el-table-column align="center" label="已拥有角色" prop="name"/>
            </el-table>
            <!-- 分页 -->
            <Pagination
              v-model:limit="queryParams.pageSize"
              v-model:page="queryParams.pageNo"
              :total="total"
              @pagination="getList"
            />
          </ContentWrap>
        </el-col>
        <el-col :span="6">
          <ContentWrap>
            <div>
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="dialogVisible = false">取 消</el-button>
            </div>
            <el-table style="margin-top: 20px" v-loading="loading" :data="addList" :height="600">
              <el-table-column align="center" label="名称" prop="name" show-overflow-tooltip />
              <el-table-column align="center" label="移除">
                <template #default="scope">
                  <el-button
                    link
                    type="danger"
                    @click="removeRow(scope.row)">
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </ContentWrap>
        </el-col>

      </el-row>
  </Dialog>
</template>

<script lang="ts" setup>
import {Tree} from '@/components/tree';
import * as ByResourceApi from '@/api/system/byResource'
import {getAGroupDataListById, getGroupTree, saveAGroupRes} from "@/api/system/byResource";
import * as PermissionApi from "@/api/system/permission";
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
//选项卡默认选中
const activeName = ref('AllRole')
const dialogVisible = ref(false)
// 列表的总页数
const total = ref(0)
// 树数据
const treeData = ref(null);
//树加载
const treeDataLoaded = ref(false);
const nodeId = ref('')
const resId = ref('')
// 列表的加载中
const loading = ref(false)
// 列表的数据
const list = ref()
//已绑定列表的数据
const authToRoleList = ref()
const addList = ref([])
const isAuthLower = ref()

//查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const open = async (id,isAuth) => {
  resId.value = id.value;
  isAuthLower.value = isAuth.value;
  addList.value = [];
  getTreeData();
  getList();
  getAuthToRoleList();
  dialogVisible.value = true;
}
defineExpose({ open })
const selectRow = (row) =>{
  if (!addList.value.includes(row)){
    addList.value.push(row)
  }

}

const removeRow = (row) =>{
  addList.value.splice(addList.value.indexOf(row), 1);
}
/** 获取角色列表数据 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      id: nodeId.value,
      type: activeName.value,
      groupType: 'Role'
    }
    const data = await ByResourceApi.getAGroupDataList(params)
    list.value = data
  } finally {
    loading.value = false
  }
}
/** 获取已绑定列表数据 */
const getAuthToRoleList = async () => {
  loading.value = true
  try {
    const params = {
      resId: resId.value,
    }
    const data = await ByResourceApi.getAGroupDataListById(params)
    authToRoleList.value = data
  } finally {
    loading.value = false
  }
}

/** 获取树数据 */
const getTreeData = async () => {
  try {
    const response = await ByResourceApi.getGroupTree({ id: null, groupType: "Role" });
    treeData.value = response;
    treeDataLoaded.value = true;
  } catch (error) {
    console.error('Error fetching tree data:', error);
  }
}

//树鼠标右键
const nodeContextmenu = (node) => {
  nodeId.value = node.key
  getList();
}
//树鼠标左键
const nodeClicked = (node) => {
  nodeId.value = node.key
  getList();
}

const submitForm = async () => {
  // 校验表单
  try {
    if (addList.value.length < 1){
      dialogVisible.value = false
      return;
    } else {
      const params = {
        resId: resId.value,
        groupIds: addList.value.map(item=> item.id).toString(),
        isAuthLower: isAuthLower.value
      }
      await ByResourceApi.saveAGroupRes(params);
      message.success(t('common.createSuccess'))
      console.log("params",params)
    }
  } catch (e){

  } finally {
  dialogVisible.value = false
  }
}

</script>

