<template>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="list"
      :expand-on-click-node="false"
      :props="defaultProps"
      highlight-current
      node-key="id"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import { defaultProps, handleTree } from '@/utils/tree'
import {ref} from "vue";
import {AGroupApi, AGroupVO} from '@/api/system/group'
defineOptions({ name: 'UserDeptTree' })


const queryParams = reactive({
  name: undefined,
  shortName: undefined,
  createTime: [],
  groupType: '',
})

const list = ref<AGroupVO[]>([]) // 列表的数据

/** 查询列表 */
const getList = async () => {
  try {
    queryParams.groupType = "Role";
    const data = await AGroupApi.getAGroupList(queryParams)
    list.value = handleTree(data, 'id', 'parentID')
  } finally {
  }
}


const treeRef = ref<InstanceType<typeof ElTree>>()



/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])


const startQuery = (params: Object) => {
  Object.assign(queryParams, params);
  getList();
};
// const defaultExpandedKeys = ref(false);
// const opentree=(b :boolean)=>{
//   defaultExpandedKeys.value = !defaultExpandedKeys.value;
//   nextTick()
//
// }
defineExpose({ startQuery }) // 提供 open 方法，用于打开弹窗
/** 初始化 */
onMounted(async () => {
  await getList()
})
const defaultExpandedKeys = computed(() => {
  const keys = [];
  list.value.forEach(item => {
    // 假设每个一级节点的key是其label
    keys.push(item.id);
  });
  return keys;
});

</script>
