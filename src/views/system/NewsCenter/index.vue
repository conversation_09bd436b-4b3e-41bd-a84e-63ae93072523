<template>
  <el-scrollbar height="100%">
    <div class="body" id="apps">
      <portal_header/>
      <div class="body-inner">
        <!--抬头滚动图片-->
        <div
          class="swiper-container banner_swiper hbm-zxzx-body-leftbar1"
          style="width: 1378px; height: 338px; margin-bottom: 15px"
          v-if="banner && banner.length > 0"
          v-cloak
        >
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in banner" :key="index">
              <a :href="_url(item.url)" target="_blank">
                <img
                  :src="'/BasicApplication/DownloadFile?FileID=' + item.img"
                  style="width: 100%; height: 100%"
                />
              </a>
            </div>
          </div>
          <div class="swiper-button-next swiper-button-next1"></div>
          <div class="swiper-button-prev swiper-button-prev1"></div>
        </div>

        <div class="hbm-zxzx-body-leftbar1">
          <div class="hbm-zxzx-sec hbm-zxzx-sec1">
            <div class="hbm-zxzx-row">
              <!--图片新闻-->
              <div class="hbm-zxzx-cell zxzx_banner" v-if="tpxw && tpxw.length > 0" v-cloak>
                <div class="swiper-container news_banner news_banner_swiper" ref="banner">
                  <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="(item, index) in tpxw" :key="index">
                      <a
                        :href="detail_url('TPXW', item.ID, '图片新闻')"
                        :title="item.Title"
                        target="_blank"
                      >
                        <img :src="item.PicFile" />
                      </a>
                      <div style="bottom: 0" class="text">
                        <span class="title" :title="item.Title">
                          <pre class="qkg">{{ item.Title }}</pre>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="swiper-pagination"></div>
                  <div class="swiper-button-next swiper-button-next2"></div>
                  <div class="swiper-button-prev swiper-button-prev2"></div>
                  <a href="/Portal/NewsCenter/PublicInfoList?code=TPXW" class="more" target="_blank"
                    >更多 ></a
                  >
                </div>
              </div>
              <el-empty v-else description="数据为空" />

              <!--集团要闻&时政新闻-->
              <div class="hbm-zxzx-cell toper" style="width: 514px">
                <div class="gcs_tab_jtywszxw">
                  <div class="gcs_tabhd">
                    <span
                      ><b><i>HOT</i></b></span
                    >
                    <ul>
                      <li
                        v-on:click="tab1_index = 1"
                        :class="1 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                        atr="jtyw"
                      ></li>
                      <li
                        v-on:click="tab1_index = 2"
                        :class="2 == tab1_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                        style="margin-left: 50px"
                        atr="szxw"
                      ></li>
                    </ul>
                    <a
                      :href="
                        1 == tab1_index
                          ? '/Portal/NewsCenter/PublicInfoList?code=JTYW'
                          : '/Portal/NewsCenter/PublicInfoList?code=SZXW'
                      "
                      target="_blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>

                  <div class="gcs_tabbd">
                    <div v-if="1 == tab1_index" class="gcs_tabitem">
                      <ul class="article_list top_news" v-if="jtyw && jtyw.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in jtyw" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('XMRYRM', item.ID, '集团要闻')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>

                      <el-empty v-else description="数据为空" />
                    </div>

                    <div v-if="2 == tab1_index" class="gcs_tabitem" v-cloak>
                      <ul class="article_list top_news" v-if="szxw && szxw.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in szxw" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="item.OtherWebAddres"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--院发文&人员任免&院通知&院公告-->
          <div class="hbm-zxzx-sec hbm-zxzx-sec2">
            <div class="hbm-zxzx-row">
              <!--院发文&人员任免-->
              <div class="hbm-zxzx-cell" style="width: 383px">
                <div class="gcs_tab_yfwbmfwryrm" cty="tab" grp="zxzx_yfwbmfwryrm">
                  <div class="gcs_tabhd" grp="zxzx_yfwbmfwryrm">
                    <ul>
                      <li
                        v-on:click="tab2_index = 1"
                        :class="1 == tab2_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                      >
                        院发文
                      </li>
                      <li
                        v-on:click="tab2_index = 2"
                        :class="2 == tab2_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'"
                      >
                        人员任免
                      </li>
                    </ul>
                    <a
                      :href="
                        1 == tab2_index
                          ? '/Portal/NewsCenter/PublicInfoList?code=YNFW'
                          : '/Portal/NewsCenter/PublicInfoList?code=RYRM'
                      "
                      target="_blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>

                  <div class="gcs_tabbd" grp="zxzx_yfwbmfwryrm">
                    <div v-if="1 == tab2_index" class="gcs_tabitem" grp="zxzx_yfwbmfwryrm">
                      <ul class="article_list" v-if="yfw && yfw.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in yfw" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('YNFW', item.ID, '院发文')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>

                      <el-empty v-else description="数据为空" />
                    </div>

                    <div v-if="2 == tab2_index" class="gcs_tabitem" grp="zxzx_yfwbmfwryrm">
                      <ul class="article_list">
                        <li class="item has_icon" v-for="(item, index) in ryrm" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('RYRM', item.ID, '人员任免')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <!--院通知-->
              <div class="hbm-zxzx-cell" style="width: 383px">
                <div class="gcs_tab_yntz gcs_tab_zxzx_single" cty="tab" grp="zxzx_yntz">
                  <div class="gcs_tabhd" grp="zxzx_yntz">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="yntz">
                        院通知
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                    <a
                      href="/Portal/NewsCenter/PublicInfoList?code=YNTZ"
                      target="__blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>

                  <div class="gcs_tabbd" grp="zxzx_yntz">
                    <div class="gcs_tabitem" grp="zxzx_yntz" atr="yntz">
                      <ul class="article_list" v-if="ytz && ytz.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in ytz" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('YNTZ', item.ID, '院内通知')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                      <el-empty v-else description="数据为空" />
                    </div>
                  </div>
                </div>
              </div>

              <!--院公告-->
              <div class="hbm-zxzx-cell" style="width: 383px">
                <div class="gcs_tab_yngg gcs_tab_zxzx_single" cty="tab" grp="zxzx_yngg">
                  <div class="gcs_tabhd" grp="zxzx_yngg">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="yngg">
                        院公告
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                    <a
                      href="/Portal/NewsCenter/PublicInfoList?code=YNGG"
                      target="__blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_yngg">
                    <div class="gcs_tabitem" grp="zxzx_yngg" atr="yngg">
                      <ul class="article_list" v-if="ygg && ygg.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in ygg" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('YNGG', item.ID, '院内公告')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                      <el-empty v-else description="数据为空" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--项目人员任免&部门发&重点报道-->
          <div class="hbm-zxzx-sec hbm-zxzx-sec3">
            <div class="hbm-zxzx-row">
              <!--项目人员任免-->
              <div class="hbm-zxzx-cell" style="width: 383px">
                <div class="gcs_tab_zdbd gcs_tab_zxzx_single" cty="tab" grp="zxzx_zdbd">
                  <div class="gcs_tabhd" grp="zxzx_zdbd">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="XMRYRM">
                        项目人员任免
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                    <a
                      href="/Portal/NewsCenter/PublicInfoList?code=XMRYRM"
                      target="__blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>
                  <div class="gcs_tabbd">
                    <ul class="article_list" v-if="xmryrm && xmryrm.length > 0" v-cloak>
                      <li class="item" v-for="(item, index) in xmryrm" :key="index">
                        <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                        <a
                          class="link"
                          :href="detail_url('XMRYRM', item.ID, '项目人员任免')"
                          target="_blank"
                          :title="item.Title"
                          v-text="item.Title"
                        ></a>
                        <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                      </li>
                    </ul>
                    <el-empty v-else description="数据为空" />
                  </div>
                </div>
              </div>

              <!--部门发文-->
              <div class="hbm-zxzx-cell" style="width: 383px">
                <div class="gcs_tab_zdbd gcs_tab_zxzx_single" cty="tab" grp="zxzx_zdbd">
                  <div class="gcs_tabhd" grp="zxzx_zdbd">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="BMFW">
                        部门发文
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                    <div class="hbm-moreico fa fa-list-ul"></div>
                    <a
                      href="/Portal/NewsCenter/PublicInfoList?code=BMFW"
                      target="__blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_zdbd">
                    <div class="gcs_tabitem" grp="zxzx_zdbd" atr="zdbd">
                      <ul class="article_list" v-if="bmfw && bmfw.length > 0" v-cloak>
                        <li class="item has_icon" v-for="(item, index) in bmfw" :key="index">
                          <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                          <a
                            class="link"
                            :href="detail_url('BMFW', item.ID, '部门发文')"
                            target="_blank"
                            :title="item.Title"
                            v-text="item.Title"
                          ></a>
                          <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                        </li>
                      </ul>
                      <el-empty v-else description="数据为空" />
                    </div>
                  </div>
                </div>
              </div>

              <!--重点报道-->
              <div class="hbm-zxzx-cell" style="width: 383px">
                <div class="gcs_tab_zdbd gcs_tab_zxzx_single" cty="tab" grp="zxzx_zdbd">
                  <div class="gcs_tabhd" grp="zxzx_zdbd">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="zdbd">
                        重点报道
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                    <a
                      href="/Portal/NewsCenter/PublicInfoList?code=ZDBD"
                      target="__blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_zdbd">
                    <ul class="article_list" v-if="zdbd && zdbd.length > 0" v-cloak>
                      <li class="item has_icon" v-for="(item, index) in zdbd" :key="index">
                        <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                        <a
                          class="link"
                          :href="detail_url('ZDBD', item.ID, '重点报道')"
                          target="_blank"
                          :title="item.Title"
                          v-text="item.Title"
                        ></a>
                        <span class="datetime" v-text="format_datetime(item.CreateTime)"></span>
                      </li>
                    </ul>
                    <el-empty v-else description="数据为空" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--财务资金-->
          <div class="hbm-zxzx-sec hbm-zxzx-sec4 wlaqsj">
            <div class="hbm-zxzx-row">
              <div class="hbm-zxzx-cell" style="width: 100%">
                <div class="gcs_tab_wlaqzthd gcs_tab_zxzx_single" cty="tab" grp="zxzx_wlaqzthd">
                  <div class="gcs_tabhd" grp="zxzx_wlaqzthd">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="CWZJZL">
                        财务资金专栏
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_wlaqzthd">
                    <div class="gcs_tabitem" grp="zxzx_wlaqzthd" atr="wlaqzthd">
                      <div class="gcs_zxzx_wlaqzthd_childs_cont">
                        <div class="gcs_zxzx_wlaqzthd_listitem">
                          <div class="gcs_zxzx_wlaqzthd_zstz">
                            <div class="gcs_zxzx_ss_head" style="position: relative">
                              <span class="gcs_zxzx_ss_head_ico"></span>
                              <span class="gcs_zxzx_ss_head_title">财务资金</span>
                              <a
                                href="/Portal/NewsCenter/PublicInfoList?code=CWZJ"
                                target="__blank"
                                class="hbm-moreico fa fa-list-ul"
                              ></a>
                            </div>
                            <div class="gcs_zxzx_ss_body">
                              <ul class="article_list" v-if="cwzj && cwzj.length > 0" v-cloak>
                                <li
                                  class="item has_icon"
                                  v-for="(item, index) in cwzj"
                                  :key="index"
                                >
                                  <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                                  <a
                                    class="link"
                                    :href="detail_url('CWZJ', item.ID, '财务资金')"
                                    target="_blank"
                                    :title="item.Title"
                                    v-text="item.Title"
                                  ></a>
                                  <span
                                    class="datetime"
                                    v-text="format_datetime(item.CreateTime)"
                                  ></span>
                                </li>
                              </ul>
                              <el-empty v-else description="数据为空" />
                            </div>
                          </div>
                        </div>

                        <div class="gcs_zxzx_wlaqzthd_listitem">
                          <div class="gcs_zxzx_wlaqzthd_wkt">
                            <div class="gcs_zxzx_ss_head" style="position: relative">
                              <span class="gcs_zxzx_ss_head_ico"></span>
                              <span class="gcs_zxzx_ss_head_title">税务管理</span>
                              <a
                                href="/Portal/NewsCenter/PublicInfoList?code=SWGL"
                                target="__blank"
                                class="hbm-moreico fa fa-list-ul"
                              ></a>
                            </div>
                            <div class="gcs_zxzx_ss_body">
                              <ul class="article_list" v-if="swgl && swgl.length > 0" v-cloak>
                                <li
                                  class="item has_icon"
                                  v-for="(item, index) in swgl"
                                  :key="index"
                                >
                                  <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                                  <a
                                    class="link"
                                    :href="detail_url('SWGL', item.ID, '税务管理')"
                                    target="_blank"
                                    :title="item.Title"
                                    v-text="item.Title"
                                  ></a>
                                  <span
                                    class="datetime"
                                    v-text="format_datetime(item.CreateTime)"
                                  ></span>
                                </li>
                              </ul>
                              <el-empty v-else description="数据为空" />
                            </div>
                          </div>
                        </div>

                        <div class="gcs_zxzx_wlaqzthd_listitem">
                          <div class="gcs_zxzx_wlaqzthd_wlaqsj">
                            <div class="gcs_zxzx_ss_head" style="position: relative">
                              <span class="gcs_zxzx_ss_head_ico"></span>
                              <span class="gcs_zxzx_ss_head_title">资产与产权管理</span>
                              <a
                                href="/Portal/NewsCenter/PublicInfoList?code=ZCYCQ"
                                target="__blank"
                                class="hbm-moreico fa fa-list-ul"
                              ></a>
                            </div>
                            <div class="gcs_zxzx_ss_body">
                              <ul class="article_list" v-if="zcycq && zcycq.length > 0" v-cloak>
                                <li
                                  class="item has_icon"
                                  v-for="(item, index) in zcycq"
                                  :key="index"
                                >
                                  <i class="top" v-if="item.IsTop > 0" v-cloak></i>
                                  <a
                                    class="link"
                                    :href="detail_url('ZCYCQ', item.ID, '资产与产权管理')"
                                    target="_blank"
                                    :title="item.Title"
                                    v-text="item.Title"
                                  ></a>
                                  <span
                                    class="datetime"
                                    v-text="format_datetime(item.CreateTime)"
                                  ></span>
                                </li>
                              </ul>
                              <el-empty v-else description="数据为空" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--科技创新园地-->
          <div class="hbm-zxzx-sec hbm-zxzx-sec5">
            <div class="hbm-zxzx-row">
              <div class="hbm-zxzx-cell" style="width: 100% !important">
                <div class="gcs_tab_kjcxyd gcs_tab_zxzx_single" cty="tab" grp="zxzx_kjcxyd">
                  <div class="gcs_tabhd" grp="zxzx_kjcxyd">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="yfw">
                        科技创新园地
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                    <a
                      v-cloak
                      v-if="kjcxy && kjcxy.length > 0"
                      :href="'/Portal/NewsCenter/PublicInfoList?code=' + kjcxy[tab3_index]['Code']"
                      target="__blank"
                      class="hbm-moreico fa fa-list-ul"
                    ></a>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_kjcxyd">
                    <div class="gcs_tabitem" grp="zxzx_kjcxyd" atr="yfw">
                      <div class="gcs_tab_kjcxyd_childs" cty="tab">
                        <div class="gcs_tabhd" style="float: left">
                          <ul v-cloak v-if="kjcxy && kjcxy.length > 0">
                            <li
                              v-on:click="tab3_index = index"
                              :class="
                                index == tab3_index ? 'gcs_tabhd_item gcs_cur' : 'gcs_tabhd_item'
                              "
                              atr="item1"
                              v-for="(item, index) in kjcxy"
                              :key="index"
                            >
                              <span class="gcs_tabliico"></span
                              ><span :title="item.CatalogName" v-text="item.CatalogName"></span>
                            </li>
                          </ul>
                          <el-empty v-else description="数据为空" />
                        </div>

                        <div class="gcs_tabbd" style="float: right; width: 938px">
                          <div class="gcs_tabitem" v-if="kjcxy && kjcxy.length > 0" v-cloak>
                            <div
                              class="gcs_newslist gcs_newslist_kjcx_dynamic"
                              v-for="(item, index) in kjcxy"
                              :key="index"
                            >
                              <templatete v-if="index == tab3_index">
                                <div
                                  v-for="(i, idx) in item.list"
                                  :key="idx"
                                  class="gcs_newslist_item"
                                  cid="77783"
                                  :title="i.Title"
                                >
                                  <span class="gcs_title bgtitle">
                                    <a
                                      :href="detail_url(item.Code, i.ID, item.CatalogName)"
                                      target="_blank"
                                      :title="i.Title"
                                      v-text="i.Title"
                                    ></a>
                                  </span>
                                  <span class="gcs_dt" v-text="format_date(i.CreateTime)"></span>
                                </div>
                              </templatete>
                            </div>
                          </div>
                          <el-empty v-else description="数据为空" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="hbm-zxzx-body-rightbar1">
          <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-ztzl">
            <div class="hbm-zxzx-row">
              <div class="hbm-zxzx-cell" style="width: 100%; height: 328px">
                <div class="gcs_tab_ztzl gcs_tab_zxzx_single" cty="tab" grp="zxzx_ztzl">
                  <div class="gcs_tabhd" grp="zxzx_ztzl">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="ztzl"
                        >专题专栏
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_ztzl">
                    <div class="gcs_tabitem" grp="zxzx_ztzl" atr="ztzl">
                      <ul class="gcs_ztzl">
                        <li class="gcs_ztzl_zlgh">
                          <a
                            href="http://10.10.1.173:8001/Portal/NewsCenter/PublicInfoList?code=ZLGH"
                            target="_blank"
                            >战略规划</a
                          >
                        </li>

                        <li class="gcs_ztzl_szkmy" style="display: none">
                          <a href="http://10.10.1.28:9003/" target="_blank">数字昆明院</a>
                        </li>

                        <li class="gcs_ztzl_szyxt">
                          <a href="http://10.10.10.90:88" target="_blank">市政院系统</a>
                        </li>

                        <li class="gcs_ztzl_yzxx">
                          <a href="http://10.10.1.29/yzxx/YZXX_List.asp" target="_blank"
                            >院长信箱</a
                          >
                        </li>

                        <li class="gcs_ztzl_jlbgt">
                          <a
                            href="/Portal/NewsCenter/PublicInfoList?code=JLBGT"
                            title="减利因素曝光台"
                            target="_blank"
                            >减利因素曝光台</a
                          >
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-ksrk">
            <div class="hbm-zxzx-row">
              <div class="hbm-zxzx-cell" style="width: 100%; min-height: 300px">
                <div class="gcs_tab_ksrk gcs_tab_zxzx_single" cty="tab" grp="zxzx_ksrk">
                  <div class="gcs_tabhd" grp="zxzx_ksrk">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="yfw">
                        快速入口
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_ksrk">
                    <div class="list" v-cloak v-if="ksrk && ksrk.length > 0">
                      <a
                        :href="_url(item.Url)"
                        target="_blank"
                        :title="item.Name"
                        class="item"
                        v-for="(item, index) in ksrk"
                        :key="index"
                      >
                        <span class="text" v-text="item.Name"></span>
                        <span class="gcs_aaarow_flag">>></span>
                      </a>
                    </div>
                    <el-empty v-else description="数据为空" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="hbm-zxzx-body-rightbaritem hbm-zxzx-yqlj">
            <div class="hbm-zxzx-row">
              <div class="hbm-zxzx-cell" style="width: 100%; min-height: 300px">
                <div class="gcs_tab_yqlj gcs_tab_zxzx_single" cty="tab" grp="zxzx_yqlj">
                  <div class="gcs_tabhd" grp="zxzx_yqlj">
                    <ul>
                      <li class="gcs_tabhd_item gcs_cur" atr="yqlj">
                        友情链接
                        <div class="gcs_cur_inner"></div>
                      </li>
                    </ul>
                  </div>
                  <div class="gcs_tabbd" grp="zxzx_yqlj">
                    <div class="list" v-cloak v-if="yqlj && yqlj.length > 0">
                      <a
                        :href="item.Url"
                        target="_blank"
                        :title="item.Name"
                        class="item"
                        v-for="(item, index) in yqlj"
                        :key="index"
                      >
                        <span class="text" v-text="item.Name"></span>
                        <span class="gcs_aaarow_flag">>></span>
                      </a>
                    </div>
                    <el-empty v-else description="数据为空" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footViewStyle">
      <portal_bottomer/>
    </div>
    </div>


  </el-scrollbar>
</template>

<script>
import * as PortalApi from '@/api/system/portal'
import $ from 'jquery'
import { getAccessToken } from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'
import portal_header from '@/views/portal/components/portal_header.vue'
import portal_bottomer from '@/views/portal/components/portal_bottomer.vue'
export default {
  data() {
    return {
      banner: [], //顶部轮播图抬头图片
      tpxw: [], //图片新闻
      jtyw: [], //集团要闻
      szxw: [], //时政新闻
      yfw: [], //院发文
      ryrm: [], //人员任免
      ytz: [], //院通知
      ygg: [], //院公告
      ksrk: [], //快速入口
      xmryrm: [], //项目人员任免
      bmfw: [], //部门发文
      zdbd: [], //重点报道
      yqlj: [], //友情链接
      //fbgs: [], //分包公示
      //fbgs_count: 0, //分包公示总数
      //zstz: [], //知识拓展
      //wkt: [], //微课堂
      //wlaqsj: [], //网络安全事件
      cwzj: [], //财务资金
      swgl: [], //税务管理
      zcycq: [], //资产与产权
      kjcxy: [], //科技创新园地
      tab1_index: 1, //集团要闻&时政新闻tab切换索引
      tab2_index: 1, //院发文&人员任免tab切换索引
      tab3_index: 0 //科技创新园tab切换索引
    }
  },
  components: {
    portal_header,
    portal_bottomer
  },
  created: function () {
    this.get_banner() //获取 - 顶部轮播图抬头图片
    this.get_tpxw() //获取 - 图片新闻
    this.get_jtyw() //获取 - 集团要闻
    this.get_szxw() //获取 - 时政新闻
    this.get_yfw() //获取 - 院发文
    this.get_ryrm() //获取 - 人员任免
    this.get_ytz() //获取 - 院通知
    this.get_ygg() //获取 - 院公告
    this.get_ksrk() //获取 - 快速入口
    this.get_xmryrm() //获取 - 项目人员任免
    this.get_bmfw() //获取 - 部门发文
    this.get_zdbd() //获取 - 重点报道
    this.get_yqlj() //获取 - 友情链接
    //this.get_fbgs(); //获取 - 分包公示
    //this.get_zstz(); //获取 - 知识拓展
    //this.get_wkt(); //获取 - 微课堂
    //this.get_wlaqsj(); //获取 - 网络安全事件
    this.get_cwzj()
    this.get_swgl()
    this.get_zcycq()
    this.get_kjcxy() //获取 - 科技创新园地
  },

  //vue需要的方法
  methods: {
    //顶部抬头轮播图
    get_banner: async function () {
      //系统脚本库 - 资讯中心_首页_抬头banner图片
      let result = await PortalApi.execSystemScript({
        code: 'SQL_ae0c00eda596438f90547de550efa3ed',
        ExecData: ''
      })
      this.banner = result
      let banner_timer = setInterval(function () {
        if (this.banner && this.banner.length > 0) {
          clearInterval(banner_timer)
          //顶部抬头banner图片swiper
          let banner_swiper = new Swiper('.banner_swiper', {
            loop: true,
            speed: 1200,
            autoplay: {
              delay: 6000 //5秒切换一次
            },
            navigation: {
              nextEl: '.swiper-button-next1',
              prevEl: '.swiper-button-prev1'
            }
          })
        }
      }, 100)
    },

    //banner图片新闻
    get_tpxw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'TPXW', pageSize: 8 })
      this.tpxw = result.records
      let tpxw_timer = setInterval(function () {
        if (this.tpxw && this.tpxw.length > 0) {
          clearInterval(tpxw_timer)
          //图片新闻swiper
          let news_banner_swiper = new Swiper('.news_banner_swiper', {
            loop: true,
            speed: 800,
            autoplay: {
              delay: 4000 //5秒切换一次
            },
            pagination: {
              el: '.swiper-pagination',
              clickable: true
            },
            navigation: {
              nextEl: '.swiper-button-next2',
              prevEl: '.swiper-button-prev2'
            }
          })
        }
      }, 100)
    },

    //集团要闻
    get_jtyw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'JTYW', pageSize: 7 })
      this.jtyw = result.records
    },

    //时政要闻
    get_szxw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'SZXW', pageSize: 7 })
      this.szxw = result.records
    },

    //院发文
    get_yfw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'YNFW', pageSize: 6 })
      this.yfw = result.records
    },

    //人员任免
    get_ryrm: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'RYRM', pageSize: 6 })
      this.ryrm = result.records
    },

    //院通知
    get_ytz: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'YNTZ', pageSize: 6 })
      this.ytz = result.records
    },

    //院公告
    get_ygg: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'YNGG', pageSize: 6 })
      this.ygg = result.records
    },

    //快速入口
    get_ksrk: async function () {
      let result = await PortalApi.newsCenterGetLinks({ type: '快速入口', page: 1, pageSize: 10 })
      this.ksrk = result.records
    },

    //项目人员任免
    get_xmryrm: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'XMRYRM', pageSize: 6 })
      this.xmryrm = result.records
    },

    //部门发文
    get_bmfw: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'BMFW', pageSize: 6 })
      this.bmfw = result.records
    },

    //重点报道
    get_zdbd: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'ZDBD', pageSize: 6 })
      this.zdbd = result.records
    },

    //友情链接
    get_yqlj: async function () {
      let result = await PortalApi.newsCenterGetLinks({ type: '友情链接', page: 1, pageSize: 6 })
      this.yqlj = result.records
    },

    //分包公示
    get_fbgs: async function () {
      //系统脚本库 - 资讯中心_首页_分包公示_列表
      const userStore = useUserStore()
      let result = await PortalApi.execSystemScript({
        code: 'SQL_ae0c00bd40c0463d920c662411019169',
        ExecData: JSON.stringify([
          { UserDeptID: userStore.getUser.deptId, UserID: userStore.getUser.id }
        ])
      })
      this.fbgs = result

      //系统脚本库 - 资讯中心_首页_分包公示_总数
      let fbgsResult = await PortalApi.execSystemScript({
        code: 'SQL_ae0400f1a54a4a1199c46d5296d9aed7',
        ExecData: ''
      })
      this.fbgs_count = result[0]['Column1']
    },

    //知识拓展
    get_zstz: async function () {
      let result = await PortalApi.newsCenterGetNewsListChildren({ code: 'ZSTZ', pageSize: 5 })
      this.zstz = result.records
    },

    //微课堂
    get_wkt: async function () {
      let result = await PortalApi.newsCenterGetNewsListChildren({ code: 'WKT', pageSize: 5 })
      this.wkt = result.records
    },

    //网络安全事件
    get_wlaqsj: async function () {
      let result = await PortalApi.newsCenterGetNewsListChildren({ code: 'WLAQSJ', pageSize: 5 })
      this.wlaqsj = result.records
    },
    //财务资金
    get_cwzj: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'CWZJ', pageSize: 5 })
      this.cwzj = result.records
    },
    //税务管理
    get_swgl: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'SWGL', pageSize: 5 })
      this.swgl = result.records
    },
    //资产与产权管理
    get_zcycq: async function () {
      let result = await PortalApi.newsCenterGetNewsList({ code: 'ZCYCQ', pageSize: 5 })
      this.zcycq = result.records
    },
    //科技创新园地
    get_kjcxy: async function () {
      let result = await PortalApi.newsCenterGetTechnologicalInnovation({ pageSize: 11 })
      //循环data，反序列化list为json格式
      // for (let i = 0; i < result.length; i++) {
      //   result[i].list = JSON.parse(result[i].list)
      // }
      this.kjcxy = result
    },

    //截取日期，格式：月-日
    format_datetime: function (time) {
      let date_str = /\d{4}-\d{1,2}-\d{1,2}/g.exec(time)[0]
      let date_arr = date_str.split('-')
      return date_arr[1] + '-' + date_arr[2]
    },

    //截取日期，格式：年-月-日
    format_date: function (time) {
      let date_str = /\d{4}-\d{1,2}-\d{1,2}/g.exec(time)[0]
      return date_str
    },

    //新闻详情url
    detail_url: function (code, id, cate) {
      return (
        '/Portal/NewsCenter/NewsDetail?Code=' +
        code +
        '&ID=' +
        id +
        '&navigation=' +
        encodeURI(cate)
      ) //url中文编码
    },

    //分包公示详情url
    fbgs_detail_url: function (_id) {
      return (
        '/UIBuilder/UIViewer/FormViewer?TempletCode=Page_ac4000eb3bc34ec395451892f25ae8b5&FuncType=View&ID=' +
        _id +
        '&token=' +
        getAccessToken()
      )
    },

    //头部新闻baner图片url
    _url: function (url) {
      if (url != null && url != '') {
        if (url.indexOf('ftp://') == -1) {
          //排除ftp链接
          if (url.indexOf('?') > 0) {
            url += '&' //如果已经有？号则拼接&符号
          } else {
            url += '?' //如果没有？号则拼接?符号
          }

          if (url.indexOf('token') == -1) {
            //最后拼接token
            url += 'token=' + getAccessToken()
          }
        }
      }
      return url
    }
  }
}
</script>

<style lang="scss" scoped>
@import url('../../../assets/css/uk.min.css');
@import url('../../../assets/css/newsCenterMaster.css');
@import url('../../../assets/css/bootstrap.min.css');
@import url('../../../assets/css/newsCenterIndex.css');
@import url('../../../assets/css/swiper.min.css');
@import url('../../../assets/css/uikit.min.css');
@import url('../../../assets/css/font-awesome.css');
.qkg {
  display: inline-block;
  background: none;
  border: none;
  color: #fff;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 0;
  font-family: '微软雅黑';
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.footViewStyle {
  /*底部视图*/
  // background-color: #F4F5F6;
  width: 100%;
  margin-top: auto;
}

.body-inner {
  width: 1378px;
  /*margin-left: 262px;*/
  margin: 0 auto;
  height: 100%;
  background-color: transparent;
  margin-top: 74px;
}

.hbm-zxzx-body-leftbar1 {
  ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }
}

.gcs_ztzl {
  padding: 0;
}
</style>
