<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:A-res:create']"
        >
          <Icon icon="ep:plus" class="mr-5px"/>
          新增
        </el-button>
        <el-button
          type="danger"
          @click="handleDelete(deleteId)"
          v-hasPermi="['system:A-res:delete']"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-row v-loading="loading" style="min-height: 60vh">
      <el-col :span="8">

        <ResEditTree v-if="treeDataLoaded" @node-clicked="nodeClicked" :data="treeData"
                     @node-contextmenu="nodeContextmenu"/>


        <!-- 分页 -->

      </el-col>
      <el-col :span="14" :offset="1">
        <AResForm ref="formRef" v-if="formif" :data="treeData" @success="getList"/>
      </el-col>
    </el-row>

  </ContentWrap>
  <AResFormAdd ref="formRefAdd" @success="getList"/>

  <!-- 表单弹窗：添加/修改 -->
</template>

<script setup lang="ts">
import {AResApi} from '@/api/system/ares'
import AResForm from './AResForm.vue'
import AResFormAdd from './AResFormAdd.vue';
import {ResEditTree} from '@/components/ResEditTree'
import * as ByResourceApi from '@/api/system/byResource'

const formif = ref(false);
let deleteId = ref();
const loading = ref(true);
/** 资源管理 列表 */
defineOptions({name: 'ARes'})
const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const queryParams = reactive({
  id: '',
  parentID: undefined,
  name: '',
  code: '',
  bootstrapCls: undefined,
  requireDept: undefined,
  useScope: undefined,
  keyWord: undefined,
  specialUser: undefined,
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  queryFormRef.value.resetFields()
  getTreeData();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getTreeData();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const formRefAdd = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AResApi.deleteARes(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getTreeData();
    openForm('create');
  } catch {
  }
}

// 树数据
const treeData = ref(null);
//树加载
const treeDataLoaded = ref(false);
const nodeId = ref('')
//树鼠标左键
const nodeClicked = (node) => {
  deleteId.value = node.key;
  nodeId.value = node.key
  formRef.value.open("edit", node.key)
  // getList();
}
//树鼠标右键
const nodeContextmenu = (node) => {
  nodeId.value = node.key
}
/** 获取树数据 */
const getTreeData = async () => {
  try {
    loading.value = true;

    //MenuRoot
    // queryParams.id = "MENU_ROOT";
    const response = await ByResourceApi.getResTree2(queryParams);

    if(response){
      loading.value = false;
      treeData.value =  eval("(" + response + ")");
      treeDataLoaded.value = false;
      await nextTick()
      treeDataLoaded.value = true;
      formif.value = true;
    }


  } catch (error) {
    console.error('Error fetching tree data:', error);
  }
};
onMounted(async () => {
  await getTreeData();
  // 获取窗口高度

})

</script>
