<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
    v-loading="formLoading"
  >
    <el-form-item label="父节点" prop="parentID">
      <el-tree-select
        clear
        v-model="formData.parentID"
        :data="aResTree"
        :props="defaultProps"
        check-strictly
        placeholder="请选择父节点"
      />
    </el-form-item>
    <el-row>
      <el-col :span="12">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称"/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入编码"/>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="是否是子系统" prop="isSubsystem">
          <el-select clearable v-model="formData.isSubsystem" placeholder="请输入是否是子系统">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="排序号" prop="sortIndex">
          <el-input v-model="formData.sortIndex" placeholder="请输入排序号"/>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="主界面地址" prop="url">
      <el-input v-model="formData.url" placeholder="请输入地址"/>
    </el-form-item>


    <el-row>
      <el-col :span="12">
        <el-form-item label="树节点图标" prop="iconCls">
          <IconSelect v-model="formData.iconCls" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="菜单字体图标" prop="bootstrapCls">
          <el-input v-model="formData.bootstrapCls" placeholder="菜单字体图标"/>
        </el-form-item>
      </el-col>
    </el-row>



    <el-row>
      <el-col :span="12">
        <el-form-item label="菜单图片" prop="menuImage">
          <UploadImg v-model="formData.menuImage"/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="打开方式" prop="execType">
          <el-select v-model="formData.execType"
                     placeholder="打开方式">
            <el-option label="工作区打开页面" value="ButtomIframe"/>
            <el-option label="弹出新窗口" value="OpenWindow"/>
            <el-option label="执行js函数" value="OpenWorkArea"/>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>


    <el-row>
      <el-col :span="12">
        <el-form-item label="是否刷新页面" prop="isRefreshPage">
          <el-select clearable v-model="formData.isRefreshPage" placeholder="是否刷新页面">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发布状态" prop="publishStatus">
          <el-select v-model="formData.publishStatus"
                     placeholder="发布状态">
            <el-option label="已发布" value="Published"/>
            <el-option label="未发布" value="Unpublished"/>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="关键词" prop="keyWord">
          <el-input v-model="formData.keyWord" placeholder="请输入关键词"/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否子菜单项" prop="isTitle">
          <el-select clearable v-model="formData.isTitle" placeholder="请输入是否是子系统">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="简要描述" prop="description">
      <el-input v-model="formData.description" type="textarea" rows="3" placeholder="简要描述"/>
    </el-form-item>
  </el-form>
  <div class="center-button">
    <el-button @click="submitForm" type="primary" :disabled="formLoading">保 存</el-button>
  </div>
</template>
<script setup lang="ts">
import {getStrDictOptions, DICT_TYPE} from '@/utils/dict'
import {AResApi, AResVO} from '@/api/system/ares'
import {defaultProps, handleTree} from '@/utils/tree'

/** 资源管理 表单 */
defineOptions({name: 'AResForm'})
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  showCheckbox:{
    type: Boolean,
    required: false,
  },
  checkedKeys:{
    type: Array,
    required: false,
  }

});
const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('create') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  parentID: undefined,
  fullID: undefined,
  sortIndex: undefined,
  name: undefined,
  code: undefined,
  type: undefined,
  iconCls: undefined,
  url: undefined,
  ctrlType: undefined,
  auth: undefined,
  description: undefined,
  systemCode: undefined,
  createUser: undefined,
  createUserID: undefined,
  createTime: undefined,
  modifyUser: undefined,
  modifyUserID: undefined,
  modifyTime: undefined,
  publishStatus: undefined,
  prePublishStatus: undefined,
  isDeleted: undefined,
  execType: undefined,
  scriptContent: undefined,
  releateConfigID: undefined,
  isRefreshPage: undefined,
  bootstrapCls: undefined,
  requireDept: undefined,
  useScope: undefined,
  keyWord: undefined,
  specialUser: undefined,
  isSubsystem: undefined,
  businessCategory: undefined,
  files: undefined,
  isenterprise: undefined,
  abbreviation: undefined,
  servicereamrk: undefined,
  institutions: undefined,
  business: undefined,
  serviceDirectory: undefined,
  inRes: undefined,
  inResName: undefined,
  outRes: undefined,
  outResName: undefined,
  isProtal: undefined,
  otherFile: undefined,
  menuImage: undefined,
  isTitle: undefined,
  sqrole: undefined,
  sqorg: undefined,
  squser: undefined,
  sqroleId: undefined,
  sqorgId: undefined,
  squserId: undefined,
  keyWordId: undefined,
  glywId: undefined,
  glyw: undefined,
  glzdId: undefined,
  glzd: undefined,
  sqRoleCount: undefined,
  sqUserCount: undefined,
  sxYewu: undefined,
  sxYewuName: undefined,
  xxYewu: undefined,
  xxYewuName: undefined,
  dgRelatedFiles: undefined,
  authUserID: undefined,
  authUserName: undefined,
  authRoleID: undefined,
  authRoleName: undefined,
  dataAuth: undefined,
  ctrAuth: undefined,
})
const formRules = reactive({
  name: [{required: true, message: '名称不能为空', trigger: 'blur'}],
  parentID: [{required: true, message: '父节点不能为空', trigger: 'blur'}],
})
const formRef = ref() // 表单 Ref
const aResTree = props.data// 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AResApi.getARes(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗


/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AResVO
    if (formType.value === 'create') {
      await AResApi.createARes(data)
      message.success(t('common.createSuccess'))
    } else {
      await AResApi.updateARes(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    parentID: undefined,
    fullID: undefined,
    sortIndex: undefined,
    name: undefined,
    code: undefined,
    type: undefined,
    iconCls: undefined,
    url: undefined,
    ctrlType: undefined,
    auth: undefined,
    description: undefined,
    systemCode: undefined,
    createUser: undefined,
    createUserID: undefined,
    createTime: undefined,
    modifyUser: undefined,
    modifyUserID: undefined,
    modifyTime: undefined,
    publishStatus: undefined,
    prePublishStatus: undefined,
    isDeleted: undefined,
    execType: undefined,
    scriptContent: undefined,
    releateConfigID: undefined,
    isRefreshPage: undefined,
    bootstrapCls: undefined,
    requireDept: undefined,
    useScope: undefined,
    keyWord: undefined,
    specialUser: undefined,
    isSubsystem: undefined,
    businessCategory: undefined,
    files: undefined,
    isenterprise: undefined,
    abbreviation: undefined,
    servicereamrk: undefined,
    institutions: undefined,
    business: undefined,
    serviceDirectory: undefined,
    inRes: undefined,
    inResName: undefined,
    outRes: undefined,
    outResName: undefined,
    isProtal: undefined,
    otherFile: undefined,
    menuImage: undefined,
    isTitle: undefined,
    sqrole: undefined,
    sqorg: undefined,
    squser: undefined,
    sqroleId: undefined,
    sqorgId: undefined,
    squserId: undefined,
    keyWordId: undefined,
    glywId: undefined,
    glyw: undefined,
    glzdId: undefined,
    glzd: undefined,
    sqRoleCount: undefined,
    sqUserCount: undefined,
    sxYewu: undefined,
    sxYewuName: undefined,
    xxYewu: undefined,
    xxYewuName: undefined,
    dgRelatedFiles: undefined,
    authUserID: undefined,
    authUserName: undefined,
    authRoleID: undefined,
    authRoleName: undefined,
    dataAuth: undefined,
    ctrAuth: undefined,
  }
  formRef.value?.resetFields()
}





</script>

