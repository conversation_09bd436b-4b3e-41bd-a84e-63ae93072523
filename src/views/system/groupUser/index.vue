<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="部门名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="部门简称" prop="shortName">
        <el-input
          v-model="queryParams.shortName"
          placeholder="请输入部门简称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label-width="120px" label="部门创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:A-group:create']"
        >
          <Icon icon="ep:plus" class="mr-5px"/>
          新增
        </el-button>
        <el-button
          type="danger"
          @click="handleDelete(selectedObj.id)"
          v-hasPermi="['system:A-res:delete']"
        >
          删除
        </el-button>
        <el-button
          type="primary"
          @click="openForm('update', selectedObj.id);"
          v-hasPermi="['system:A-group:update']"
        >
          编辑
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-row>
      <el-col :span="6">
        <div class="tree_container height70">
          <div class="scrollable-row width220">
            <DeptTree ref="deptTreeRef"  @node-click="handleDeptNodeClick" />
          </div>
        </div>
      </el-col>
      <el-col :span="16" :offset="1">
        <UserList ref="childRef"/>
      </el-col>
    </el-row>
  </ContentWrap>
  <!-- 表单弹窗：添加/修改 -->
  <AGroupForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {handleTree} from '@/utils/tree'
import download from '@/utils/download'
import {AGroupApi, AGroupVO} from '@/api/system/group'
import UserList from '../auser/UserList.vue'
import AGroupForm from "@/views/system/groupUser/AGroupForm.vue";
import DeptTree from './DeptTree.vue'

/** 系统角色管理 列表 */
defineOptions({name: 'AGroup'})
const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AGroupVO[]>([]) // 列表的数据
const queryParams = reactive({
  name: undefined,
  shortName: undefined,
  createTime: [],
  groupType: '',
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.groupType = "Org";
    const data = await AGroupApi.getAGroupList(queryParams)
    list.value = handleTree(data, 'id', 'parentID')
  } finally {
    loading.value = false
  }
}
//选中的部门id
let selectedObj = ref();
const childRef = ref();

const deptTreeRef = ref();
/** 搜索按钮操作 */
const handleQuery = () => {
  deptTreeRef.value.startQuery(queryParams);
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {

  formRef.value.open(type, id)
  nextTick()
  if(!id){
    parentIdSet()
  }
}




/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm('确定删除"'+selectedObj.value.name+'"吗?')
    // 发起删除
    await AGroupApi.deleteAGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    deptTreeRef.value.startQuery(queryParams);
  } catch {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AGroupApi.exportAGroup(queryParams)
    download.excel(data, '系统角色管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 展开/折叠操作 */
const isExpandAll = ref(true) // 是否展开，默认全部展开
const refreshTable = ref(true) // 重新渲染表格状态
const toggleExpandAll = async () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  await nextTick()
  deptTreeRef.value.opentree(isExpandAll.value);
  refreshTable.value = true
}

/** 初始化 **/
onMounted(() => {
  getList()
})
/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  selectedObj.value = row;
  childRef.value.open(row.id)
}

const parentIdSet = ()=>{
  formRef.value.parentIdSet2(selectedObj.value.parentID)
}


</script>
