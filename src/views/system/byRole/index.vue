<template>
  <el-row>
    <el-col :span="12" style="background-color: white">
      <el-tabs
        v-model="activeName"
        type="card"
        class="demo-tabs"
        tab-position="left"
        @tab-click="handleClick"
      >
        <el-tab-pane label="按照角色" style="background-color: white" name="Role">
          <el-row>
            <div class="search-bar">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入角色名称"
                class="!w-240px"
                @keyup.enter="handleQuery"
              >
                <template #append>
                  <el-button @click="handleQuery">
                    <Icon icon="ep:search" class="mr-5px"/>
                  </el-button>
                </template>
              </el-input>
            </div>
          </el-row>
          <el-row>
            <div class="tree_container height60">
              <div class="scrollable-row width100">
                <!-- 列表 -->
                <el-table v-loading="loading" :data="list" ref="taskTableRef" @row-click="handleRowClick" highlight-current-row
                          :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
                  <el-table-column type="selection" width="55" />
                  <el-table-column label="角色名称" prop="name"/>
                </el-table>
                <!-- 分页 -->
              </div>
            </div>

          </el-row>
          <el-row>
            <ContentWrap style="width: 100%;  border: 0.1px solid #CCCCCC">
              <Pagination
                :total="total"
                v-model:page="queryParams.pageNo"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
                layout="sizes,prev, pager, next"
              />
            </ContentWrap>
          </el-row>

        </el-tab-pane>
        <el-tab-pane label="按照组织" name="Org">
          <div class="tree_container height70">
            <div class="scrollable-row width100">
              <DeptTree ref="deptTreeRef"  @node-click="handleRowClick" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>


    </el-col>
    <el-col :span="12">
      <div class="tree_container height85">
        <div class="scrollable-row width100">
          <Tree ref="resTreeRef" v-if="treeDataLoaded" :data="treeData" @node-clicked="nodeClicked" @save-sucess="saveSucess" @node-contextmenu="nodeContextmenu" />
        </div>
      </div>
    </el-col>
  </el-row>

  <!-- 表单弹窗：添加/修改 -->
  <AGroupForm ref="formRef" @success="getList"/>
</template>

<script setup lang="ts">
import {AGroupApi, AGroupVO} from '@/api/system/group'
import AGroupForm from './AGroupForm.vue'
import {Tree} from "@/components/ResTree";
import * as ByResourceApi from '@/api/system/byResource'

/** 组织管理 列表 */
defineOptions({name: 'AGroup'})

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AGroupVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  shortName: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AGroupApi.getAGroupPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AGroupApi.deleteAGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
const taskTableRef = ref(); // 表格ref

const handleSelectionChange = (selection) => {
  console.log(selection);
  // 只保留最后一个选中的行
  if (selection.length > 1) {
    let del_row = selection.shift();
    taskTableRef.value.toggleRowSelection(del_row, false); // 用于多选表格，切换某一行的选中状态，如果使用了第二个参数，则是设置这一行选中与否（selected 为 true 则选中）；第二个参数为true时又变成了多选
  }
};
const resTreeRef = ref()
const selectedRow = ref();

const handleRowClick = async (row)=>{
  selectedRow.value = row;
  treeDataLoaded.value = false;
  //通过row.id获取选择节点的ids
  const ids = await AGroupApi.getResIdsByGroupId(row.id);
  if (ids) {
    treeDataLoaded.value = true;
    await nextTick()
    resTreeRef.value.open(ids, row.id);
  }
}

const saveSucess = async ()=>{
  treeDataLoaded.value = false;
  //通过row.id获取选择节点的ids
  const ids = await AGroupApi.getResIdsByGroupId(selectedRow.value.id);
  if (ids) {
    treeDataLoaded.value = true;
    await nextTick()
    resTreeRef.value.open(ids, selectedRow.value.id);
  }
}
// 树数据
const treeData = ref(null);
//树加载
const treeDataLoaded = ref(false);
const nodeId = ref('')
//树鼠标左键
const nodeClicked = (node) => {
  nodeId.value = node.key
  getList();
}
//树鼠标右键
const nodeContextmenu = (node) => {
  nodeId.value = node.key
}
/** 获取树数据 */
const getTreeData = async () => {
  try {
    // const response = await ByResourceApi.getResTree({ id: 'MenuRoot', name: null });
    const response = await ByResourceApi.getResTree2(queryParams);
    treeData.value =  eval("(" + response + ")");
    treeDataLoaded.value = true;
  } catch (error) {
    console.error('Error fetching tree data:', error);
  }
};
onMounted(async () => {
  await getTreeData();
  // 获取窗口高度

})
import type { TabsPaneContext } from 'element-plus'
import DeptTree from "@/views/system/groupUser/DeptTree.vue";

const activeName = ref('Role')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab.paneName, event)
  console.log(tab, event)
}

</script>
<style>
.demo-tabs > .el-tabs__content {
  padding: 0px;
/*  color: #6b778c;
  font-size: 32px;
  font-weight: 600;*/
}
.search-bar {
  width: 100%;
  padding: 5px;
  background-color: white;
  margin-bottom: 5px;
  border: 0.1px solid #CCCCCC
}
.el-tabs__item {
  writing-mode: vertical-rl;
  white-space: nowrap;
  height: auto;
  width: 30px; /* 根据需要调整 */
  text-align: center;
  padding: 5px 0;
}
</style>
