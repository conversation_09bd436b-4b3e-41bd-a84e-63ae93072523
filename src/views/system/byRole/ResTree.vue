

<template>
  <el-tree
    v-model="parentID"
    :data="aResTree"
    :props="defaultProps"
    node-key="id"
    placeholder="请选择父节点"
  />
</template>
<script setup lang="ts">

import {AResApi, AResVO} from '@/api/system/ares'
import {defaultProps, handleTree} from '@/utils/tree'

const aResTree = ref() // 树形结构
const parentID = ref();

const queryParams = reactive({
  id: undefined,
  parentID: "",
  name: undefined,
  code: undefined,
  bootstrapCls: undefined,
  requireDept: undefined,
  useScope: undefined,
  keyWord: undefined,
  specialUser: undefined,
})
/** 获得资源管理树 */
const getAResTree = async () => {
  // if(aResTree||aResTree.length==0){
  aResTree.value = []
  const data = await AResApi.getAResList(queryParams)
  const root: Tree = {id: 0, name: '资源管理', children: []}
  root.children = handleTree(data, 'id', 'parentID')
  aResTree.value.push(root)
  // }
}
const open = async (id?: string) => {
  if (id != null) {
    // queryParams.parentID = id;
  }
  await getAResTree()
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

</script>
<style scoped lang="scss">

</style>
