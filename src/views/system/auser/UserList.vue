<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="姓名" prop="name" >
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="工号" prop="workNo">
        <el-input
          v-model="queryParams.workNo"
          placeholder="请输入工号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />

      </el-form-item>
      <el-form-item label="座机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入座机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="最后登录IP" prop="lastLoginIP">
        <el-input
          v-model="queryParams.lastLoginIP"
          placeholder="请输入最后登录IP"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!--      <el-form-item label="错误时间" prop="errorTime">
              <el-date-picker
                v-model="queryParams.errorTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="修改时间" prop="modifyTime">
              <el-date-picker
                v-model="queryParams.modifyTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>-->
      <el-form-item label="手机" prop="mobilePhone">
        <el-input
          v-model="queryParams.mobilePhone"
          placeholder="请输入手机"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-row>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
          <el-button
            type="primary"
            plain
            @click="openForm('create')"
            v-hasPermi="['system:A-user:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
          <el-button
            type="danger"
            plain
            @click="openPasswordForm()"
            v-hasPermi="['system:A-user:create']"
          >
            <Icon icon="ep:money" class="mr-5px" /> 重置密码
          </el-button>
          <el-button
            type="success"
            plain
            @click="handleExport"
            :loading="exportLoading"
            v-hasPermi="['system:A-user:export']"
          >
            <Icon icon="ep:download" class="mr-5px" /> 导出
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list"     ref="myTable"
              :stripe="true" :show-overflow-tooltip="true" @sort-change="handleSortChange"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"/>
      <el-table-column label="部门名称" align="center" prop="deptName"  sortable="custom" />
      <el-table-column label="工号" align="center" prop="workNo" sortable="custom" />
      <el-table-column label="姓名" align="center" prop="name" sortable="custom" />
      <el-table-column label="职务" align="center" prop="duties" sortable="custom" />
      <el-table-column label="性别" align="center" prop="sex" sortable="custom"/>
      <el-table-column label="座机" align="center" prop="phone" sortable="custom"/>
      <el-table-column label="手机" align="center" prop="mobilePhone" sortable="custom"/>
      <el-table-column label="最后登录IP" align="center" prop="lastLoginIP" sortable="custom"/>

      <!--
            <el-table-column label="登录名" align="center" prop="loginName" />
            <el-table-column label="排序" align="center" prop="sortIndex" />
            <el-table-column label="兼职部门" align="center" prop="parttimeDeptID" />
            <el-table-column label="兼职部门" align="center" prop="parttimeDeptName" />
            <el-table-column label="是否授权" align="center" prop="isAuth">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.isAuth" />
              </template>
            </el-table-column>
            <el-table-column label="错误次数" align="center" prop="errorCount" />
            <el-table-column
              label="错误时间"
              align="center"
              prop="errorTime"
              :formatter="dateFormatter"
              width="180px"
            />
            <el-table-column
              label="修改时间"
              align="center"
              prop="modifyTime"
              :formatter="dateFormatter"
              width="180px"
            />
            <el-table-column label="备注" align="center" prop="description" />
            <el-table-column label="Emai" align="center" prop="email" />
            <el-table-column label="地址" align="center" prop="address" />
            <el-table-column
              label="生日"
              align="center"
              prop="birthday"
              :formatter="dateFormatter"
              width="180px"
            />
            <el-table-column label="默认IP" align="center" prop="clientIp" />
            <el-table-column label="签名密码" align="center" prop="signPwd" />
            <el-table-column label="子系统编号" align="center" prop="systemCode" />
            <el-table-column label="是否接收手机短信" align="center" prop="acceptMobileMsg" />
            <el-table-column label="状态" align="center" prop="status" />
            <el-table-column label="身份证照片" align="center" prop="iDCardImg" />
            <el-table-column label="用户照片" align="center" prop="userImg" />
            <el-table-column label="身份证反面" align="center" prop="iDCardImgF" />
            <el-table-column label="Ext1" align="center" prop="ext1" />
            <el-table-column label="OfficeId" align="center" prop="officeId" />
            <el-table-column label="OfficeName" align="center" prop="officeName" />
            <el-table-column label="ucmobile" align="center" prop="ucmobile" />
            <el-table-column label="uctitle" align="center" prop="uctitle" />
            <el-table-column label="ucshort" align="center" prop="ucshort" />
            <el-table-column label="ucmail" align="center" prop="ucmail" />
            <el-table-column label="thirdPartMail" align="center" prop="thirdPartMail" />
            <el-table-column label="voipNumber" align="center" prop="voipNumber" />
            <el-table-column label="OutKey" align="center" prop="outKey" />
            <el-table-column label="身份证号" align="center" prop="idCard" />
            <el-table-column label="用户类型" align="center" prop="userType" />
            <el-table-column label="isAuthority" align="center" prop="isAuthority" />
            <el-table-column label="民族" align="center" prop="nation" />
            <el-table-column label="曾用名" align="center" prop="beforeName" />
            <el-table-column label="政治面貌" align="center" prop="politics" />
            <el-table-column label="OfficeFullID" align="center" prop="officeFullID" />-->
      <el-table-column label="操作" align="center" width="200px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:A-user:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:A-user:delete']"
          >
            删除
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleUnlock(scope.row.id)"
            v-hasPermi="['system:A-user:delete']"
          >
            解锁
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AUserForm ref="formRef" @success="getList" />
  <PasswordReset ref="passwordRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { AUserApi, AUserVO } from '@/api/system/auser'
import AUserForm from './AUserForm.vue'
import PasswordReset from "@/views/system/auser/PasswordReset.vue";
/** 个人设置 列表 */
defineOptions({ name: 'AUser' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AUserVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  loginName: undefined,
  workNo: undefined,
  deptName: undefined,
  deptId: undefined,
  lastLoginIP: undefined,
  errorTime: [],
  modifyTime: [],
  mobilePhone: undefined,
  email: undefined,
  address: undefined,
  idCard: undefined,
  isAuthority: undefined,
  phone: undefined,
  name: undefined,
  sortColumn: "sortIndex",
  sortOrder: "asc",
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AUserApi.getAUserPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.sortColumn = "sortIndex";
  queryParams.sortOrder = "asc";
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}




/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AUserApi.deleteAUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 解锁按钮操作 */
const handleUnlock = async (id: number) => {
  try {
    // 删除的二次确认
    await message.confirm('解锁后，用户将可以正常登录系统，确定要解锁吗？')
    // 发起删除
    await AUserApi.handleUnlock(id)
    message.success(t('解锁成功'))
    // 刷新列表
    await getList()
  } catch {}
}
const open=async(id:string)=>{
  queryParams.deptID = id;
  getList();
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AUserApi.exportAUser(queryParams)
    download.excel(data, '个人设置.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
const handleSortChange = (column: any) => {
  queryParams.sortColumn = column.prop;
  queryParams.sortOrder = column.order;
  getList();
};
const myTable = ref()
const handleSelectionChange = (val) => {
  // 这部分代码是让复选框设置为单选
  val.map( (row,index) => {
    if(val.length <=1 ){
      return
    }
    // toggleRowSelection 用于多选表格，切换某一行的选中状态， 如果使用了第二个参数，则可直接设置这一行选中与否
    myTable.value.toggleRowSelection(row,false)
    if(index === val.length-1){
      myTable.value.toggleRowSelection(row,true)
    }
  })
}
let passwordRef = ref();
const openPasswordForm =  () => {
  let selectionRows = myTable.value.getSelectionRows();
  if (selectionRows.length < 1) {
    message.error("请先选择用户");
    return;
  }
  if (selectionRows.length > 1) {
    message.error("最多只能选择一个用户");
    return;
  }

  if (selectionRows.length == 1) {
    passwordRef.value.open(selectionRows[0].id)
  }
}

</script>
