<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="登录名" prop="loginName">
        <el-input v-model="formData.loginName" placeholder="请输入登录名" />
      </el-form-item>
      <el-form-item label="工号" prop="workNo">
        <el-input v-model="formData.workNo" placeholder="请输入工号" />
      </el-form-item>

      <el-form-item label="密码" prop="password">
        <el-input v-model="formData.password" placeholder="请输入密码" />
      </el-form-item>
      <el-form-item label="排序" prop="sortIndex">
        <el-input v-model="formData.sortIndex" placeholder="请输入排序" />
      </el-form-item>
      <el-form-item label="部门ID" prop="deptID">
        <el-input v-model="formData.deptID" placeholder="请输入部门ID" />
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName">
        <el-input v-model="formData.deptName" placeholder="请输入部门名称" />
      </el-form-item>
      <el-form-item label="部门完整ID" prop="deptFullID">
        <el-input v-model="formData.deptFullID" placeholder="请输入部门完整ID" />
      </el-form-item>
      <el-form-item label="兼职部门" prop="parttimeDeptID">
        <el-input v-model="formData.parttimeDeptID" placeholder="请输入兼职部门" />
      </el-form-item>
      <el-form-item label="兼职部门" prop="parttimeDeptName">
        <el-input v-model="formData.parttimeDeptName" placeholder="请输入兼职部门" />
      </el-form-item>
      <el-form-item label="是否授权" prop="isAuth">
        <el-select v-model="formData.isAuth" placeholder="请选择是否授权">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否删除" prop="isDeleted">
        <el-select v-model="formData.isDeleted" placeholder="请选择是否删除">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="删除时间" prop="deleteTime">
        <el-date-picker
          v-model="formData.deleteTime"
          type="date"
          value-format="x"
          placeholder="选择删除时间"
        />
      </el-form-item>
      <el-form-item label="最后登录时间" prop="lastLoginTime">
        <el-date-picker
          v-model="formData.lastLoginTime"
          type="date"
          value-format="x"
          placeholder="选择最后登录时间"
        />
      </el-form-item>
      <el-form-item label="最后登录IP" prop="lastLoginIP">
        <el-input v-model="formData.lastLoginIP" placeholder="请输入最后登录IP" />
      </el-form-item>
      <el-form-item label="错误次数" prop="errorCount">
        <el-input v-model="formData.errorCount" placeholder="请输入错误次数" />
      </el-form-item>
      <el-form-item label="错误时间" prop="errorTime">
        <el-date-picker
          v-model="formData.errorTime"
          type="date"
          value-format="x"
          placeholder="选择错误时间"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="modifyTime">
        <el-date-picker
          v-model="formData.modifyTime"
          type="date"
          value-format="x"
          placeholder="选择修改时间"
        />
      </el-form-item>
      <el-form-item label="备注" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="formData.sex" placeholder="请选择性别">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入职日期" prop="inDate">
        <el-date-picker
          v-model="formData.inDate"
          type="date"
          value-format="x"
          placeholder="选择入职日期"
        />
      </el-form-item>
      <el-form-item label="离职日期" prop="outDate">
        <el-date-picker
          v-model="formData.outDate"
          type="date"
          value-format="x"
          placeholder="选择离职日期"
        />
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入电话" />
      </el-form-item>
      <el-form-item label="手机" prop="mobilePhone">
        <el-input v-model="formData.mobilePhone" placeholder="请输入手机" />
      </el-form-item>
      <el-form-item label="Emai" prop="email">
        <el-input v-model="formData.email" placeholder="请输入Emai" />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入地址" />
      </el-form-item>
      <el-form-item label="职务" prop="duties">
        <el-input v-model="formData.duties" placeholder="请输入职务" />
      </el-form-item>
      <el-form-item label="生日" prop="birthday">
        <el-date-picker
          v-model="formData.birthday"
          type="date"
          value-format="x"
          placeholder="选择生日"
        />
      </el-form-item>
      <el-form-item label="默认IP" prop="clientIp">
        <el-input v-model="formData.clientIp" placeholder="请输入默认IP" />
      </el-form-item>
      <el-form-item label="签名密码" prop="signPwd">
        <el-input v-model="formData.signPwd" placeholder="请输入签名密码" />
      </el-form-item>
      <el-form-item label="子系统编号" prop="systemCode">
        <el-input v-model="formData.systemCode" placeholder="请输入子系统编号" />
      </el-form-item>
      <el-form-item label="SortIndex1" prop="sortIndex1">
        <el-input v-model="formData.sortIndex1" placeholder="请输入SortIndex1" />
      </el-form-item>
      <el-form-item label="SortIndex2" prop="sortIndex2">
        <el-input v-model="formData.sortIndex2" placeholder="请输入SortIndex2" />
      </el-form-item>
      <el-form-item label="SortIndex3" prop="sortIndex3">
        <el-input v-model="formData.sortIndex3" placeholder="请输入SortIndex3" />
      </el-form-item>
      <el-form-item label="SortIndex4" prop="sortIndex4">
        <el-input v-model="formData.sortIndex4" placeholder="请输入SortIndex4" />
      </el-form-item>
      <el-form-item label="SortIndex5" prop="sortIndex5">
        <el-input v-model="formData.sortIndex5" placeholder="请输入SortIndex5" />
      </el-form-item>
      <el-form-item label="是否接收手机短信" prop="acceptMobileMsg">
        <el-input v-model="formData.acceptMobileMsg" placeholder="请输入是否接收手机短信" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="身份证照片" prop="iDCardImg">
        <el-input v-model="formData.iDCardImg" placeholder="请输入身份证照片" />
      </el-form-item>
      <el-form-item label="用户照片" prop="userImg">
        <el-input v-model="formData.userImg" placeholder="请输入用户照片" />
      </el-form-item>
      <el-form-item label="身份证反面" prop="iDCardImgF">
        <el-input v-model="formData.iDCardImgF" placeholder="请输入身份证反面" />
      </el-form-item>
      <el-form-item label="Ext1" prop="ext1">
        <el-input v-model="formData.ext1" placeholder="请输入Ext1" />
      </el-form-item>
      <el-form-item label="OfficeId" prop="officeId">
        <el-input v-model="formData.officeId" placeholder="请输入OfficeId" />
      </el-form-item>
      <el-form-item label="OfficeName" prop="officeName">
        <el-input v-model="formData.officeName" placeholder="请输入OfficeName" />
      </el-form-item>
      <el-form-item label="ucmobile" prop="ucmobile">
        <el-input v-model="formData.ucmobile" placeholder="请输入ucmobile" />
      </el-form-item>
      <el-form-item label="uctitle" prop="uctitle">
        <el-input v-model="formData.uctitle" placeholder="请输入uctitle" />
      </el-form-item>
      <el-form-item label="ucshort" prop="ucshort">
        <el-input v-model="formData.ucshort" placeholder="请输入ucshort" />
      </el-form-item>
      <el-form-item label="ucmail" prop="ucmail">
        <el-input v-model="formData.ucmail" placeholder="请输入ucmail" />
      </el-form-item>
      <el-form-item label="thirdPartMail" prop="thirdPartMail">
        <el-input v-model="formData.thirdPartMail" placeholder="请输入thirdPartMail" />
      </el-form-item>
      <el-form-item label="voipNumber" prop="voipNumber">
        <el-input v-model="formData.voipNumber" placeholder="请输入voipNumber" />
      </el-form-item>
      <el-form-item label="OutKey" prop="outKey">
        <el-input v-model="formData.outKey" placeholder="请输入OutKey" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input v-model="formData.idCard" placeholder="请输入身份证号" />
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="formData.userType" placeholder="请选择用户类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="isAuthority" prop="isAuthority">
        <el-input v-model="formData.isAuthority" placeholder="请输入isAuthority" />
      </el-form-item>
      <el-form-item label="民族" prop="nation">
        <el-input v-model="formData.nation" placeholder="请输入民族" />
      </el-form-item>
      <el-form-item label="曾用名" prop="beforeName">
        <el-input v-model="formData.beforeName" placeholder="请输入曾用名" />
      </el-form-item>
      <el-form-item label="政治面貌" prop="politics">
        <el-input v-model="formData.politics" placeholder="请输入政治面貌" />
      </el-form-item>
      <el-form-item label="OfficeFullID" prop="officeFullID">
        <el-input v-model="formData.officeFullID" placeholder="请输入OfficeFullID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AUserApi, AUserVO } from '@/api/system/auser'

/** 个人设置 表单 */
defineOptions({ name: 'AUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  loginName: undefined,
  workNo: undefined,
  password: undefined,
  sortIndex: undefined,
  deptID: undefined,
  deptName: undefined,
  deptFullID: undefined,
  parttimeDeptID: undefined,
  parttimeDeptName: undefined,
  isAuth: undefined,
  isDeleted: undefined,
  deleteTime: undefined,
  lastLoginTime: undefined,
  lastLoginIP: undefined,
  errorCount: undefined,
  errorTime: undefined,
  modifyTime: undefined,
  description: undefined,
  sex: undefined,
  inDate: undefined,
  outDate: undefined,
  phone: undefined,
  mobilePhone: undefined,
  email: undefined,
  address: undefined,
  duties: undefined,
  birthday: undefined,
  clientIp: undefined,
  signPwd: undefined,
  systemCode: undefined,
  sortIndex1: undefined,
  sortIndex2: undefined,
  sortIndex3: undefined,
  sortIndex4: undefined,
  sortIndex5: undefined,
  acceptMobileMsg: undefined,
  status: undefined,
  iDCardImg: undefined,
  userImg: undefined,
  iDCardImgF: undefined,
  ext1: undefined,
  officeId: undefined,
  officeName: undefined,
  ucmobile: undefined,
  uctitle: undefined,
  ucshort: undefined,
  ucmail: undefined,
  thirdPartMail: undefined,
  voipNumber: undefined,
  outKey: undefined,
  idCard: undefined,
  userType: undefined,
  isAuthority: undefined,
  nation: undefined,
  beforeName: undefined,
  politics: undefined,
  officeFullID: undefined,
})
const formRules = reactive({
  isDeleted: [{ required: true, message: '是否删除不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AUserApi.getAUser(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AUserVO
    if (formType.value === 'create') {
      await AUserApi.createAUser(data)
      message.success(t('common.createSuccess'))
    } else {
      await AUserApi.updateAUser(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    loginName: undefined,
    workNo: undefined,
    password: undefined,
    sortIndex: undefined,
    deptID: undefined,
    deptName: undefined,
    deptFullID: undefined,
    parttimeDeptID: undefined,
    parttimeDeptName: undefined,
    isAuth: undefined,
    isDeleted: undefined,
    deleteTime: undefined,
    lastLoginTime: undefined,
    lastLoginIP: undefined,
    errorCount: undefined,
    errorTime: undefined,
    modifyTime: undefined,
    description: undefined,
    sex: undefined,
    inDate: undefined,
    outDate: undefined,
    phone: undefined,
    mobilePhone: undefined,
    email: undefined,
    address: undefined,
    duties: undefined,
    birthday: undefined,
    clientIp: undefined,
    signPwd: undefined,
    systemCode: undefined,
    sortIndex1: undefined,
    sortIndex2: undefined,
    sortIndex3: undefined,
    sortIndex4: undefined,
    sortIndex5: undefined,
    acceptMobileMsg: undefined,
    status: undefined,
    iDCardImg: undefined,
    userImg: undefined,
    iDCardImgF: undefined,
    ext1: undefined,
    officeId: undefined,
    officeName: undefined,
    ucmobile: undefined,
    uctitle: undefined,
    ucshort: undefined,
    ucmail: undefined,
    thirdPartMail: undefined,
    voipNumber: undefined,
    outKey: undefined,
    idCard: undefined,
    userType: undefined,
    isAuthority: undefined,
    nation: undefined,
    beforeName: undefined,
    politics: undefined,
    officeFullID: undefined,
  }
  formRef.value?.resetFields()
}
</script>
