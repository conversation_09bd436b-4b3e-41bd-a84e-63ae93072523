<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="简称" prop="shortName">
        <el-input v-model="formData.shortName" placeholder="请输入简称" />
      </el-form-item>
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="请输入编号" />
      </el-form-item>
      <el-form-item label="父节点ID" prop="parentID">
        <el-tree-select
          v-model="formData.parentID"
          :data="aGroupTree"
          :props="defaultProps"
          check-strictly
          default-expand-all
          placeholder="请选择父节点ID"
        />
      </el-form-item>
      <el-form-item label="节点全路径" prop="fullID">
        <el-input v-model="formData.fullID" placeholder="请输入节点全路径" />
      </el-form-item>
      <el-form-item label="分组类型（组织或角色）" prop="groupType">
        <el-select v-model="formData.groupType" placeholder="请选择分组类型（组织或角色）">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="sortIndex">
        <el-input v-model="formData.sortIndex" placeholder="请输入排序" />
      </el-form-item>
      <el-form-item label="删除状态" prop="isDeleted">
        <el-input v-model="formData.isDeleted" placeholder="请输入删除状态" />
      </el-form-item>
      <el-form-item label="删除日期" prop="deleteTime">
        <el-date-picker
          v-model="formData.deleteTime"
          type="date"
          value-format="x"
          placeholder="选择删除日期"
        />
      </el-form-item>
      <el-form-item label="组织等级" prop="orgLevel">
        <el-input v-model="formData.orgLevel" placeholder="请输入组织等级" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
      <el-form-item label="所在地" prop="location">
        <el-input v-model="formData.location" placeholder="请输入所在地" />
      </el-form-item>
      <el-form-item label="子系统编号" prop="systemCode">
        <el-input v-model="formData.systemCode" placeholder="请输入子系统编号" />
      </el-form-item>
      <el-form-item label="外部数据Key" prop="outKey">
        <el-input v-model="formData.outKey" placeholder="请输入外部数据Key" />
      </el-form-item>
      <el-form-item label="数据库" prop="connName">
        <el-input v-model="formData.connName" placeholder="请输入数据库" />
      </el-form-item>
      <el-form-item label="查询SQL" prop="userSQL">
        <el-input v-model="formData.userSQL" placeholder="请输入查询SQL" />
      </el-form-item>
      <el-form-item label="部门全路径名称" prop="fullName">
        <el-input v-model="formData.fullName" placeholder="请输入部门全路径名称" />
      </el-form-item>
      <el-form-item label="组织角色" prop="orgRole">
        <el-input v-model="formData.orgRole" placeholder="请输入组织角色" />
      </el-form-item>
      <el-form-item label="所属分类" prop="categoryID">
        <el-input v-model="formData.categoryID" placeholder="请输入所属分类" />
      </el-form-item>
      <el-form-item label="所属分类名称" prop="categoryName">
        <el-input v-model="formData.categoryName" placeholder="请输入所属分类名称" />
      </el-form-item>
      <el-form-item label="使用分类" prop="useCategory">
        <el-input v-model="formData.useCategory" placeholder="请输入使用分类" />
      </el-form-item>
      <el-form-item label="属性分类" prop="propCategory">
        <el-input v-model="formData.propCategory" placeholder="请输入属性分类" />
      </el-form-item>
      <el-form-item label="是否是领导" prop="isleader">
        <el-input v-model="formData.isleader" placeholder="请输入是否是领导" />
      </el-form-item>
      <el-form-item label="creditCode" prop="creditCode">
        <el-input v-model="formData.creditCode" placeholder="请输入creditCode" />
      </el-form-item>
      <el-form-item label="DeptUnit" prop="deptUnit">
        <el-input v-model="formData.deptUnit" placeholder="请输入DeptUnit" />
      </el-form-item>
      <el-form-item label="DeptUnitName" prop="deptUnitName">
        <el-input v-model="formData.deptUnitName" placeholder="请输入DeptUnitName" />
      </el-form-item>
      <el-form-item label="Tlevel" prop="tlevel">
        <el-input v-model="formData.tlevel" placeholder="请输入Tlevel" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="formData.createTime"
          type="date"
          value-format="x"
          placeholder="选择创建时间"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="modifyTime">
        <el-date-picker
          v-model="formData.modifyTime"
          type="date"
          value-format="x"
          placeholder="选择修改时间"
        />
      </el-form-item>
      <el-form-item label="角色用户" prop="jsyh">
        <el-input v-model="formData.jsyh" placeholder="请输入角色用户" />
      </el-form-item>
      <el-form-item label="角色用户名称" prop="jSYHName">
        <el-input v-model="formData.jSYHName" placeholder="请输入角色用户名称" />
      </el-form-item>
      <el-form-item label="平台标识" prop="platformTag">
        <el-input v-model="formData.platformTag" placeholder="请输入平台标识" />
      </el-form-item>
      <el-form-item label="所属分类名称" prop="categoryIDName">
        <el-input v-model="formData.categoryIDName" placeholder="请输入所属分类名称" />
      </el-form-item>
      <el-form-item label="是否开发人员维护" prop="isEditable">
        <el-input v-model="formData.isEditable" placeholder="请输入是否开发人员维护" />
      </el-form-item>
      <el-form-item label="是否开发人员使用" prop="sfkfrysy">
        <el-input v-model="formData.sfkfrysy" placeholder="请输入是否开发人员使用" />
      </el-form-item>
      <el-form-item label="GlYwName" prop="glYwName">
        <el-input v-model="formData.glYwName" placeholder="请输入GlYwName" />
      </el-form-item>
      <el-form-item label="GlYwId" prop="glYwId">
        <el-input v-model="formData.glYwId" placeholder="请输入GlYwId" />
      </el-form-item>
      <el-form-item label="UserCountInRole" prop="userCountInRole">
        <el-input v-model="formData.userCountInRole" placeholder="请输入UserCountInRole" />
      </el-form-item>
      <el-form-item label="MenuCountInRole" prop="menuCountInRole">
        <el-input v-model="formData.menuCountInRole" placeholder="请输入MenuCountInRole" />
      </el-form-item>
      <el-form-item label="Cl_JSYHName" prop="clJsyhname">
        <el-input v-model="formData.clJsyhname" placeholder="请输入Cl_JSYHName" />
      </el-form-item>
      <el-form-item label="颜色" prop="color">
        <el-input v-model="formData.color" placeholder="请输入颜色" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AGroupApi, AGroupVO } from '@/api/system/group'
import { defaultProps, handleTree } from '@/utils/tree'

/** 系统角色管理 表单 */
defineOptions({ name: 'AGroupForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  name: undefined,
  shortName: undefined,
  code: undefined,
  parentID: undefined,
  fullID: undefined,
  groupType: undefined,
  type: undefined,
  sortIndex: undefined,
  isDeleted: undefined,
  deleteTime: undefined,
  orgLevel: undefined,
  description: undefined,
  location: undefined,
  systemCode: undefined,
  outKey: undefined,
  connName: undefined,
  userSQL: undefined,
  fullName: undefined,
  orgRole: undefined,
  categoryID: undefined,
  categoryName: undefined,
  useCategory: undefined,
  propCategory: undefined,
  isleader: undefined,
  creditCode: undefined,
  deptUnit: undefined,
  deptUnitName: undefined,
  tlevel: undefined,
  createTime: undefined,
  modifyTime: undefined,
  jsyh: undefined,
  jSYHName: undefined,
  platformTag: undefined,
  categoryIDName: undefined,
  isEditable: undefined,
  sfkfrysy: undefined,
  glYwName: undefined,
  glYwId: undefined,
  userCountInRole: undefined,
  menuCountInRole: undefined,
  clJsyhname: undefined,
  color: undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '编号不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const aGroupTree = ref() // 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AGroupApi.getAGroup(id)
    } finally {
      formLoading.value = false
    }
  }
  await getAGroupTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AGroupVO
    if (formType.value === 'create') {
      await AGroupApi.createAGroup(data)
      message.success(t('common.createSuccess'))
    } else {
      await AGroupApi.updateAGroup(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    name: undefined,
    shortName: undefined,
    code: undefined,
    parentID: undefined,
    fullID: undefined,
    groupType: undefined,
    type: undefined,
    sortIndex: undefined,
    isDeleted: undefined,
    deleteTime: undefined,
    orgLevel: undefined,
    description: undefined,
    location: undefined,
    systemCode: undefined,
    outKey: undefined,
    connName: undefined,
    userSQL: undefined,
    fullName: undefined,
    orgRole: undefined,
    categoryID: undefined,
    categoryName: undefined,
    useCategory: undefined,
    propCategory: undefined,
    isleader: undefined,
    creditCode: undefined,
    deptUnit: undefined,
    deptUnitName: undefined,
    tlevel: undefined,
    createTime: undefined,
    modifyTime: undefined,
    jsyh: undefined,
    jSYHName: undefined,
    platformTag: undefined,
    categoryIDName: undefined,
    isEditable: undefined,
    sfkfrysy: undefined,
    glYwName: undefined,
    glYwId: undefined,
    userCountInRole: undefined,
    menuCountInRole: undefined,
    clJsyhname: undefined,
    color: undefined,
  }
  formRef.value?.resetFields()
}

/** 获得系统角色管理树 */
const getAGroupTree = async () => {
  aGroupTree.value = []
  const data = await AGroupApi.getAGroupList()
  const root: Tree = { id: 0, name: '顶级系统角色管理', children: [] }
  root.children = handleTree(data, 'id', 'parentID')
  aGroupTree.value.push(root)
}
</script>
