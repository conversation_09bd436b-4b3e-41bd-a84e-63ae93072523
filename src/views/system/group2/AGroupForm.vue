<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="150px"
    v-loading="formLoading"
  >
    <el-form-item label="父节点" prop="parentID">
      <el-tree-select
        v-model="formData.parentID"
        :data="aGroupTree"
        :props="defaultProps"
        check-strictly
        default-expand-all
        placeholder="请选择父节点"
      />
    </el-form-item>
    <el-row>
      <el-col :span="12">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称"/>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="编号" prop="code">
          <el-input v-model="formData.code" placeholder="请输入编号"/>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择类型">
            <el-option label="系统角色" value="SystemRole"/>
            <el-option label="组织角色" value="OrgRole"/>
            <el-option label="子系统" value="SubDepartment"/>
            <el-option label="自动更新角色" value="AutoRole"/>
            <el-option label="部门" value="Department"/>
            <el-option label="组织" value="Organization"/>
            <el-option label="动态角色" value="VariableRole"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="平台标识" prop="platformTag">
          <el-input v-model="formData.platformTag" placeholder="请输入平台标识"/>
        </el-form-item>
      </el-col>
    </el-row>


    <el-row>
      <el-col :span="12">
        <el-form-item label="使用分类" prop="useCategory">
          <el-input v-model="formData.useCategory" placeholder="请输入使用分类"/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="属性分类" prop="propCategory">
          <el-input v-model="formData.propCategory" placeholder="请输入属性分类"/>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item label="是否开发人员维护" prop="isEditable">
          <el-select
            v-model="formData.isEditable"
            placeholder="请选择是否需要开发人员维护"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          &nbsp;
          <el-button @click="roleUserFunction">
            维护角色用户<Icon icon="ep:setting"/>
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="描述"   prop="description">
      <el-input v-model="formData.description" type="textarea" rows=4 placeholder="请输入描述"/>
    </el-form-item>

  </el-form>
  <div class="center-button">
    <el-button @click="submitForm" type="primary" :disabled="formLoading">保 存</el-button>
  </div>
</template>
<script setup lang="ts">
import {AGroupApi, AGroupVO} from '@/api/system/group'
import {defaultProps, handleTree} from '@/utils/tree'
import {DICT_TYPE, getBoolDictOptions, getStrDictOptions} from "@/utils/dict";

/** 系统角色管理 表单 */
defineOptions({name: 'AGroupForm'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  name: undefined,
  shortName: undefined,
  code: undefined,
  parentID: undefined,
  fullID: undefined,
  groupType: undefined,
  type: undefined,
  sortIndex: undefined,
  isDeleted: undefined,
  deleteTime: undefined,
  orgLevel: undefined,
  description: undefined,
  location: undefined,
  systemCode: undefined,
  outKey: undefined,
  connName: undefined,
  userSQL: undefined,
  fullName: undefined,
  orgRole: undefined,
  categoryID: undefined,
  categoryName: undefined,
  useCategory: undefined,
  propCategory: undefined,
  isleader: undefined,
  creditCode: undefined,
  deptUnit: undefined,
  deptUnitName: undefined,
  tlevel: undefined,
  createTime: undefined,
  modifyTime: undefined,
  jsyh: undefined,
  jSYHName: undefined,
  platformTag: undefined,
  categoryIDName: undefined,
  isEditable: undefined,
  sfkfrysy: undefined,
  glYwName: undefined,
  glYwId: undefined,
  userCountInRole: undefined,
  menuCountInRole: undefined,
  clJsyhname: undefined,
  color: undefined,
})
const formRules = reactive({
  name: [{required: true, message: '名称不能为空', trigger: 'blur'}],
  code: [{required: true, message: '编号不能为空', trigger: 'blur'}],
})
const formRef = ref() // 表单 Ref
const aGroupTree = ref() // 树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AGroupApi.getAGroup(id)
    } finally {
      formLoading.value = false
    }
  }
  await getAGroupTree()
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AGroupVO
    if (formType.value === 'create') {
      await AGroupApi.createAGroup(data)
      message.success(t('common.createSuccess'))
    } else {
      await AGroupApi.updateAGroup(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const roleUserFunction=()=>{

}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    name: undefined,
    shortName: undefined,
    code: undefined,
    parentID: undefined,
    fullID: undefined,
    groupType: undefined,
    type: undefined,
    sortIndex: undefined,
    isDeleted: undefined,
    deleteTime: undefined,
    orgLevel: undefined,
    description: undefined,
    location: undefined,
    systemCode: undefined,
    outKey: undefined,
    connName: undefined,
    userSQL: undefined,
    fullName: undefined,
    orgRole: undefined,
    categoryID: undefined,
    categoryName: undefined,
    useCategory: undefined,
    propCategory: undefined,
    isleader: undefined,
    creditCode: undefined,
    deptUnit: undefined,
    deptUnitName: undefined,
    tlevel: undefined,
    createTime: undefined,
    modifyTime: undefined,
    jsyh: undefined,
    jSYHName: undefined,
    platformTag: undefined,
    categoryIDName: undefined,
    isEditable: undefined,
    sfkfrysy: undefined,
    glYwName: undefined,
    glYwId: undefined,
    userCountInRole: undefined,
    menuCountInRole: undefined,
    clJsyhname: undefined,
    color: undefined,
  }
  formRef.value?.resetFields()
}

/** 获得系统角色管理树 */
const getAGroupTree = async () => {
  aGroupTree.value = []
  const data = await AGroupApi.getAGroupList({groupType:"Org"})
  const root: Tree = {id: 0, name: '顶级系统角色管理', children: []}
  root.children = handleTree(data, 'id', 'parentID')
  aGroupTree.value.push(root)
}
</script>

