<template>
  <div>
    <!-- <div class="text-center">
      <UserAvatar :img="'data:image/jpg;base64,' + imageData" />
    </div> -->

    <div class="text-center" v-if="image" style="width: 200px; height: 200px">
      <img :src="'data:image/jpg;base64,' + imageData" alt=""
        style="display: block;width: 200px; height: 200px;" />
    </div>
    <ul class="list-group list-group-striped">
      <li class="list-group-item">
        <Icon class="mr-5px" icon="ep:user" />
        {{ t('profile.user.username') }}
        <div class="pull-right">{{ userInfo?.username }}</div>
      </li>
      <li class="list-group-item">
        <Icon class="mr-5px" icon="ep:phone" />
        {{ t('profile.user.mobile') }}
        <div class="pull-right">{{ userInfo?.mobile }}</div>
      </li>
      <li class="list-group-item">
        <Icon class="mr-5px" icon="fontisto:email" />
        {{ t('profile.user.email') }}
        <div class="pull-right">{{ userInfo?.email }}</div>
      </li>
      <li class="list-group-item">
        <Icon class="mr-5px" icon="carbon:tree-view-alt" />
        {{ t('profile.user.dept') }}
        <div v-if="userInfo?.dept" class="pull-right">{{ userInfo?.dept.name }}</div>
      </li>
      <li class="list-group-item">
        <Icon class="mr-5px" icon="ep:suitcase" />
        {{ t('profile.user.posts') }}
        <div v-if="userInfo?.posts" class="pull-right">
          {{ userInfo?.posts.map((post) => post.name).join(',') }}
        </div>
      </li>
      <li class="list-group-item">
        <Icon class="mr-5px" icon="icon-park-outline:peoples" />
        {{ t('profile.user.roles') }}
        <div v-if="userInfo?.roles" class="pull-right">
          {{ userInfo?.roles.map((role) => role.name).join(',') }}
        </div>
      </li>
      <li class="list-group-item">
        <Icon class="mr-5px" icon="ep:calendar" />
        {{ t('profile.user.createTime') }}
        <div class="pull-right">{{ formatDate(userInfo.createTime) }}</div>
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'

import { getUserProfile, ProfileVO } from '@/api/system/user/profile'

import UserAvatar from './UserAvatar.vue';
defineOptions({ name: 'ProfileUser' })


const image = ref(false);
const { t } = useI18n()
const userInfo = ref({} as ProfileVO)
const getUserInfo = async () => {
  const users = await getUserProfile()
  userInfo.value = users
  if (users) {
    image.value = true;
  }
}
const imageData = computed(() => {
  if (userInfo.value.avatar) {
    userInfo.value.avatar = userInfo.value.avatar.replace(/^0x/, '');
    // 将十六进制字符串转换为字节数组
    let bytes = [];
    for (let i = 0; i < userInfo.value.avatar.length; i += 2) {
      bytes.push(parseInt(userInfo.value.avatar.substr(i, 2), 16));
    }
    // 将字节数组转换为Base64字符串
    let binary = String.fromCharCode.apply(null, bytes);
    return btoa(binary);
  }
});
onMounted(async () => {
  await getUserInfo()
})

</script>

<style scoped>
.text-center {
  position: relative;
  height: 120px;
  text-align: center;
}

.list-group-striped>.list-group-item {
  padding-right: 0;
  padding-left: 0;
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

.list-group {
  padding-left: 0;
  list-style: none;
}

.list-group-item {
  padding: 11px 0;
  margin-bottom: -1px;
  font-size: 13px;
  border-top: 1px solid #e7eaec;
  border-bottom: 1px solid #e7eaec;
}

.pull-right {
  float: right !important;
}
</style>
