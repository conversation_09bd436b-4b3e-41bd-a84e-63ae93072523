<template>
  <Dialog v-model="dialogVisible" title="任务详细" width="70%">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="任务名称" min-width="60">
        {{ detailData?.taskName }}
      </el-descriptions-item>
      <el-descriptions-item label="任务编码">
        {{ detailData?.taskCode }}
      </el-descriptions-item>
      <el-descriptions-item label="任务分组">
        {{ detailData?.taskGroup }}
      </el-descriptions-item>
      <el-descriptions-item label="任务状态">
        {{ detailData?.stateStr }}
      </el-descriptions-item>

      <el-descriptions-item label="初始化状态">
        {{ detailData?.init == 1 ? '初始化成功' : '未初始化' }}
      </el-descriptions-item>

      <el-descriptions-item label="ApiUrl">
        <DynamicTable :ModelType="'ApiUrl'" :tableList="detailData?.taskApiUrlList" :show="false" />
      </el-descriptions-item>
      <el-descriptions-item label="ApiSQL">
        <DynamicTable :ModelType="'ApiSQL'" :tableList="detailData?.taskApiSQLList" :show="false" />
      </el-descriptions-item>

      <el-descriptions-item label="Cron 表达式">
        {{ detailData?.cron }}
      </el-descriptions-item>
      <el-descriptions-item label="创建人">
        {{ detailData?.createUser }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{
          formatDate(new Date(detailData?.createTime == undefined ? '' : detailData?.createTime))
        }}
      </el-descriptions-item>
      <el-descriptions-item label="修改人">
        {{ detailData?.modifyUser }}
      </el-descriptions-item>
      <el-descriptions-item label="修改时间">
        {{
          formatDate(new Date(detailData?.modifyTime == undefined ? '' : detailData?.modifyTime))
        }}
      </el-descriptions-item>

      <el-descriptions-item label="下次执行时间" min-width="120">
        {{ detailData?.init == 1 ? formatDate(nextTimes) : '未初始化' }}
      </el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import * as JobApi from '@/api/infra/job'
import DynamicTable from './components/DynamicTable.vue'

defineOptions({ name: 'InfraJobDetail' })
interface TableType {
  id: string
  newTimerTaskID: string
  headerKey: string
  sortIndex: number
  headerValue: string
  requestUrl: string
  stepCode: string
  stepName: string
  requestType: string
  // sql
  execSQL: string
  connName: string
  remark: string
}

interface formType {
  id: string
  taskName: string
  cron: string
  isDeleted: string
  taskState: number
  taskGroup: string
  remark: string
  lastRunTime: string
  lastRunEndTime: string
  taskCode: string
  init: number
  stateStr: string
  createUser: string
  createUserID: string
  createTime: string
  modifyUser: string
  modifyUserID: string
  modifyTime: string
  taskApiUrlList: Array<TableType>
  taskApiSQLList: Array<TableType>
}

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref<formType>() // 详情数据
const nextTimes = ref() // 下一轮执行时间的数组

/** 打开弹窗 */
const open = async (id: string) => {
  dialogVisible.value = true
  // 查看，设置数据
  if (id) {
    detailLoading.value = true
    try {
      detailData.value = await JobApi.getTask(id)
      console.log(detailData.value?.id)
      // 获取下一次执行时间
      nextTimes.value = await JobApi.getTaskNextTimes(
        id,
        detailData.value?.taskGroup == undefined ? '' : detailData.value?.taskGroup
      )
    } finally {
      detailLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
