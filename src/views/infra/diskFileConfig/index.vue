<template>

  <!-- 搜索 -->
  <ContentWrap>
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" @submit.prevent>
      <el-form-item label="磁盘路径" prop="folderPath">
        <el-input v-model="queryParams.folderPath" placeholder="磁盘路径" clearable @keyup.enter.stop="handleQuery" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['infra:file-config:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <!-- <el-table-column label="编号" align="center" prop="id" width="100px" /> -->
      <el-table-column label="磁盘根路径" align="center" prop="rootFolderPath"/>

      <el-table-column label="总空间" align="center" prop="totalSpace">
        <template #default="scope">
          <div>{{scope.row.totalSpace == undefined?'--':scope.row.totalSpace+' G'}}</div>
        </template>
      </el-table-column>

      <el-table-column label="可用空间" align="center" prop="freeSpace">
        <template #default="scope">
          <div>{{scope.row.freeSpace == undefined?'--':scope.row.freeSpace+' G'}}</div>
        </template>
      </el-table-column>

      <el-table-column label="是否生效" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.isValid == 1">主配置</el-tag>
          <el-tag v-if="scope.row.isValid != 1" type="info">未配置</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center" width="240px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)"
            v-hasPermi="['infra:file-config:update']">
            编辑
          </el-button>
          <el-button link type="primary" :disabled="scope.row.isValid == 1" @click="handleMaster(scope.row.id)"
            v-hasPermi="['infra:file-config:update']">
            主配置
          </el-button>
          <!-- <el-button link type="primary" @click="handleTest(scope.row.id)"> 测试 </el-button> -->
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['infra:file-config:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <FileConfigForm ref="formRef" @success="getList" />
</template>
<script lang="ts" setup>
import * as FileConfigApi from '@/api/infra/fileConfig'
import FileConfigForm from './FileConfigForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

defineOptions({ name: 'InfraFileConfig' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  storage: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await FileConfigApi.getDiskFileConfigPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await FileConfigApi.deleteDiskFileConfig(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 主配置按钮操作 */
const handleMaster = async (id) => {
  try {
    await message.confirm('是否确认修改配置编号为"' + id + '"的数据项为主配置?')
    await FileConfigApi.updateDiskFileConfigMaster(id)
    message.success(t('common.updateSuccess'))
    await getList()
  } catch { }
}

/** 测试按钮操作 */
const handleTest = async (id) => {
  try {
    const response = await FileConfigApi.testFileConfig(id)
    message.alert('测试通过，上传文件成功！访问地址：' + response)
  } catch { }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
