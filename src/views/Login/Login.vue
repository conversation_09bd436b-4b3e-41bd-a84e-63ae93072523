<template>
  <div class="content">
    <!--左上角标题LOGO-->
    <div id="slideshow">
      <img v-for="(item, index) in imgs" :key="index" :src="proxys.$getFullUrl('/BasicApplication/DownloadFile/IndexAnonymous?FileID=' + item.img)
          " />
    </div>
    <div class="custom-login-content">
      <img src="@/assets/imgs/mlogo.png" />
    </div>

    <!--右边登录框-->
    <div class="login_box">
      <div class="flex">
        <div class="login">
          <div class="tabs">
            <div class="zh">登录</div>
            <div class="eng">LOGIN</div>
            <div class="type" v-show="showLogin" @click="isShowLogin">密码登录</div>
            <div class="type" v-show="!showLogin" @click="isShowLogin">扫码登录</div>
            <img class="tp" v-show="showLogin" @click="isShowLogin"
                 src="@/assets/imgs/scanqrcode.png"/>
            <img class="tp" v-show="!showLogin" @click="isShowLogin"
                 src="@/assets/imgs/username.png"/>
          </div>
          <!--登录框-->
          <div class="form" v-loading="loginLoading" element-loading-text="正在登录，请稍后...">
            <LoginForm v-if="showLogin" @update-loading="updateLoading" class="loginForm"/>
            <div ref="wxReg" id="wx_reg" v-if="!showLogin" style="margin-top: 30px"></div>
          </div>

          <!--下载链接-->
          <div class="download">
            <div class="item">
              <img style="width: 14px; height: 14px" src="@/assets/svgs/anzhuo.svg"/>
              <span>安卓下载</span>
              <div class="simg">
                <img class="img" src="@/assets/imgs/android.png"/>
              </div>
            </div>

            <div class="item">
              <img style="width: 14px; height: 14px" src="@/assets/svgs/ios.svg"/>
              <span>IOS下载</span>
              <div class="simg">
                <img class="img" src="@/assets/imgs/ios.png"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ElMessage} from 'element-plus'
// import { useDesign } from '@/hooks/web/useDesign'
// import { useAppStore } from '@/store/modules/app'
import {LoginForm} from './components'
import {getCurrentInstance} from 'vue'
import * as AuthApi from '@/api/login'
import qrcode from 'qrcode'
import {WarningFilled} from '@element-plus/icons-vue'

defineOptions({name: 'Login'})
import * as LoginApi from '@/api/login'
import * as authUtil from '@/utils/auth'
import {usePermissionStore} from '@/store/modules/permission'
import {useRoute} from 'vue-router'

const permissionStore = usePermissionStore()
const redirect = ref<string>('')
const {push} = useRouter()
const route = useRoute()
const wxReg = ref(null)
// const appStore = useAppStore()
// const { getPrefixCls } = useDesign()
// const prefixCls = getPrefixCls('login')

const isShowRefresh = ref<boolean>(false)
const QRLoading = ref<boolean>(false)
const loginLoading = ref()
const QRCodeKey = ref('')
let showLogin = ref(true)

const isShowLogin = async () => {
  if (showLogin.value) {
    create_qr() //创建二维码
    showLogin.value = false
  } else {
    showLogin.value = true
  }
}

//创建二维码
const create_qr = async () => {
  QRLoading.value = true
  try {
    const qr = await AuthApi.get_uuid()
    if (qr) {
      await window.WwLogin({
        id: 'wx_reg',
        appid: 'wl3380ce2cef',
        agentid: '1000241',
        redirect_uri: 'https://mobile.khidi.com/api/qrlogin',
        state: qr,
        href: ''
      })
    }
  } finally {
    QRLoading.value = false
  }
}

//检查登录状态
const check_login_status = async () => {
  const uuId = route.query.uuId as string
  if (uuId) {
    const uuid = uuId
    removeUrlParam('uuId') //清除url参数
    loginLoading.value = true

    await getTenantId() //获取租户信息

    const ret = await AuthApi.check_login_status({uuId: uuid})
    if (ret?.userId && ret?.accessToken) {
      loginLoading.value = false
      authUtil.setToken(ret) //保存token

      // 获取自动退出登录超时时间并启动功能
      const autoLogoutTimeout = await LoginApi.getAutoLogoutTimeout()
      console.log('检查登录状态 - 无操作退出登录时间======================>', autoLogoutTimeout)
      if (autoLogoutTimeout && autoLogoutTimeout > 0) {
        authUtil.autoLogoutManager.start(autoLogoutTimeout)
      }

      window.location.href = '/Portal/workCenter/workIndex' //跳转到首页
    } else {
      loginLoading.value = false
      ElMessage('扫码登录失败，请重试！')
    }
  }
}

//清除url里某个参数和值
function removeUrlParam(param) {
  // 获取当前 URL
  const currentUrl = window.location.href

  // 创建一个 URL 对象
  const url = new URL(currentUrl)

  // 使用 URLSearchParams 来处理查询参数
  const params = new URLSearchParams(url.search)

  // 删除指定的参数
  params.delete(param)

  // 更新 URL 对象的查询部分
  url.search = params.toString()

  // 更新浏览器的地址栏
  window.history.replaceState({}, '', url)
}

//获取租户ID
const getTenantId = async () => {
  if (loginData.tenantEnable) {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}

const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    tenantName: '中国电建昆明院',
    username: '',
    password: '',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})

// 获取二维码
const getQRcCode = async () => {
  qrCodeImage.value = undefined
  QRLoading.value = true
  let ret

  try {
    ret = await AuthApi.getQRcCode()
    console.log('ret', ret)
    if (ret) {
      //初始化显示二维码
      await window.WwLogin({
        id: 'wx_reg',
        appid: 'wl3380ce2cef',
        agentid: '1000241',
        redirect_uri: 'https://mobile.khidi.com/api/qrlogin',
        state: ret,
        href: ''
      })
      // QRCodeKey.value = ret.key ? ret.key : ''
    }
  } finally {
    QRLoading.value = false
  }
}

// 刷新二维码
const refreshQRCode = () => {
  getQRcCode()
  isShowRefresh.value = false
}

// 轮询查询状态
const getQRStatus = async (uuId: string, key: string) => {
  const ret = await AuthApi.getQRcCodeState({qrcodeKey: key, uuId: uuId})
  if (ret.status == 'QR_SUCCESS') {
    loginLoading.value = true

    //获取设置租户id
    getTenantId()
    let auToken = ret.token

    //保存token
    authUtil.setToken(auToken)

    // 获取自动退出登录超时时间并启动功能
    const autoLogoutTimeout = await LoginApi.getAutoLogoutTimeout()
    console.log('二维码登录 - 无操作退出登录时间======================>', autoLogoutTimeout)
    if (autoLogoutTimeout && autoLogoutTimeout > 0) {
      authUtil.autoLogoutManager.start(autoLogoutTimeout)
    }

    if (!redirect.value) {
      redirect.value = '/'
    }

    window.location.href = '/Portal/workCenter/workIndex'

    // setTimeout(() => {
    //   loginLoading.value = false
    //   if (redirect.value.indexOf('sso') !== -1) {
    //     window.location.href = window.location.href.replace('/login?redirect=', '')
    //   } else {
    //     push({ path: redirect.value || permissionStore.addRouters[0].path })
    //   }
    // }, 2000)
  } else {
    isShowRefresh.value = true //二维码过期处理
  }
  console.log(ret)
}

// 生成二维码
const qrCodeImage = ref()
const QRCodeStyle = {
  version: '', // 二维码版本。如果未指定，将计算更合适的值。
  errorCorrectionLevel: 'M', // 纠错级别。low, medium, quartile, high , L, M, Q, H
  maskPattern: 1, // 0、1、2、3、4、5、6、7
  margin: 1, // 边距
  scale: 4, // 每一个黑点的宽度
  width: 4, // 二维码宽
  'color.dark': '#000', // 二维码颜色
  'color.light': '#fff' // 背景色
}

const generateQRCode = (url: string) => {
  qrcode
    .toDataURL(url, QRCodeStyle)
    .then((url) => {
      qrCodeImage.value = url
    })
    .catch(() => {
      useMessage().error('二维码处理异常')
    })
}

const loading = ref(false)
const updateLoading = (flag: boolean) => {
  loading.value = flag
}

//切换登录背景图片
function change_background() {
  /**背景切换*/
  var images = document.querySelectorAll('#slideshow img'),
    imgLength = images.length,
    i = 1

  if (images && images.length > 0) {
    function setFirst() {
      images[0].className = 'fx'
    }

    window.setTimeout(setFirst, 1)
    if (imgLength > 1) {
      window.setInterval(kenBurns, 6200)
    }


    function kenBurns() {
      images[i].className = 'fx'
      i++

      if (i === imgLength) {
        i = 0
        images[imgLength - 2].className = ''
      }
      if (i === 1) {
        images[imgLength - 1].className = ''
      }
      if (i > 1) {
        images[i - 2].className = ''
      }
    }
  }
}

//登录背景图片处理
const proxys = ref()
const imgs = ref<any[]>() //背景图片集合

const background_img = async () => {
  //获取文件全局路径
  const {proxy} = getCurrentInstance()
  proxys.value = proxy
  //获取图片列表
  const list = await AuthApi.getLoginImage()
  if (list) {
    imgs.value = list
  }
  //切换背景图片
  setTimeout(() => {
    change_background()
  }, 100)
}

const getuuId = () => {
  const uuId = route.query.uuId as string
  if (uuId) {
    getQRStatus(uuId, QRCodeKey.value)
  }
}

onMounted(() => {
  background_img() //登录背景图片处理
  // getuuId()

  check_login_status() //检查登录状态
})
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:deep(.el-input__inner) {
  color: #000;
}

#wx_reg {
  :deep(iframe) {
    transform: scale(0.7);
  }
}

.content {
  /**logo */
  .custom-login-content {
    img {
      position: absolute;
      top: 60px;
      left: 180px;
      color: #fff;
      font-size: 28px;
      font-weight: 500;
      width: 600px;
    }
  }

  /**背景轮播*/
  #slideshow {
    position: absolute;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: #000;

    img {
      position: absolute;
      width: 100%;
      height: 100%;

      -webkit-transition-property: opacity, -webkit-transform;
      transition-property: opacity, -webkit-transform;
      transition-property: transform, opacity;
      transition-property: transform, opacity, -webkit-transform;
      -webkit-transition-duration: 9s, 4s;
      transition-duration: 9s, 4s;
      -webkit-transform-origin: bottom left;
      transform-origin: bottom left;
      opacity: 0;

      &:nth-child(2n + 1) {
        -webkit-transform-origin: top right;
        transform-origin: top right;
      }

      &:nth-child(3n + 1) {
        -webkit-transform-origin: top left;
        transform-origin: top left;
      }

      &:nth-child(4n + 1) {
        -webkit-transform-origin: bottom right;
        transform-origin: bottom right;
      }

      &:firsh-child {
        opacity: 1;
        z-index: -1;
      }

      &.fx {
        opacity: 1;
        transform-origin: center center;
        -webkit-transform: scale(1);
        transform: scale(1);
      }
    }
  }

  /**登录FORM */
  .login_box {
    width: 100vw;
    height: 100vh;
    display: flex;
    position: fixed;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;

    .flex {
      display: flex;
      width: 100%;
      height: 100%;
      align-items: center;
      justify-content: center;
    }
  }

  .login {
    background: #fff;
    margin-left: auto;
    margin-right: 300px;
    border-radius: 4px;
    width: 360px;
    height: 320px;

    @media screen and (max-width: 1544px) {
      margin-right: 150px;
    }

    @media screen and (max-width: 1344px) {
      margin-right: 80px;
    }

    @media screen and (max-width: 1024px) {
      margin-right: 50px;
    }

    .tabs {
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 5px;

      .zh {
        font-size: 18px;
        color: #333;
        margin-left: 25px;
      }

      .eng {
        font-size: 14px;
        color: #ccc;
        margin-left: 4px;
      }

      .type {
        margin-left: auto;
        width: 72px;
        height: 26px;
        cursor: pointer;
        line-height: 26px;
        background: #ffecd3;
        color: #feb14c;
        font-size: 13px;
        text-align: center;
        position: relative;
        user-select: none;

        &::after {
          content: '';
          position: absolute;
          top: 5px;
          right: -8px;

          width: 0;
          height: 0;

          border-color: transparent;
          border-style: solid;
          border-width: 8px 0 8px 8px;
          border-left-color: #ffecd3;
        }
      }

      .tp {
        cursor: pointer;
        user-select: none;
      }
    }

    .form {
      width: 100%;
      height: calc(100% - 120px);
      padding: 0 30px;
      margin-top: 10px;

      display: flex;
      align-items: center;
      justify-content: center;

      .smdl {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .refresh {
          position: absolute;
          text-align: center;
          margin-bottom: 45px;
          opacity: 0.96;
          background-color: #fff;
          width: 140px;
          height: 140px;

          .el-button {
            background-color: #fff;
            width: 50px;
            margin-top: 10%;
            font-size: #000;
          }
        }

        img,
        .imgLoading {
          border: 2px solid #ccc;
          width: 140px;
          height: 140px;
        }

        .text {
          font-size: 18px;
          color: #000;
          margin-top: 20px;
        }
      }
    }

    .download {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #555;

      .item {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;

        &:hover {
          color: #409eff;

          .simg {
            display: block;
          }
        }

        &:first-child {
          border-right: 1px solid #999;
          padding-right: 10px;
          margin-right: 10px;
        }

        img {
          margin-right: 5px;
        }

        .simg {
          width: 100px;
          height: 100px;
          border: 2px solid #ccc;
          padding: 0;
          margin: 0;
          position: absolute;
          top: 25px;
          left: -15px;
          display: none;

          &::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 40px;
            border-color: transparent;
            border-style: solid;
            border-width: 10px 10px 10px 10px;
            border-bottom-color: #ccc;
          }

          img {
            padding: 0;
            margin: 0;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
</style>
