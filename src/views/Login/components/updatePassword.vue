<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-menu
      :default-active="active"
      class="el-menu-demo"
      mode="horizontal"
      text-color="#485365"
      active-text-color="#0975ff"
      @select="handleSelect"
    >
      <el-menu-item index="1">旧密码修改</el-menu-item>
      <el-menu-item index="2">电建通验证码</el-menu-item>
      <!-- <el-menu-item index="3">手机验证码</el-menu-item> -->
    </el-menu>
    <el-form
      style="margin-top: 30px"
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="用户名" prop="workNo">
        <el-input
          @change="init(formData.workNo)"
          v-model="formData.workNo"
          placeholder="请输入用户名"
        />
      </el-form-item>
      <div v-if="active == '1'">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            :prefix-icon="iconLock"
            type="password"
            show-password
            v-model="formData.oldPassword"
            placeholder="请输入旧密码"/>
        </el-form-item>
      </div>

      <div v-if="active == '2'">
        <el-form-item label="电建通账号" prop="appWorkNo">
          <el-input disabled v-model="formData.appWorkNo" placeholder="请输入电建通账号">
            <template #append>
              <el-button :disabled="buttonDisabled" @click="getCode()" style="color: #3399ff">{{
                  buttonText
                }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="appVerCode">
          <el-input v-model="formData.appVerCode" placeholder="请输入验证码"/>
        </el-form-item>
      </div>
      <div v-if="active == '3'">
        <el-form-item label="手机号" prop="mobilePhone">
          <el-input disabled v-model="formData.mobilePhone" placeholder="请输入手机号">
            <template #append>
              <el-button :disabled="buttonDisabled" @click="getCode()" style="color: #3399ff">{{
                  buttonText
                }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="phoneCode">
          <el-input v-model="formData.phoneCode" placeholder="请输入验证码"/>
        </el-form-item>
      </div>

      <el-form-item label="密码" prop="newPassword">
        <el-input
          :prefix-icon="iconLock"
          type="password"
          show-password
          @change="checkPasswordStrength()"
          v-model="formData.newPassword"
          placeholder="请输入密码"
        />
      </el-form-item>
      <el-form-item label="密码强度">
        <el-rate v-model="passwordScore" allow-half disabled/>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          :prefix-icon="iconLock"
          type="password"
          show-password
          v-model="formData.confirmPassword"
          placeholder="请输入确认密码"/>
      </el-form-item>
      <el-form-item style="word-break: break-all" label="密码规则" prop="value">
        <p style="text-align: left">
          <span style="color: #f56c6c">*</span>最短6位，最长20位<br/>
          <span style="color: #f56c6c">*</span>必须设置密码强度为“中”以上才可以保存<br/>
          <span style="color: #f56c6c">*</span>建议数字、字母、特殊字符组合
        </p>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import * as AuthApi from '@/api/login'
import * as PortalApi from '@/api/system/portal/index'
import {useIcon} from "@/hooks/web/useIcon";

const iconLock = useIcon({icon: 'ep:lock'})
const buttonDisabled = ref(false)
const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('修改密码') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const buttonText = ref('获取验证码')
const passwordScore = ref(0)
const formData = ref({
  workNo: '', //工号用户名
  oldPassword: '', //旧密码
  newPassword: '', //新密码
  confirmPassword: '', //确认密码
  codeId: '',
  appWorkNo: '', //电建通账号
  appVerCode: '', //电建通验证码
  mobilePhone: '', //手机号
  phoneCode: '', //手机验证码
  active: '' //修改方式
})
const codeId = ref('')
const active = ref('1')
const formRef = ref() // 表单 Ref

const handleSelect = (key: string, keyPath: string[]) => {
  active.value = key
}

const startTimer = () => {
  let timer = 120
  const intervalId = setInterval(function () {
    buttonText.value = timer + '秒后可重新发送'
    if (--timer < 0) {
      clearInterval(intervalId)
      buttonDisabled.value = false
      buttonText.value = '获取验证码'
    }
  }, 1000)
}
const getCode = async () => {
  buttonDisabled.value = true
  const parm = {
    appWorkNo: 'qizh_kmy_test',
    workNo: formData.value.workNo,
    sendType: 0
  }

  if (active.value == '2') {
    //电建通验证码
    try {
      parm.sendType = 0
      const ret = await PortalApi.GetVerificationCode(parm)
      if (ret) {
        useMessage().success('验证码发送成功')
        startTimer()
        codeId.value = ret
      } else {
        buttonDisabled.value = false
        useMessage().error('验证码发送失败，请稍后重新尝试')
      }
    } catch (error) {
      buttonDisabled.value = false
    }
  } else if (active.value == '3') {
    //短信验证码
    parm.sendType = 1
  }
}

/** 打开弹窗 */
const open = async (workNo: string) => {
  formData.value.workNo = workNo
  await init(workNo)
  dialogVisible.value = true
}
const init = async (workNo) => {
  let data = await AuthApi.getUserInfo(workNo)
  if (data) {
    formData.value.appWorkNo = data.DJTUserName
    formData.value.mobilePhone = data.KMYMobile
    formData.value.workNo = data.KMYWorkNo
  } else {
    formData.value.appWorkNo = ''
    formData.value.mobilePhone = ''
  }
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  if (passwordScore.value < 3) {
    message.warning('您设置的密码过于简单！请重新设置')
    return
  }
  formData.value.active = active.value
  formData.value.codeId = codeId.value
  // 提交请求
  formLoading.value = true

  try {
    //校验验证码
    const data = {...formData.value}
    data.oldPassword = encodePassword(data.oldPassword)
    data.newPassword = encodePassword(data.newPassword)
    data.confirmPassword = encodePassword(data.confirmPassword)
    const ret = await AuthApi.updatePassword(data)
    if (ret) {
      message.success(t('common.updateSuccess'))
      dialogVisible.value = false
      // 发送操作成功的事件
      emit('success')
    } else {
      message.error('修改失败')
      dialogVisible.value = false
    }
  } finally {
    formLoading.value = false
  }
}
const encodePassword = (value) => {
  return btoa(unescape(encodeURIComponent(value)))
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {}
  formRef.value?.resetFields()
}

// 检查密码强度
const checkPasswordStrength = () => {
  const password = formData.value.newPassword
  let score = 0

  // 检查密码是否包含数字
  if (/\d/.test(password)) score += 1
  // 检查密码是否包含小写字母
  if (/[a-z]/.test(password)) score += 1
  // 检查密码是否包含大写字母
  if (/[A-Z]/.test(password)) score += 1
  // 检查密码是否包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) score += 1

  // 设置强度等级
  passwordScore.value = Math.min(score + 1, 5) // 最高强度为 5 星
}

// 验证密码强度
const validatePasswordStrength = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    // 假设密码至少需要 8 个字符
    if (value.length < 6) {
      callback(new Error('密码长度至少为 6 个字符'))
    } else if (value.length > 20) {
      callback(new Error('密码长度最大为 20 个字符'))
    } else {
      callback()
    }
  }
}

// 验证确认密码
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formData.value.newPassword) {
    callback(new Error('两次输入的密码不一致!'))
  } else {
    callback()
  }
}

const formRules = reactive({
  workNo: [{required: true, message: '用户名不能为空', trigger: 'blur'}],
  oldPassword: [{required: true, message: '旧密码不能为空', trigger: 'blur'}],
  newPassword: [
    {required: true, message: '新密码不能为空', trigger: 'blur'},
    {validator: validatePasswordStrength, trigger: 'blur'}
  ],
  confirmPassword: [
    {required: true, message: '确认密码不能为空', trigger: 'blur'},
    {validator: validateConfirmPassword, trigger: 'blur'}
  ],
  appWorkNo: [{required: true, message: '电建通账号不能为空', trigger: 'blur'}],
  appVerCode: [{required: true, message: '电建通验证码不能为空', trigger: 'blur'}],
  mobilePhone: [{required: true, message: '手机号不能为空', trigger: 'blur'}],
  phoneCode: [{required: true, message: '手机验证码不能为空', trigger: 'blur'}]
})
</script>
