<template>
  <!--  <div
    :class="prefixCls"
    class="relative h-[100%] lt-md:px-10px lt-sm:px-10px lt-xl:px-10px lt-xl:px-10px"
  >
    <div class="relative mx-auto h-full flex">
      <div
        :class="`${prefixCls}__left flex-1 bg-gray-500 bg-opacity-20 relative p-30px lt-xl:hidden`"
      >
        &lt;!&ndash; 左上角的 logo + 系统标题 &ndash;&gt;
        <div class="relative flex items-center text-white">
          <img alt="" class="mr-10px h-48px w-48px" src="@/assets/imgs/logo.png" />
        </div>
      </div>
      <div style="background-color: rgba(255,255,255,0)" class="relative flex-1 p-30px dark:bg-[var(&#45;&#45;login-bg-color)] lt-sm:p-10px">
&lt;!&ndash;         右上角的主题、语言选择
        <div
          class="flex items-center justify-between text-white at-2xl:justify-end at-xl:justify-end"
        >
          <div class="flex items-center at-2xl:hidden at-xl:hidden">
            <img alt="" class="mr-10px h-48px w-48px" src="@/assets/imgs/logo.png" />
            <span class="text-20px font-bold">{{ underlineToHump(appStore.getTitle) }}</span>
          </div>
          <div class="flex items-center justify-end space-x-10px">
            <ThemeSwitch />
            <LocaleDropdown class="dark:text-white lt-xl:text-white" />
          </div>
        </div>&ndash;&gt;
        &lt;!&ndash; 右边的登录界面 &ndash;&gt;
        <Transition style="height: 500px; width: 500px; background-color: #ffffff; color: #ffffff;margin-top: calc(50% - 250px)" appear enter-active-class="animate__animated animate__bounceInRight">
          <div
            class="m-auto h-full w-[100%] flex items-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px"
          >
            &lt;!&ndash; 账号登录 &ndash;&gt;
            <LoginForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />
            &lt;!&ndash; 手机登录 &ndash;&gt;
&lt;!&ndash;            <MobileForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />&ndash;&gt;
            &lt;!&ndash; 二维码登录 &ndash;&gt;
            <QrCodeForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />
            &lt;!&ndash; 注册 &ndash;&gt;
            <RegisterForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />
            &lt;!&ndash; 三方登录 &ndash;&gt;
&lt;!&ndash;            <SSOLoginVue class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />&ndash;&gt;
          </div>
        </Transition>
      </div>
    </div>
  </div>
  <div v-html="html1"></div>-->

  <div class="app">
    <div class="content" v-loading="loading" element-loading-text="系统登录中...">
      <!--   左上角标题LOGO   -->
      <div :class="`bg-opacity-20 relative p-[30px] lt-xl:hidden`">
        <img class="h-[48px]" src="@/assets/svgs/logo.png" alt="logo" />
      </div>

      <!--   右边登录框   -->
      <div class="login">
        <!--   登录框标题   -->
        <!-- <div class="loginTitle">
          <h5 style="font-size: 48px">您好！</h5>
        </div> -->

        <div class="tab-username"><span class="mfont">登录</span> <span>LOGIN</span></div>

        <div class="loginType">
          <span v-show="showLogin" style="border: #e37341 1px; color: #e37341" @click="isShowLogin">扫码登录<img style="height: 40px;width: 40px; border-radius: 10px" src="@/assets/svgs/loginType2.svg" /></span>
          <span v-show="!showLogin" style="border: #e37341 1px; color: #e37341" @click="isShowLogin">账号登录<img style="height: 40px;width: 40px; border-radius: 10px" src="@/assets/svgs/loginType1.svg" /></span>
        </div>

        <!--   登录框   -->
        <div style="text-align: center">
          <LoginForm @update-loading="updateLoading" v-show="showLogin" class="loginForm" />
          <!--   扫码登录      -->
          <img v-show="!showLogin" style="height: 316px; width: 316px" src="@/assets/imgs/erweima.png" alt="" />
        </div>

        <!--   下载链接   -->
        <div class="download">
          <span><img style="width: 14px; height: 14px" src="@/assets/svgs/anzhuo.svg" />安卓下载</span>
          <span style="margin-left: 20px"><img style="width: 14px; height: 14px" src="@/assets/svgs/ios.svg" />IOS下载</span>
        </div>
      </div>

      <!--   左中下欢迎语   -->
      <!-- <div class="footer">
        <span>Welcome</span>
      </div> -->
    </div>
  </div>


</template>

<script lang="ts" setup>
import { underlineToHump } from '@/utils'


import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { ThemeSwitch } from '@/layout/components/ThemeSwitch'
import { LocaleDropdown } from '@/layout/components/LocaleDropdown'

import { LoginForm, MobileForm, QrCodeForm, RegisterForm, SSOLoginVue } from './components'
import { ref } from 'vue';
defineOptions({ name: 'Mocklogin' })

const { t } = useI18n()
const appStore = useAppStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')

let showLogin = ref(true)
const isShowLogin = () => {
  showLogin.value = !showLogin.value
}
const loading = ref(false);
const updateLoading = (flag: boolean) => {
  loading.value = flag;
}
/*onMounted(()=>{
  console.log($)
})*/
const html1 = "";
</script>

<style lang="scss" scoped>
.app {
  height: 100vh;
  width: 100%;
  background-color: #0b0c46;
  display: flex;
  background-image: url('@/assets/svgs/bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

}

:deep(.el-input__inner) {
  color: #000;
}

.content {
  margin-top: 2%;
  margin-left: 2%;
  height: 92%;
  width: 96%;
  padding: 0 20px;
  position: relative;
}

.login {
  // margin-left: 10%;
  // height: 68%;
  width: 360px;
  padding: 0 20px;
  background-color: #fff;
  border-radius: 10px;
  top: 230px;
  left: 65%;
  position: absolute;
  color: #333;
  padding-bottom: 20px;
}

.loginTitle {
  margin-bottom: 20px;
  height: 10%;
  border: 1px solid blue;
  padding: 20px;
  border-radius: 20px;
  background-color: #fff;
}

.tab-username {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;

  .mfont {
    font-size: 18px;
    color: #333;
  }

  span {
    color: #ccc;
    font-size: 14px;
  }
}

.loginType {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 0px;
  position: absolute;
  right: 0px;
  top: 0px;
}

.loginForm {
  margin: auto;
  height: auto;
  padding: 20px;
  border-radius: 20px;
  background-color: #fff;
  padding-bottom: 0;
}

.download {
  height: 4%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #6b6969;
}

.download :hover {
  cursor: pointer;
  text-decoration: underline;
  color: #0b0c46;
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 14%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 78px;
  letter-spacing: 12px;
  color: #fff;
}

$prefix-cls: #{$namespace}-login;

.#{$prefix-cls} {
  overflow: auto;

  &__left {
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      /* background-image: url('@/assets/svgs/login-bg.svg');*/
      background-position: center;
      background-repeat: no-repeat;
      content: '';
    }
  }
}
</style>
