import request from '@/config/axios'

export const pageLog = (params) => {
  return request.post({ url: '/system/KHIDIService/SysLog/PageLog', data: params });
}
export const funcLog = (params) => {
  return request.post({ url: '/system/KHIDIService/SysLog/FuncLog', data: params });
}
export const getOutSystem = (params) => {
  return request.postOriginal({ url: '/Portal/ServiceCenter/getOutSystemUrl', data: params });
}