import request from '@/config/axios'
export interface UserResetPwd {
  userId: string
  password: string
  repassword: string
}

// 个人设置 VO
export interface AUserVO {
  id: string // ID
  name: string // 姓名
  loginName: string // 登录名
  workNo: string // 工号
  password: string // 密码
  sortIndex: number // 排序
  deptID: string // 部门ID
  deptName: string // 部门名称
  deptFullID: string // 部门完整ID
  parttimeDeptID: string // 兼职部门
  parttimeDeptName: string // 兼职部门
  isAuth: string // 是否授权
  isDeleted: string // 是否删除
  deleteTime: Date // 删除时间
  lastLoginTime: Date // 最后登录时间
  lastLoginIP: string // 最后登录IP
  errorCount: number // 错误次数
  errorTime: Date // 错误时间
  modifyTime: Date // 修改时间
  description: string // 备注
  sex: string // 性别
  inDate: Date // 入职日期
  outDate: Date // 离职日期
  phone: string // 电话
  mobilePhone: string // 手机
  email: string // Emai
  address: string // 地址
  duties: string // 职务
  birthday: Date // 生日
  clientIp: string // 默认IP
  signPwd: string // 签名密码
  systemCode: string // 子系统编号
  sortIndex1: number // SortIndex1
  sortIndex2: number // SortIndex2
  sortIndex3: number // SortIndex3
  sortIndex4: number // SortIndex4
  sortIndex5: number // SortIndex5
  acceptMobileMsg: string // 是否接收手机短信
  status: string // 状态
  iDCardImg: byte[] // 身份证照片
  userImg: byte[] // 用户照片
  iDCardImgF: byte[] // 身份证反面
  ext1: string // Ext1
  officeId: string // OfficeId
  officeName: string // OfficeName
  ucmobile: string // ucmobile
  uctitle: string // uctitle
  ucshort: string // ucshort
  ucmail: string // ucmail
  thirdPartMail: string // thirdPartMail
  voipNumber: string // voipNumber
  outKey: string // OutKey
  idCard: string // 身份证号
  userType: string // 用户类型
  isAuthority: string // isAuthority
  nation: string // 民族
  beforeName: string // 曾用名
  politics: string // 政治面貌
  officeFullID: string // OfficeFullID
}

// 个人设置 API
export const AUserApi = {
  // 查询个人设置分页
  getAUserPage: async (params: any) => {
    return await request.get({ url: `/system/A-user/getAUserPage`, params })
  },

  // 查询个人信息单表
  getAUserPage2: async (params: any) => {
    return await request.get({ url: `/system/A-user/page2`, params })
  },
  // 查询个人设置详情
  getAUser: async (id: number) => {
    return await request.get({ url: `/system/A-user/get?id=` + id })
  },

  // 新增个人设置
  createAUser: async (data: AUserVO) => {
    return await request.post({ url: `/system/A-user/create`, data })
  },

  // 修改个人设置
  updateAUser: async (data: AUserVO) => {
    return await request.put({ url: `/system/A-user/update`, data })
  },
  // 修改个人设置
  resetPassword: async (data: UserResetPwd) => {
    return await request.post({ url: `/system/A-user/resetPwd`, data })
  },

  // 删除个人设置
  deleteAUser: async (id: number) => {
    return await request.delete({ url: `/system/A-user/delete?id=` + id })
  },

  // 导出个人设置 Excel
  exportAUser: async (params) => {
    return await request.download({ url: `/system/A-user/export-excel`, params })
  },
  // 解锁个人设置
  handleUnlock: async (id: number) => {
    return await request.delete({ url: `/system/A-user/unlock?id=` + id })
  },
  // 解锁个人设置
  getUserAll: async (params) => {
    return await request.get({ url: `/system/A-user/getUserAll`, params })
  },
}
