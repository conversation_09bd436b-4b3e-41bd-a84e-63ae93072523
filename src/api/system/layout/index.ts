import request from '@/config/axios'


// 获取列表和布局数据
export const getLayoutList = (params) => {
  return request.get({ url: '/system/layout/getLayout', params })
}

// 获取列表和布局数据
export const getDefaultLayout = (params) => {
  return request.get({ url: '/system/layout/getDefaultLayout', params })
}


// 添加布局数据
export const addLayoutData = (data: any) => {
  return request.post({ url: '/system/layout/saveLayoutData', data })
}
// 添加修改布局配置
export const saveLayoutConfig = (data: any) => {
  return request.post({ url: '/system/layout/saveLayoutConfig', data })
}


// 删除布局数据
export const delLayoutData = (params) => {
  return request.post({ url: '/system/layout/delLayoutData', params })
}


// 修改数据
export const updateLayoutData = (data: any) => {
  return request.post({ url: '/system/layout/updateLayoutData', data })
}

//获得拖拽组件列表
export const dragComponentList = (param: any) => {
  return request.get({ url: '/system/layout/getLayoutData?category=' + param })
}

// 添加布局
export const addLayoutList = (data: any) => {
  return request.post({ url: '/system/layout/saveLayoutList', data })
}

// 获取用户布局配置
export const getLayoutConfig = () => {
  return request.get({ url: '/system/layout/getLayoutConfig' })
}

// =======================================
// 获取分组布局数据
export const getGroupLayoutList = (params) => {
  return request.get({ url: '/system/admin-layout/list', params })
}

// 添加或修改布局
export const addOrUpdateLayoutList = (data: any) => {
  return request.post({ url: '/system/admin-layout/saveOrUpdate', data })
}
