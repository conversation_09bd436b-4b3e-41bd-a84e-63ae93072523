import request from '@/config/axios'

// 资源管理 VO
export interface AResVO {
  id: string // ID
  parentID: string // 父ID
  fullID: string // 全ID
  sortIndex: number // 排序索引
  name: string // 名称
  code: string // 编码
  type: string // 类型
  iconCls: string // 图标
  url: string // Url
  ctrlType: string // 控制类型
  auth: string // 权限，可以为页面控件ID，数据的查询条件
  description: string // 描述
  systemCode: string // 系统编码
  createUser: string // 创建人
  createUserID: string // 创建人id
  createTime: Date // 创建时间
  modifyUser: string // 修改人
  modifyUserID: string // 修改人id
  modifyTime: Date // 修改时间
  publishStatus: string // 发布状态
  prePublishStatus: string // 上次发布状态
  isDeleted: string // 逻辑删除
  execType: string // 入口的执行类型（弹出页面，再右侧添加子tab，执行一个js函数）
  scriptContent: string // js脚本内容（预留）
  releateConfigID: string // 关联配置项的ID
  isRefreshPage: string // 第二次打开时是否刷新页面
  bootstrapCls: string // 首页的图标样式
  requireDept: string // 需求部门
  useScope: string // 使用范围
  keyWord: string // 关键词
  specialUser: string // 特殊用户
  isSubsystem: string // 是否是子系统
  businessCategory: string // 业务目录
  files: string // 文件
  isenterprise: string // Isenterprise
  abbreviation: string // Abbreviation
  servicereamrk: string // Servicereamrk
  institutions: string // 关联制度
  business: string // 关联业务
  serviceDirectory: string // 服务指南
  inRes: string // 输入数据
  inResName: string // 输入数据
  outRes: string // 输出数据
  outResName: string // 输出数据
  isProtal: string // 门户显示
  otherFile: string // 相关文件
  menuImage: string // 菜单图片
  isTitle: string // 是否是标题
  sqrole: string // sqrole
  sqorg: string // sqorg
  squser: string // squser
  sqroleId: string // sqroleId
  sqorgId: string // sqorgId
  squserId: string // squserId
  keyWordId: string // KeyWordId
  glywId: string // glywId
  glyw: string // glyw
  glzdId: string // glzdId
  glzd: string // glzd
  sqRoleCount: number // SqRoleCount
  sqUserCount: number // SqUserCount
  sxYewu: string // SxYewu
  sxYewuName: string // SxYewuName
  xxYewu: string // XxYewu
  xxYewuName: string // XxYewuName
  dgRelatedFiles: string // dgRelatedFiles
  authUserID: string // AuthUserID
  authUserName: string // AuthUserName
  authRoleID: string // AuthRoleID
  authRoleName: string // AuthRoleName
  dataAuth: string // DataAuth
  ctrAuth: string // CtrAuth
}

// 资源管理 API
export const AResApi = {
  // 查询资源管理列表
  getAResList: async (params) => {
    return await request.get({ url: `/system/A-res/list`, params })
  },

  // 查询资源管理详情
  getARes: async (id: number) => {
    return await request.get({ url: `/system/A-res/get?id=` + id })
  },

  // 新增资源管理
  createARes: async (data: AResVO) => {
    return await request.post({ url: `/system/A-res/create`, data })
  },

  // 修改资源管理
  updateARes: async (data: AResVO) => {
    return await request.put({ url: `/system/A-res/update`, data })
  },

  // 删除资源管理
  deleteARes: async (id: number) => {
    return await request.delete({ url: `/system/A-res/delete?id=` + id })
  },

  // 导出资源管理 Excel
  exportARes: async (params) => {
    return await request.download({ url: `/system/A-res/export-excel`, params })
  },

  getResTree: async (params) => {
    return await request.get({ url: `/system/A-res/getResTree`, params })
  },
}
