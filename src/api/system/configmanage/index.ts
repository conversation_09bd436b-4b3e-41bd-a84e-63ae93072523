import request from '@/config/axios'

// 系统配置结构树 VO
export interface ConfigmanageVO {
  id: string // ID
  parentID: string // 父ID
  fullID: string // 全ID
  sortIndex: number // 排序索引
  name: string // 名称
  code: string // code
  type: string // 节点类型
  iconCls: string // iconcls
  url: string // 主界面地址
  ctrlType: string // 控制类型
  auth: string // 权限，可以为页面控件ID，数据的查询条件
  description: string // 描述
  systemCode: string // 子系统编号
  createUser: string // 创建人
  createUserID: string // 创建用户id
  modifyUser: string // 修改用户
  modifyUserID: string // 修改用户id
  modifyTime: Date // 修改时间
  status: string // 节点状态（未发布，已发布）
  editAuth: string // 节点编辑权限
  editAuthUser: string // 节点编辑权限
  configUrl: string // 节点的连接页面
  mainDBConn: string // 主数据库连接
  relateID: string // relateId
  relateTable: string // 关联表
  isMainUrl: string // 是否主界面
  isDeleted: string // 是否删除
  isStandard: string // isStandard
}

// 系统配置结构树 API
export const ConfigmanageApi = {
  // 查询系统配置结构树分页
  getConfigmanagePage: async (params: any) => {
    return await request.get({ url: `/system/configmanage/page`, params })
  },

  // 查询系统配置结构树详情
  getConfigmanage: async (id: number) => {
    return await request.get({ url: `/system/configmanage/get?id=` + id })
  },

  // 新增系统配置结构树
  createConfigmanage: async (data: ConfigmanageVO) => {
    return await request.post({ url: `/system/configmanage/create`, data })
  },

  // 修改系统配置结构树
  updateConfigmanage: async (data: ConfigmanageVO) => {
    return await request.put({ url: `/system/configmanage/update`, data })
  },

  // 删除系统配置结构树
  deleteConfigmanage: async (id: number) => {
    return await request.delete({ url: `/system/configmanage/delete?id=` + id })
  },

  // 导出系统配置结构树 Excel
  exportConfigmanage: async (params) => {
    return await request.download({ url: `/system/configmanage/export-excel`, params })
  },
}
