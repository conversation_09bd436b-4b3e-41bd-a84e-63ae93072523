import request from '@/config/axios'

// 执行系统脚本库
export const execSystemScript = (data) => {
  return request.postOriginal({ url: '/BasicApplication/Common/ExecSystemScript', data, headersType: 'multipart/form-data'})
}

// 服务中心功能项
export const serviceCenterloadData = async (data) => {
  return await request.postOriginal({ url: '/Portal/ServiceCenter/List', data})
}

//获取左侧菜单分类
export const serviceCenterGetLeftListCat = () => {
  return request.postOriginal({ url: '/Portal/ServiceCenter/LeftListCat' })
}

//资讯中心 - 首页文章列表
export const newsCenterGetNewsList = (params) => {
  return request.postOriginal({ url: '/Portal/WorkCenter/NewsList', params })
}

// 快速入口&友情链接
export const newsCenterGetLinks = (params) => {
  return request.postOriginal({ url: '/Portal/WorkCenter/Links', params })
}

// 资讯中心 - 首页文章列表 - 子表内容列表
export const newsCenterGetNewsListChildren = (params) => {
  return request.postOriginal({ url: '/Portal/WorkCenter/NewsListChildren', params })
}

// 科技创新
export const newsCenterGetTechnologicalInnovation = (params) => {
  return request.postOriginal({ url: '/Portal/NewsCenter/TechnologicalInnovation', params })
}

// 资讯中心详情页
export const newsCenterGetNewsDetail = (params) => {
  return request.postOriginal({ url: '/Portal/WorkCenter/NewsDetail', params })
}

// 获取项目中心数据
export const getXmzxUnitInfos = async () => {
  return await request.post({ url: '/Portal/Home/GetXmzxUnitInfos' })
}

// 获取资讯列表左侧菜单
export const publicInfoListGetZXLeftMenu = async () => {
  return await request.postOriginal({ url: '/Portal/NewsCenter/GetZXLeftMenu' })
}

// 获取资讯列表获取点击菜单后的数据
export const publicInfoListGetPublicInformData = async (params) => {
  return await request.postOriginal({ url: '/Portal/NewsCenter/GetPublicInformData', params })
}

// 获取一级菜单
export const GetSysMenu1l = async () => {
  return await request.post({ url: '/Portal/Home/GetSysMenu1l' })
}

// 获取未读消息数，待办工作数
export const getMsgCount = async () => {
  return await request.post({ url: '/Portal/Home/getMsgCount' })
}

// 关注取关
export const execMyFocus = async (data:any) => {

  return await request.post({ url: '/system/Portal/WorkCenter/FocusFlow',data})
}

// 根据关注id获取待办
export const GetMyFocusByID = async (params) => {

  return await request.post({ url: '/system/Portal/WorkCenter/GetMyFocusByID',params})
}

// 获取已办工作列表打开路径

export const getCompleteTasks = async (params) => {
  return await request.get({ url: import.meta.env.VITE_TOURL_PREFIX+ '/TaskApi/getCompleteTasks?page=1&pageSize=10&Activity=&Type=completetask',params})
}

// 根据关注id获取待办
export const IsOuterForm = async (params) => {

  return await request.post({ url: '/system/Portal/WorkCenter/IsOuterForm',params})
}

// 更新任务颜色
export const UpdateTaskNameColor = async (data:any) => {
  return await request.post({ url: '/system/Portal/WorkCenter/UpdateTaskNameColor',data})
}

// d对老系统任务进行处理
export const GetGWSTaskUrl = async (params) => {
  return await request.post({ url: '/system/Portal/WorkCenter/GetGWSTaskUrl',params})
}


// 取消收藏 KDAPI 地址(子系统后台)
export const CancelSc = async (url:string,data:any) => {
  return await request.post({ url: url,data})
}

// 获取文件token
export const GetFileToken = async (params) => {
  return await request.get({ url: '/system/Portal/WorkCenter/GetFileToken',params})
}

// 下载文件token,只负责执行访问下载
export const GetFileByUrl = async (url:string) => {
  return await request.get({ url: url})
}

// 记录功能使用日志
export const FuncLog = async (data:any) => {
  return await request.post({ url: '/system/Portal/WorkCenter/FuncLog',data})
}

//获取温馨提示列表
export const GetWarmPromptList = async (params) => {
  return await request.get({ url: "/system/Portal/WorkCenter/GetWarmPromptList", params })
}
//获取温馨提示详情
export const getWarmPromptDetail = async (params) => {
  return await request.get({ url: "/system/Portal/WorkCenter/GetWarmPromptDetail", params })
}

//发送获取code
export const GetVerificationCode = async (params) => {
  return await request.get({ url: "/Portal/Home/GetVerificationCode", params })
}

//校验code
export const CheckVerificationCode = async (params) => {
  return await request.get({ url: "/Portal/Home/CheckVerificationCode", params })
}

export const addPasswordLog = async (data) => {
  return await request.post({ url: `/system/auth/addPasswordLog`, data })
}
