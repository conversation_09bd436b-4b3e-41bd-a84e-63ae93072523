
import request from '@/config/axios'
import { getAccessToken} from '@/utils/auth'
const uploadUrl = import.meta.env.VITE_UPLOAD_URL
const downLoadUrl = import.meta.env.VITE_DOWNLOAD_URL

export interface FilePageReqVO extends PageParam {
  path?: string
  type?: string
  createTime?: Date[]
}

// 文件预签名地址 Response VO
export interface FilePresignedUrlRespVO {
  // 文件配置编号
  configId: number
  // 文件上传 URL
  uploadUrl: string
  // 文件 URL
  url: string
}

// 查询文件列表
export const getFilePage = (params: FilePageReqVO) => {
  return request.get({ url: '/infra/file/page', params })
}

// 删除文件
export const deleteFile = (id: number) => {
  return request.delete({ url: '/infra/file/delete?id=' + id })
}

// 获取文件预签名地址
export const getFilePresignedUrl = (path: string) => {
  return request.get<FilePresignedUrlRespVO>({
    url: '/infra/file/presigned-url',
    params: { path }
  })
}

// 创建文件
export const createDiskFile = (data: any) => {
  return request.post({ url: '/infra/file/create', data })
}

// 上传文件
// export const updateFile = (data: any) => {
//   return request.upload({ url: uploadUrl+"?token="+getAccessToken(), data })
// }


// 上传文件
export const updateFile = (data: any) => {
  data = {
    file: data.file,
    url: uploadUrl+"?token="+getAccessToken()
  }
  return request.upload({ url: '/infra/file/upload', data })
}


// =========================================================

// 查询文件列表
export const getDiskFilePage = (params: FilePageReqVO) => {
  return request.get({ url: '/infra/file/disk-page', params })
}

// 删除文件
export const deleteDiskFile = (id: number) => {
  return request.delete({ url: '/infra/file/disk-delete?id=' + id })
}

// 管理端文件下载
export const downloadFile = (FileToken: string, fileId: string) => {
  const data = {
    url: downLoadUrl + "?FileToken=" + FileToken,
    fileId: fileId
  }
  return request.downloadPost({ url: '/infra/file/download', data })
}

// 管理端文件下载
export const downloadFileByName = (fileName: string) => {
  return request.download({ url: downLoadUrl + '?FileID=' + fileName })
}


