import request from '@/config/axios'

// 任务列表
export const getJobPage = (params: PageParam) => {
  return request.get({ url: '/quartz/job/getList', params })
}
// 任务详情
export const getJob = (id: string) => {
  return request.get({ url: '/quartz/job/getJob?jobId=' + id })
}
// 新增任务
export const createJob = (data: any) => {
  return request.post({ url: '/quartz/job/add', data })
}
// 修改定时任务调度
export const updateJob = (data: any) => {
  return request.put({ url: '/quartz/job/update', data })
}
// 恢复定时任务
export const resumeJob = (jobId: string) => {
  return request.put({ url: '/quartz/job/resume?jobId=' + jobId })
}
// 停止定时任务
export const pauseJob = (jobId: string) => {
  return request.put({ url: '/quartz/job/pause?jobId=' + jobId })
}
// 删除定时任务调度
export const deleteJob = (id: string) => {
  return request.delete({ url: '/quartz/job/deleteJob?jobId=' + id })
}
// 导出定时任务调度
export const exportJob = (params) => {
  return request.download({ url: '/quartz/job/export-excel', params })
}
// 任务状态修改
export const updateJobStatus = (id: number, status: number) => {
  const params = {
    id,
    status
  }
  return request.put({ url: '/quartz/job/update', params })
}
// 定时任务立即执行一次
export const runJob = (id: string) => {
  return request.put({ url: '/quartz/job/trigger?jobId=' + id })
}
// 获得定时任务的下 n 次执行时间
export const getJobNextTimes = (id: string, jobHandlerName: string) => {
  const params = {
    jobId: id,
    jobHandlerName: jobHandlerName
  }
  return request.get({ url: '/quartz/job/getNextFireTime', params })
}


// =======================C_NewTimerTask==========================
// 任务列表
export const getTaskList = (params: any) => {
  return request.get({ url: '/quartz/task/getTasks', params })
}

// 任务详情
export const getTask = (id: string) => {
  return request.get({ url: '/quartz/task/getTask?id=' + id })
}
// 初始化
export const taskInit = (data: any) => {
  return request.post({ url: '/quartz/task/init', data })
}
// 运行
export const runTask = (taskId: string) => {
  return request.post({ url: '/quartz/task/resume?taskId=' + taskId })
}
// 暂停
export const pauseTask = (taskId: string) => {
  return request.post({ url: '/quartz/task/pause?taskId=' + taskId })
}
// 重启
export const startTask = (taskId: string) => {
  return request.post({ url: '/quartz/task/start?taskId=' + taskId })
}

// 删除
export const delTask = (data: any) => {
  return request.post({ url: '/quartz/task/deleteTask', data })
}
// 执行一次
export const triggerTask = (taskId: string) => {
  return request.post({ url: '/quartz/task/trigger?taskId=' + taskId })
}

export const getTaskNextTimes = (id: string, taskGroup: string) => {
  const params = {
    jobId: id,
    jobGroup: taskGroup
  }
  return request.get({ url: '/quartz/task/getTaskNextTimes', params })
}
