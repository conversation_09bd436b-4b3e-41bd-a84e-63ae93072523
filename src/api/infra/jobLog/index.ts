import request from '@/config/axios'

export interface JobLogVO {
  id: number
  jobId: number
  handlerName: string
  handlerParam: string
  cronExpression: string
  executeIndex: string
  beginTime: Date
  endTime: Date
  duration: string
  status: number
  result: string
  createTime: string
}

// 任务日志列表
export const getJobLogPage = (params: PageParam) => {
  return request.get({ url: '/quartz/job-log/getLogList', params })
}

// 任务日志详情
export const getJobLog = (id: string) => {
  return request.get({ url: '/quartz/job-log/getLog?id=' + id })
}

// 删除日志列表
export const delJobLogs = (params) => {
  return request.post({ url: '/quartz/job-log/delJobLogs', params })
}

// 导出定时任务日志
export const exportJobLog = (params) => {
  return request.download({
    url: '/infra/job-log/export-excel',
    params
  })
}
