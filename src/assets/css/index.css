@font-face {
    font-family: "iconfont";
    src: url(/WebResource/Bootstrap/fonts/iconfont.eot);
    /* IE9*/
    src: url(/WebResource/Bootstrap/fonts/iconfont.eot?#iefix) format("embedded-opentype"), /* IE6-IE8 */
    url(/WebResource/Bootstrap/fonts/iconfont.woff) format("woff"), /* chrome, firefox */
    url(/WebResource/Bootstrap/fonts/iconfont.ttf) format("truetype"), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
    url(/WebResource/Bootstrap/fonts/iconfont.svg#iconfont) format("svg");
    /* iOS 4.1- */
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 24px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0px;
    -moz-osx-font-smoothing: grayscale;
}

html,
body {
    margin: 0;
    padding: 0;
    /* font-family:Microsoft YaHei UI; */
    background-color: #ffffff;
    color: #333;
}

img {
    border: 0px;
}

ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.fl {
    float: left !important;
}

.fr {
    float: right !important;
    padding: 0 10px 0 0;
}

.head {
    width: 100%;
    min-width: 960px;
    height: 50px;
    background-color: #2dc3e8;
    border-bottom: 0px solid #ddd;
    overflow: hidden;
}

.head .menu {
    /* font-family:Microsoft YaHei UI; */
    height: 50px;
    overflow: hidden;
    white-space: nowrap;
    width: auto;
    /*border-left: 1px solid #ddd;*/
}

.head .menu li {
    /*float: left;*/
    cursor: pointer;
    font-size: 13pt;
    height: 50px;
    line-height: 50px;
    text-align: center;
    /*border-right: 1px solid #ddd;*/
    display: inline-block;
}

.head .menu li a {
    float: left;
    cursor: pointer;
    height: 50px;
    line-height: 50px;
    text-align: center;
    padding: 0 23px;
    border-right: 0px solid #ddd;
}

.head .menu li a:hover {
    float: left;
    cursor: pointer;
    background-color: rgb(51, 136, 255);
    color: #fff;
    height: 50px;
    line-height: 50px;
    text-align: center;
    padding: 0 23px;
    border-right: 0px solid #ddd;
    text-decoration: none;
}

.head .scroll {
    /* font-family:Microsoft YaHei UI; */
    width: 25px;
    height: 50px;
    overflow: hidden;
    white-space: nowrap;
}

.head .scroll li {
    /*float: left;*/
    cursor: pointer;
    font-size: 11pt;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-right: 0px solid #ddd;
    display: inline-block;
}

.head .scroll li a {
    padding-top: 3px;
    float: left;
    cursor: pointer;
    height: 50px;
    line-height: 50px;
    text-align: center;
}

.head .scroll li a:hover {
    float: left;
    cursor: pointer;
    height: 50px;
    line-height: 50px;
    text-align: center;
    text-decoration: none;
}

.head .btns a {
    padding: 10px 5px 10px 5px;
    border-left: 0px solid #ddd;
    height: 50px;
    float: left;
    position: relative;
}

.head .btns a:hover {
    border-left: 0px solid #ddd;
    padding: 9px 5px 10px 5px;
    height: 50px;
    float: left;
    text-decoration: none;
    /*background-color: rgb(51,136,255);*/
}

.head .btns a span {
    display: block;
    position: absolute;
    width: 15px;
    height: 15px;
    border-radius: 10px;
    background: #ff6565;
    color: #fff;
    z-index: 2;
    top: 6px;
    right: 0px;
    text-align: center;
}


/*.body
{
    padding: 20px;
    width: 1000px;
    width: -moz-calc(100% - 40px);
    width: -webkit-calc(100% - 40px);
    width: calc(100% - 40px);
}*/

.body .btl {
    float: left;
    height: 400px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.body .left {
    width: 34%;
    width: calc((100% - 390px) * 0.47);
    width: -moz-calc((100% - 390px) * 0.47);
    width: -webkit-calccalc((100% - 390px) * 0.47);
    margin-right: 20px;
}

.body .center {
    width: 38%;
    width: calc((100% - 390px) * 0.53);
    width: -moz-calc((100% - 390px) * 0.53);
    width: -webkit-calccalc((100% - 390px) * 0.53);
    margin-right: 20px;
}

.body .right {
    width: 350px;
}

.current {
    background-color: rgb(51, 136, 255);
    color: #fff;
}


/*更多菜单样式*/

.dh-fm-more {
    cursor: pointer;
    background: url(../Images/more.png) no-repeat;
    background-position: 20px 14px;
    color: #000;
    width: 75px;
    padding-left: 23px;
    height: 50px;
    line-height: 50px;
    overflow: hidden;
    background-color: #f2f2f2;
    overflow: hidden;
    z-index: 9999;
}

.dh-fm-more:hover {
    color: #fff;
    background-color: rgb(51, 136, 255);
}

.dh-fm-more.current {
    color: #fff;
    background-color: rgb(51, 136, 255);
}

.dh-moremain {
    position: absolute;
    left: 10px;
    top: 1px;
    width: 135px;
    background-color: #f2f2f2;
    border: 1px solid rgb(51, 136, 255);
    z-index: 99999;
}

.dh-mm-menu {
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0px;
    padding: 0px;
}

.dh-mm-menu ul {
    margin: 0px;
    padding: 0px;
    list-style-type: none;
}

.dh-mm-menu li {
    cursor: pointer;
    float: left;
    width: 100%;
    display: inline-block;
    overflow: hidden;
    height: 31px;
    border-bottom: #ccc 1px solid;
}

.dh-mm-menu li a {
    float: left;
    line-height: 31px;
    height: 31px;
    padding-left: 23px;
    width: 100%;
    text-decoration: none;
}

.dh-mm-menu li:hover {
    color: #fff;
    background-color: rgb(51, 136, 255);
}

.dh-mm-menu li.current {
    color: #fff;
    background-color: rgb(51, 136, 255);
}


/*左边菜单样式*/

.cd-left {
    position: fixed;
    right: 1px;
    top: 49px;
    background-color: #f2f2f2;
    z-index: 50;
}

.cd-left ul {
    /* font-family:Microsoft YaHei UI; */
    font-size: 9pt;
    width: 100%;
    border: 0px solid #ccc;
}

.cd-left li {
    line-height: 35px;
    position: relative;
    padding: 0px 0px 0px 10px;
    height: 35px;
    border-bottom: 1px solid #ccc;
    overflow: hidden;
    margin: 0px;
}

.cd-left li:hover {
    cursor: pointer;
    color: #fff;
    background-color: rgb(51, 136, 255);
}


/*快捷菜单样式*/

.shortcutmenu {
    background-color: #f2f2f2;
    position: absolute;
    right: 0px;
    top: 52px;
    bottom: 0px;
    width: 140px;
    border: 1px solid rgb(51, 136, 255);
    border-right: 0px solid #cbcbcb;
    z-index: 9000;
}

.shortcutmenu ul {
    line-height: 27px;
    background: #ffffff;
    list-style-type: none;
    width: 140px;
    margin: 0;
    padding: 0;
}

.shortcutmenu ul li {
    float: left;
    width: 140px;
    border-bottom: 1px solid #ccc;
    height: 27px;
    overflow: hidden;
}

.shortcutmenu ul li:hover {
    color: #fff;
    background-color: rgb(51, 136, 255);
}

.shortcutmenu_onmouse {
    background: #f0f0f0;
}

.shortcutmenu_span_left {
    width: 105px;
    float: left;
    padding-left: 15px;
}

.shortcutmenu_span_right {
    width: 20px;
    float: right;
}

.shortcutmenu ul a {
    text-align: left;
    overflow: hidden;
}

.shortcutmenu ul a:link {
    color: #000;
    text-decoration: none;
}

.shortcutmenu ul a:visited {
    color: #000;
    text-decoration: none;
}

.shortcutmenu ul a:hover {
    color: #fff;
    text-decoration: underline;
    font-weight: normal;
}


/*工具箱*/

.toolBox {
    background-color: #f2f2f2;
    position: absolute;
    right: 0px;
    bottom: 0px;
    width: 300px;
    height: 380px;
    border: 1px solid rgb(51, 136, 255);
    border-right: 0px solid #cbcbcb;
    z-index: 9000;
}

.toolBox ul li {
    float: left;
    width: 148px;
    height: 35px;
    line-height: 35px;
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    overflow: hidden;
    position: relative;
}

.toolBox ul li:hover {
    color: #fff;
    background-color: rgb(51, 136, 255);
}

.toolBox_onmouse {
    background: #f0f0f0;
}

.toolBox_span_left {
    width: 105px;
    float: left;
    padding-left: 15px;
}

.toolBox_span_right {
    width: 20px;
    float: right;
}

.toolBox ul a {
    text-align: left;
    overflow: hidden;
}

.toolBox ul a:link {
    color: #000;
    text-decoration: none;
}

.toolBox ul a:visited {
    color: #000;
    text-decoration: none;
}

.toolBox ul a:hover {
    color: #fff;
    text-decoration: underline;
    font-weight: normal;
}

.advert::after {
    position: absolute;
    content: "";
    display: block;
    background: #00408b;
    width: 4px;
    left: 0;
    top: 16px;
    height: 20px;
}

.advtab {
    display: inline-block;
    float: left;
}

.advmore {
    margin: 8px 30px 0 0;
    display: inline-block;
    float: right;
    line-height: 36px;
    font-size: 14px;
    color: #666666;
    cursor: pointer;
}

.advmore a {
    color: #666666;
}


/*.advmore::after {
  position: absolute;
  content: "›";
  margin: 0 0 0 10px;
  font-size: 23px;
  line-height: 32px;
}*/

.advmore:hover {
    color: #0053a8;
}

.advtab span {
    padding: 0 0 0 24px;
    font-size: 18px;
    color: #666666;
    font-weight: bolder;
    /*cursor: pointer;*/
}

.advtab .ad {
    display: inline-block;
    line-height: 50px;
    color: #00408b;
}

.advtav_title {
    border-bottom: 3px solid #00408b;
}

.torial {
    clear: both;
}

.college {
    display: none;
}

.college ul {
    width: 50%;
    height: 200px;
    border-top: none;
    float: left;
}

.college ul:first-child {
    border-right: 1px solid #e2e2e2;
}

.college ul li {
    width: 100%;
    padding: 5px 0;
}

.college ul li a {
    margin: 0 0 0 14px;
    font-size: 14px;
    color: #666666;
    display: inline-block;
    width: 260px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.college ul li span {
    float: right;
    margin: 0 14px 0 0;
    font-size: 14px;
    color: #999999;
}

.college ul li:hover a,
.college ul li:hover span {
    color: #00408b;
}

.co {
    display: block;
}

.special {
    width: 100%;
    display: inline-block;
    border: 1px solid #e2e2e2;
    border-top: none;
}

.spectab {
    position: relative;
    display: inline-block;
    float: left;
    width: 33%;
}

.spectab span {
    display: block;
    padding: 20px 0 20px 34px;
    font-size: 14px;
    color: #323233;
    background-color: #f5f5f5;
    cursor: pointer;
}

.spectab span::after {
    position: absolute;
    content: "›";
    right: 22px;
    margin: 0 0 0 10px;
    font-size: 23px;
    line-height: 14px;
}

.specpanel {
    display: inline-block;
    width: 67%;
    /* background-color: cyan; */
}

.specitem {
    display: none;
}

.specitem ul {}

.specitem ul li {
    padding: 6px 0;
}

.specitem ul li a {
    margin: 0 0 0 14px;
    font-size: 14px;
    color: #666666;
    display: inline-block;
    width: 360px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.specitem ul li span {
    float: right;
    margin: 0 14px 0 0;
    font-size: 14px;
    color: #999999;
}

.specitem ul li:hover a,
.specitem ul li:hover span {
    color: #00408b;
}

.newpanel {
    display: none;
}

.newpanel ul {}

.newpanel ul li {
    padding: 5px 0;
}

.newpanel ul li a {
    margin: 0 0 0 14px;
    font-size: 14px;
    color: #666666;
    display: inline-block;
    width: 360px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.newpanel ul li span {
    float: right;
    margin: 0 14px 0 0;
    font-size: 14px;
    color: #999999;
}

.newpanel ul li:hover a,
.newpanel ul li:hover span {
    color: #00408b;
}

.spectab .spe {
    background: linear-gradient(to right, #009ce4, #014fa4);
    color: #ffffff;
}

.newtab .spe {
    background: linear-gradient(to right, #009ce4, #014fa4);
    color: #ffffff;
}

.pan {
    display: block;
}

.part {
    display: inline-block;
    width: 26.3%;
}

.part_ul {
    width: 100%;
}

.part_ul>li {
    margin-bottom: 12px;
    width: 100%;
}

.part_ul>li a {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.part_ul>li img {
    width: 100%;
    position: absolute;
    z-index: 0;
    left: 0;
    top: 0;
}

.part_max {
    width: 100%;
    height: 67px;
}

.part_max p {
    line-height: 67px;
    margin: 0;
    color: #fff;
}

.part_min p {
    line-height: 76px;
    margin: 0;
    color: #fff;
}

.part_min {
    width: 100%;
    height: 76px;
}

.part_ul>li p {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.quick {
    background-color: #ffffff;
}

.hole {
    width: 100%;
    border: 1px solid #e2e2e2;
    margin-top: 13px;
}

.hole span {
    padding: 12px 0 12px 24px;
    display: inline-block;
    font-size: 18px;
    color: #00408b;
    text-align: center;
    font-weight: bolder;
    border-bottom: 3px solid #00408b;
}

.technology {
    margin: 20px 0 0 0;
}

.technology span {
    width: 140px;
}

.system {
    margin: 20px 0 0 0;
}

.size_z {
    font-size: 24px;
}

.size_max {
    font-size: 26px;
}

.item ul li {
    margin: 2px 0 0 0;
    padding: 8px 0 8px 16px;
    background-color: #f5f5f5;
    cursor: pointer;
}

.item ul li:first-child {
    margin: 0;
}

.item ul li:hover {
    border-left: 1px solid #e2e2e2;
    border-right: 1px solid #e2e2e2;
}

.item ul li a {
    font-size: 15px;
    color: #333333;
}

.statistical {
    margin: 20px 0 0 0;
    background-color: #f5f5f5;
    border: 1px solid #e2e2e2;
}

.total {
    padding: 18px 0 18px 18px;
    font-size: 18px;
    color: #00408b;
    font-weight: bolder;
}

.number {
    padding: 0 0 10px 20px;
}

.number span {
    display: block;
    font-size: 15px;
    color: #333333;
    line-height: 30px;
}

.newtab {
    position: relative;
    display: inline-block;
    float: left;
    width: 33%;
}

.newtab div {
    display: block;
    padding: 20px 0 20px 34px;
    font-size: 14px;
    color: #323233;
    background-color: #f5f5f5;
    cursor: pointer;
}

.newtab div i {
    float: right;
    margin-right: 20px;
}


/*.newtab span {
  display: block;
  padding: 20px 0 20px 34px;
  font-size: 14px;
  color: #323233;
  background-color: #f5f5f5;
  cursor: pointer;
}
.newtab span::after {
  position: absolute;
  content: "›";
  right: 22px;
  margin: 0 0 0 10px;
  font-size: 23px;
  line-height: 14px;
}*/

.demand {
    display: inline-block;
    float: left;
    padding: 7px 0;
    width: 33%;
    background-color: #f5f5f5;
    border: 1px solid #ffffff;
}

.demand a {
    margin: 0 0 0 12px;
    font-size: 16px;
    color: #333333;
    text-align: center;
}

.outer {
    margin: 48px 0 0 0;
    width: 100%;
}

.outer div {
    display: inline-block;
    float: left;
    margin: 0 3% 0 0;
    width: 30%;
}

.outer img {
    width: 100%;
    height: auto;
}


/*list.html内容*/

.l_left {
    display: inline-block;
    width: 26.3%;
}

.l_left img {
    width: 100%;
    height: auto;
}

.l_item {
    position: relative;
}

.l_programa {
    margin: 0 0 0 20px;
    padding: 14px 0;
    font-size: 16px;
    color: #333333;
    border-top: 1px solid #eeeeee;
}

.l_programa:after {
    position: absolute;
    content: "";
    right: 36px;
    margin: 6px 0 0 0;
    width: 10px;
    height: 12px;
    background: url("../image/down.png") no-repeat;
    background-position: center center;
    background-size: 100% 100%;
}

.l_column {
    display: none;
    padding: 12px 0;
}

.l_column a {
    margin: 0 0 0 20px;
    padding: 6px 0;
    display: block;
    font-size: 16px;
    color: #666666;
}

.l_active {
    display: block;
}

.l_active a:hover {
    color: #00408b;
}

.l_right {
    display: inline-block;
    float: right;
    margin: 0 0 0 2%;
    width: 71.7%;
}

.position {
    padding: 8px 0;
    border-bottom: 1px solid #eeeeee;
}

.position span {
    display: inline-block;
    margin: 0 0 0 12px;
    font-size: 14px;
    color: #4c4c4c;
}

.position span:first-child {
    margin: 0;
    padding: 0 0 0 12px;
    border-left: 4px solid #00408b;
}

.position span:nth-child(2) {
    padding: 0 0 0 30px;
    background: url("../image/home.png") no-repeat;
    background-position: center left;
    background-size: 20px 18px;
}

.rail div {
    border-bottom: 1px solid #eeeeee;
    line-height: 60px;
    height: 60px;
}

.rail div a {
    font-size: 16px;
    color: #333333;
    display: inline-block;
    width: 700px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.rail div a:hover {
    color: #00408b;
}

.rail div span {
    display: inline-block;
    float: right;
    padding: 0 20px 0 0;
    /*background: url("../image/file.png") no-repeat;*/
    background-position: center right;
    background-size: 20px 20px;
}

.sage {}

.sage div {
    clear: both;
    padding: 20px 0;
    border-bottom: 1px solid #eeeeee;
}

.sage div img {
    display: inline-block;
    float: left;
    width: 20%;
    height: auto;
}

.sage div div {
    padding: 0;
    margin: 0 0 0 2%;
    display: inline-block;
    width: 78%;
    border: none;
}

.sage div div span {
    display: block;
    margin-top: 10px;
}

.sage div div span:first-child {
    font-size: 18px;
    color: #333333;
    font-weight: bolder;
}

.sage div div span:nth-child(2) {
    font-size: 16px;
    color: #333333;
}

.sage div div span:nth-child(2) em {
    font-size: 14px;
    color: #cc0000;
}

.sage div div span:nth-child(2) em:hover {
    cursor: pointer;
    text-decoration-line: underline;
}

.sage div div span:nth-child(3) {
    text-align: right;
    font-size: 14px;
}

.artitle {
    padding: 24px 0 20px 0;
    font-size: 26px;
    color: #333333;
    text-align: center;
    border-bottom: 1px solid #eeeeee;
}

.ardesc {
    margin: 12px 0 0 0;
}

.ardesc div {
    float: left;
    width: 20%;
    font-size: 13px;
    color: #666666;
}

.ardesc div:nth-child(2) {
    text-align: center;
}

.ardesc div:nth-child(3) {
    text-align: center;
}

.ardesc div:nth-child(4) {
    text-align: center;
}

.ardesc div:last-child {
    text-align: right;
}

#artitle {
    padding: 24px 0 10px 0;
    font-size: 21px;
    color: #333333;
    text-align: center;
    /* font-family: "Microsoft YaHei"; */
    font-weight: bold;
}

.arftitle {
    font-size: 18px;
    color: #333333;
    text-align: center;
    border-bottom: 1px solid #eeeeee;
    /* font-family: "SimSun"; */
}

.blog {
    clear: left;
    margin: 50px 0 0 0;
    /* font-family: SimSun; */
    font-size: 18px;
}

.blog img {
    width: 600px;
    margin: auto !important;
}

.blog p {
    line-height: 1.5 !important;
}

.blog div {
    font-size: 16px;
    color: #666666;
}

.next {
    margin: 24px 0 24px 0;
}

.next span {
    width: 50%;
}

.next span:last-child {
    text-align: right;
}

.next span a {
    font-size: 16px;
    color: #666666;
    font-weight: normal;
}

.next span a:hover {
    color: #00408b;
}


/*分包公示数目样式*/

#fbgs_count {
    background: #1e87f0;
    color: #fff;
    min-width: 20px;
    height: 20px;
    border-radius: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    float: left;
    margin-left: 6px;
    margin-top: 9px;
    padding: 0 4px;
}

.btit {
    float: left;
    font-size: 16px;
    font-weight: bold;
    color: #404a56;
}


/*资讯中心 start*/

.gcs_tabitem>.gcs_rightlink_item>a>.gcs_title {
    padding-left: 0 !important;
}

.zxzx_banner {
    width: 650px;
}

.news_banner,
.news_banner img {
    width: 100%;
    height: 338px;
}


/*项目人员任免 - 列表样式*/

.article_list .item {
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px 0 22px;
}

.article_list .item:hover {
    background-color: #f4faff;
}

.article_list li.item a.link {
    font-size: 16px;
    width: 300px;
    text-decoration: none;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #555;
}

.wlaqsj .article_list li.item a.link {
    width: 260px;
}

.article_list li.item a.link:hover {
    color: #555;
}

.article_list li.item .datetime {
    font-size: 16px;
    color: #80848d;
}


/*集团要闻*/

.top_news .item {
    padding: 0 15px 0 22px;
}

.top_news li.item a.link {
    width: 418px;
}

.top_news .item:hover .link::before {
    content: "";
    display: block;
    width: 4px;
    height: 40px;
    background: #0d5ffe;
    border-radius: 0px 8 px 8px 0 !important;
    /* box-shadow: 0 2px 1px #ccc inset; */
    position: absolute;
    left: 0;
    top: 0;
}


/*文章置顶图标*/

.has_icon {
    position: relative;
    padding-left: 20px;
}

.has_icon .top {
    content: "";
    width: 20px;
    height: 24px;
    position: absolute;
    background: url("/Portal/Images/is_top.png") no-repeat center center;
    background-size: cover;
    left: 4px;
    top: 8px;
}

.has_icon .file {
    content: "";
    width: 12px;
    height: 15px;
    position: absolute;
    background: url("/WebResource/Master/images/index_icon_file.png") no-repeat center center;
    background-size: cover;
    left: 4px;
    top: 12px;
}

.hbm-moreico {
    text-decoration: none !important;
}


/*loadingbox - 样式*/

.emptybox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 120px;
}

.emptybox .loading_icon div {
    background: #999;
}

.emptybox .text {
    font-size: 14px;
    margin-top: 10px;
}


/*快速入口&友情链接*/

.list .item {
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-decoration: none;
    padding: 0 10px 0 10px;
}

.list .item::before {
    content: "";
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background: #3c8cf1;
}

.list .item .text {
    margin-right: auto;
    margin-left: 6px;
    width: 130px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.list .item span {
    font-size: 16px;
    color: #3c8cf1;
}


/*专题专栏*/

.hbm-zxzx-ztzl {
    height: 338px !important;
}


/*快速入口*/

.hbm-zxzx-ksrk {
    height: 450px !important;
}


/*分包公示*/

.fbgs .title {
    text-decoration: none;
    color: #555;
    font-size: 16px !important;
}


/*资讯中心 end*/