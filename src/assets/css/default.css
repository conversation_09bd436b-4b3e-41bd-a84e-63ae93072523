.body {
    width: 1252px;
    margin: 0 auto 10px auto;

    .row {
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
        flex-direction: row;
    }
}

.banner {
    opacity: 1;
    height: 100%;
    width: 100%;
    padding-top: 16px;

    :deep(.swiper) {
        width: 100%;
        height: 100%;
    }

    .btxt {
        height: 34px;
        line-height: 34px;
        width: 100%;
        position: absolute;
        padding: 0;
        margin: 0;
        bottom: 0;
        left: 0;
        text-align: left;
        font-size: 14px;
        color: rgb(255, 255, 255);
        background: rgba(0, 0, 0, 0.7);

        .swiper-bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .text {
                height: 100%;
                line-height: 34px;
                font-size: 14px;
                color: rgb(255, 255, 255);
                flex: 1;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                margin: 0 15px 0 15px;
            }

            .swiper-more {
                height: 100%;
                line-height: 34px;
                width: 40px;
                font-size: 14px;
                color: rgb(255, 255, 255);
            }
        }

    }

    :deep(.swiper-pagination-bullets) {
        width: 100%;
        text-align: right;
        margin-bottom: 0;
        height: 34px;
        line-height: 34px;
    }

    .swiper-pagination-bullets .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        background: #fff;
        opacity: .5;
    }

    :deep(.swiper-pagination-bullets) {
        margin-bottom: 30px;
    }

    :deep(.swiper-pagination-bullets .swiper-pagination-bullet) {
        width: 10px;
        height: 10px;
        background: #fff;
        opacity: 0.5;
    }

    :deep(.swiper-pagination-bullets .swiper-pagination-bullet-active) {
        opacity: 1;
    }
}

.banner:hover .swiper-button-prev {
    margin-left: -10px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

.banner:hover .swiper-button-prev, .banner:hover .swiper-button-next {
    opacity: 0.9;
    color: rgb(25, 27, 27);
    width: 20px;
    height: 38px;
    background: rgb(255, 255, 255);
}

.list {
    width: 400px;
    display: flex;
    flex-direction: column;
    margin-top: 4px;

    .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        padding: 0 6px;
        font-size: 16px;

        .date {
            display: flex;
            align-items: center;
            margin: 0;
            padding-right: 8px;

            .day {
                font-size: 16px;
                font-weight: 700;
                color: rgb(60, 145, 214);
            }

            .xgline {
                font-size: 14px;
                color: rgb(60, 145, 214);
                margin: 0px 4px;
            }

            .year {
                font-size: 14px;
                color: rgb(60, 145, 214);
            }
        }

        .remind {
            color: rgb(255, 126, 0) !important;
            font-weight: 550 !important;
            cursor: pointer;
        }

        .href2 {
            margin: 0;
        }

        .href1 {
            margin: 0;
        }
    }

    .row-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .c1btn {
            width: 195px;
            height: 164px;
            display: block;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
            border-radius: 10px;

            .txt {
                position: absolute;
                left: 27px;
                top: 37px;
                z-index: 2;

                .c {
                    font-size: 18px;
                    font-weight: 700;
                }

                .e {
                    font-size: 12px;
                }
            }

            .icon {
                width: 195px;
                height: 100%;
                position: absolute;
                left: 0px;
                top: 0px;
                z-index: 1;
            }
        }

        .c2btn {
            width: 172px;
            height: 76px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            border-radius: 10px;
            padding: 8px;
            gap: 10px;

            .icon {
                width: 32px;
                height: 32px;
            }

            .text {
                font-size: 14px;
                color: #363636;
            }
        }
    }

    .row-list .c1btn:nth-child(1) .txt {
        .c {
            color: #4c8875;
        }

        .e {
            color: #4c8875;
        }
    }

    .row-list .c1btn:nth-child(2) .txt {
        .c {
            color: #366ea9;
        }

        .e {
            color: #366ea9;
        }
    }

    .row-list .c1btn:nth-child(3) .txt {
        .c {
            color: #3c6e81;
        }

        .e {
            color: #3c6e81;
        }
    }

    .row-list .c1btn:nth-child(4) .txt {
        .c {
            color: #475c9e;
        }

        .e {
            color: #475c9e;
        }
    }

    .c3btn {
        width: 100%;
        height: 36px;
        display: flex;
        align-items: center;
        margin-bottom: 1px;
        padding: 0 8px;
        border-radius: 8px !important;
        justify-content: space-between;

        .icon {
            width: 16px;
            height: 16px;
        }

        .text {
            font-size: 16px;
        }
    }

}

.c4btn {
    width: 195px;
    height: 104px;
    display: flex;
    align-items: center;
    border-radius: 10px;
    margin-bottom: 6px;
    padding: 0 15px;

    .icon {
        width: 38px;
        height: 38px;
        margin-right: 13px;
    }

    .text {
        font-size: 16px;
        color: #363636;

        .tit {

        }

        .desc {
            color: #496ce3;
            font-size: 12px;
            margin-top: 10px;
        }
    }
}

.node-c4 {
    display: flex;
    gap: 10px;
}

.c5btn {
    height: 36px;
    display: flex;
    align-items: center;
    border-radius: 10px;
    padding: 0 15px;
    cursor: pointer;
    .icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }

    .text {
        font-size: 16px;
        color: #363636;

        .tit {

        }

        .desc {
            color: #496ce3;
            font-size: 12px;
            margin-top: 10px;
        }
    }
}

:deep(.swiper-slide .item ) {
    width: 100%;
    height: 100%;
}

.isTop {
    position: absolute;
    left: -9px;
    top: 0;
    width: 28px;
    height: 28px;
}

.ellipsis1 {
    height: 18px;
    position: relative;
    line-height: 18px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

.href1 {
    height: 40px;
    flex: 1;
    padding-left: 10px;
    margin: 12px 0px;
    position: relative;
    line-height: 40px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

.href2 {
    width: 440px;
    height: 44px;
    padding-left: 10px;
    margin: 12px 0px;
    position: relative;
    line-height: 22px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

/*.href2::before {*/
/*    content: "";*/
/*    display: block;*/
/*    width: 4px;*/
/*    height: 4px;*/
/*    position: absolute;*/
/*    top: 7px;*/
/*    left: 0;*/
/*    border-radius: 4px;*/
/*    background: rgb(44, 47, 71);*/
/*}*/

.href2:hover, .href1:hover {
    color: #0070ff;
    cursor: pointer;
}

.btn {
    background: #fefefe;
    cursor: pointer;
    box-sizing: border-box;
    transition: 0.3s;
    border-width: 1px;
    border-style: solid;
    border-color: #eee;
    border-image: initial;

}

.btn:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-width: 1px;
    border-style: solid;
    border-color: rgb(238, 238, 238);
    border-image: initial;
}
