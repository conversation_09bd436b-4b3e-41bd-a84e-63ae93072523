.work-top .uk-card .uk-card-header {
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #dddee1;
  /* background-color: antiquewhite; */
}

.work-top .uk-card .uk-card-header a {
  cursor: pointer;
  padding-right: 0;
}


.work-top .uk-card .uk-list li {
  height: 40px;
  line-height: 40px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 2px solid #fff;
  overflow: hidden;
  box-sizing: border-box;
  padding-right: 20px;
}

.work-top .uk-card .uk-list li span {
  box-sizing: border-box;
}

.work-top .uk-card .uk-list li span:first-child {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: 10px;
  margin-left: 4px;
}

/* 列表样式 */
.work-top .uk-card .uk-list li span:last-child {
  width: 92px;
}

.work-top .uk-card .uk-list li:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

.focus-flow hr {
  margin: 13px 5px !important;
}


.work-top .uk-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 16px;
  line-height: 40px;
}

.work-top .uk-card .uk-card-header a {
  right: 26px;
  top: 0;
  position: absolute;
}

.work-top .uk-card .uk-card-header>div {
  height: 40px;
  width: auto;
  margin-right: 24px;
  cursor: pointer;
}

.work-top .uk-card .uk-card-header>div>span {
  font-weight: bold;
  font-size: 16px;
  color: #555;
  /* font-family: "MicrosoftYaHei"; */
}

.work-top .uk-card .uk-card-header>div>.uk-card-title {
  font-style: normal !important;
}

/* 头部点击变化 */
.work-top .uk-card .uk-card-header div .title_0.active {
  background: url("@/assets/icons/portal/index_icon_yfw.png") no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div .title_1.active {
  background: url("@/assets/icons/portal/index_icon_ytz.png") no-repeat center center;
  background-size: 90% auto;
}

.work-top .uk-card .uk-card-header div .title_2.active {
  background: url("@/assets/icons/portal/index_icon_ygg.png") no-repeat center center;
  background-size: 90% auto;
}

/* 咨询中心部分头部处理 active_2 */
.work-top .uk-card .uk-card-header>.active_2 {
  color: #0070ff;
  border-bottom: 2px solid #0070ff;
  display: flex;
  align-items: center;
  text-align: center;
  width: auto;
}

.work-top .uk-card .uk-card-header>.active_2 .uk-card-title {
  font-style: italic !important;
}

.work-top .uk-card .uk-card-header div>.active_2 {
  color: #0070ff;
  font-weight: bold;
}


.work-top .uk-card .uk-card-header>.active {
  color: #0070ff;
  position: relative;
}

.work-top .uk-card .uk-card-header>.active::after {
  content: "";
  border-bottom: 2px solid #0070ff;
  width: 60px;
  height: 4px;
  background: #0d5ffe;
  border-radius: 8px 8px 0 0;
  position: absolute;
  bottom: 0;
  left: 0;
}

.work-top .uk-card .uk-card-header div>.active {
  color: #0070ff;
  width: 60px;
  height: 40px;
  line-height: 40px;
  display: block;
  text-align: center;
  text-indent: -9999px;
}

.work-top .uk-card .uk-card-header div>.active::before {
  content: "";
  width: 10px;
  height: 10px;
  border: 3px solid #0070ff;
  border-radius: 10px;
  background: #fff;
  position: absolute;
  top: 8px;
  left: -8px;
}


/*--字体重新调整--*/
.work-top .uk-card .uk-list li,
.work-center .uk-card .uk-list li,
.gcs_newslist_item span.gcs_title a,
.gcs_newslist_item span.gcs_dt,
.gcs_fbgs_table .gcs_body .gcs_body_inner table tr td .gcs_tablecell span,
.gcs_tab_kjcxyd_childs .gcs_tabhd_item span:last-child,
.gcs_tab_yfwbmfwryrm>.gcs_tabhd ul li,
.gcs_fbgs_table .gcs_head .gcs_head_inner table th span,
.gcs_tabbd .gcs_tabitem div.gcs_rightlink_item a>span.gcs_title,
.page-work .uk-grid .uk-width-1-5 ul li .uk-nav-sub li a,
.work-bottom .uk-card .uk-list li div,
.work-content .uk-card .uk-list li {
  font-size: 16px !important;
}

/*我的消息、我的文件*/
/* .work-bottom {
  margin: 14px 0 !important;
} */

.work-bottom .bottom-card {
  width: 100%;
  min-width: 382px;
  max-height: 281px;
  min-height: 197px;
}

.work-bottom .bottom-card:first-child {
  margin-right: 14px;
}

.work-bottom .bottom-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #dddee1;
}

.work-bottom .bottom-card .uk-card-header div {
  display: flex;
  align-items: center;
  height: 42px;
}

.work-bottom .bottom-card .uk-card-header div .uk-badge {
  font-size: 11px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
  color: #000;
  background-color: #0070ff;
  color: #fff;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-left: 10px;
}

.work-bottom .bottom-card .uk-card-header a {
  right: 22px;
  position: absolute;
  cursor: pointer
}

.work-bottom .uk-card .uk-flex div img {
  vertical-align: middle;
  height: 16px;
  width: 15px;
  margin-right: 6px;
}

.work-bottom .uk-card .uk-flex div .task-more {
  color: #ff6666;
  cursor: pointer;
}

.work-bottom .uk-card .uk-list li {
  height: 40px;
  line-height: 40px;
  margin: 0;
  padding-left: 24px;
  display: flex;
  align-items: center;
  border-left: 2px solid #fff;
  width: 100%;
}

.work-bottom .uk-card .uk-list .uk-list-disc {
  display: flex;
  flex-direction: column;
  width: 260px;
}

.work-bottom .uk-card .uk-list .uk-divider-vertical {
  width: 2px;
  background-color: #f4faff;
}

.work-bottom .uk-card .uk-list .uk-flex:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

.work-bottom .uk-card .uk-list .uk-flex ul li {
  border: none;
}

.work-bottom .uk-card .uk-list li div:nth-child(2) {
  width: 55%;
  max-width: 60%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-bottom .uk-card .uk-list li div:nth-child(3) hr {
  margin: 13px 23px 11px 22px;
  height: 36px;
  border-color: #e3e9ee;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) {
  right: 8px;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) ul li span:first-child {
  width: auto;
  text-align-last: justify;
  -moz-text-align-last: justify;
  /*兼容firefox*/
  text-align: justify;
  text-justify: distribute-all-lines;
  /*兼容ie*/
  display: block;
  /*ie下需设为block*/
  margin-right: 5px;
  color: #a9b4c7;
}

.work-bottom .uk-card .uk-list li div:nth-child(4) ul li span:last-child {
  color: #80848d;
}


.work-bottom .uk-card .uk-list li div:nth-child(4) [class*="uk-list"]> ::before {
  left: -10px;
}

.work-bottom .uk-card .uk-list li div ul {
  /* border: 1px solid blue; */
  width: 140px;
}

.work-bottom .uk-card .uk-list li div ul li {
  height: 20px;
  line-height: 20px;
  margin: 0;
  padding-left: 0px;
}

.work-bottom .uk-card .uk-list li div ul li::before {
  margin-bottom: 0;
}

.work-bottom .bottom-card:last-child .uk-card-body ul li div img:nth-child(n + 1) {
  width: 16px;
  height: 16px;
}

.work-bottom .bottom-card:last-child .uk-card-body ul li div:first-child img {
  width: 16px;
  height: 22px;
}



/* 我的关注样式 */
.work-center .uk-card .uk-card-header {
  padding: 0 26px;
  font-weight: 400;
  font-size: 14px;
  line-height: 40px;
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #dddee1;
}

.work-center .uk-card .uk-card-header a {
  right: 26px;
  top: 0;
  position: absolute;
  cursor: pointer;
  /* 添加此代码将鼠标指针改为小手 */
}

.work-center .uk-card .uk-card-header .head-title-list {
  cursor: pointer;
}

.work-center .uk-card .uk-card-header .uk-badge {
  font-size: 11px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
  color: #000;
  background-color: #0070ff;
}

.work-center .uk-card .uk-card-header>div {
  height: 40px;
  margin-right: 24px;
  /* cursor: pointer; */
  display: flex;
  align-items: center;
  border-bottom: 2px solid #fff;
  display: flex;
  align-items: center;
}

/* 样式图标 */

/* 在标签前面动态添加一个图标 */
.work-center .uk-card .uk-card-header>div .newtask {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_task_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div .completetask {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_taskok_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div .myfile {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_msg_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div .apply {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_msg_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.work-center .uk-card .uk-card-header>div .focus {
  content: "";
  width: 20px;
  height: 24px;
  background: url("@/assets/imgs/index_icon_msg_active.png") no-repeat center center;
  background-size: auto 70%;
  background-position: 0 4px;
  display: block;
}

.swiper-pagination {
  border: 2px solid blue !important;
}


/* 下边距类型 */
.work-center .uk-card .uk-card-header>.active {
  color: #0070ff;
  border-bottom: 2px solid #0070ff;
  display: flex;
  align-items: center;
}

.work-center .uk-card .uk-card-header>.active .uk-card-title {
  font-style: italic !important;
}

.work-center .uk-card .uk-card-header div>.active {
  color: #0070ff;
  font-weight: bold;
}

/* 我的关注数量图标样式 */
.work-center .uk-card .uk-card-header div>.uk-badge {
  color: #fff;
  width: 18px;
  height: 18px;
  border-radius: 50%;
}

.work-center .uk-card .uk-card-header>div>span {
  margin-right: 5px;
  /* font-family: "MicrosoftYaHei"; */
  margin-left: 2px;
  font-style: normal !important;
}

.uk-card-default .uk-card-title {
  font-size: 16px !important;
  font-weight: bold;
}

/* 我的任务列表 */
.work-center .uk-card .uk-list li {
  padding: 10px 20px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 2px solid #fff;
}

.work-center .uk-card .uk-list li .apply-flow {
  margin-left: 15px !important;
}

.work-center .uk-card .uk-list li .cate {
  width: 20% !important;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 20px;
}

.work-center .uk-card .uk-list li .cate span {
  width: 100%;
}

.work-center .uk-card .uk-list li .info {
  width: 36%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.work-center .uk-card .uk-list li .info .dept {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: auto;
  flex: 1;
  text-align: right;
}

.work-center .uk-card .uk-list li .info .name {
  width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.work-center .uk-card .uk-list li .info .time {
  width: 140px !important;
}

.work-center .uk-card .uk-list .uk-flex:hover {
  border-left: 2px solid #0070ff;
  background-color: #f4faff;
  cursor: pointer;
}

.work-center .uk-card .uk-list li div:nth-child(2) {
  width: 40%;
  max-width: 40%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: auto;
}

.work-center .uk-card .uk-list li div:nth-child(3) hr {
  margin: 13px 23px 11px 53px;
  height: 36px;
  border-color: #e3e9ee;
}

.work-center .uk-card .uk-list li div:nth-child(4) [class*="uk-list"]> ::before {
  left: -10px;
}

.work-center .uk-card .uk-list li div:nth-child(5) hr {
  margin: 13px 15px 11px 10px;
  height: 36px;
  border-color: #e3e9ee;
}

/* .work-center .uk-card .uk-list li div:last-child {
  padding-right: 12px;
  position: absolute;
  right: 0;
} */

.work-center .uk-card .uk-list li div ul li {
  height: 30px;
  line-height: 30px;
  margin: 0;
  padding-left: 0px;
}

.work-center .uk-card .uk-flex div img {
  vertical-align: middle;
  height: 14px;
  margin-right: 6px;
}

.focus-flow hr {
  margin: 13px 5px !important;
}

.work-center .uk-card .uk-list li .apply-flow {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-left: 20px;
  border-left: 1px solid #eee;
}

/* 功能收藏数据体 */
.func_fav .list {
  padding-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}

.func_fav .list .item {
  width: 100px;
  height: 100px;
  padding: 0 4px;
  border-radius: 10px;
  border: 1px solid #eee;
  float: left;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
  margin-top: 15px;
  text-decoration: none;
  background: #fff;
  transition: all 0.1s;
  box-sizing: border-box;
}

.func_fav .list .item:hover {
  background: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  margin-top: 13px;
}

.func_fav .list .item:active {
  background: #f1f1f1;
}

.func_fav .list .item .icon {
  width: auto;
  height: 24px;
}

.func_fav .list .item .text {
  text-align: center;
  margin-top: 8px;
  flex-wrap: wrap;
  max-width: 100%;
}

.uk-card-default .uk-card-title {
  font-size: 16px !important;
  font-weight: bold;
}

.uk-nav-sub>li>a {
  font-size: 14px !important;
}

.uk-card-body {
  font-size: 14px !important;
}


.work-center .uk-card .uk-list li .apply-flow .dept,
.work-center .uk-card .uk-list li .apply-flow .name,
.work-center .uk-card .uk-list li .apply-flow .time {
  width: 100% !important;
  max-width: 100% !important;
  text-align: left;
}

.emptybox {
  width: 100px;
  height: 50px;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 咨询中心列表 */

.has_icon {
  position: relative;
  padding-left: 20px;
}

.has_icon .top {
  content: "";
  width: 20px;
  height: 24px;
  position: absolute;
  background: url("@/assets/imgs/is_top.png") no-repeat center center;
  background-size: cover;
  left: 4px;
  top: 8px;
}

.has_icon .file {
  content: "";
  width: 12px;
  height: 15px;
  position: absolute;
  background: url("@/assets/imgs/index_icon_file.png") no-repeat center center;
  background-size: cover;
  left: 4px;
  top: 12px;
}

.hbm-moreico {
  text-decoration: none !important;
}

.gcs_zxzx_wlaqzthd_childs_cont {
  display: flex;
  width: 100%;
  height: 100%;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_ss_body {
  margin-left: -5%;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem {
  width: 33%;
  border-right: 1px solid #eaeaea;
  height: 90vh;
  padding: 15px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem:nth-child(3n) {
  border-right: 0;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_ss_head {
  height: 48px;
  line-height: 48px;
  font-size: 16px;
  color: #0070ff;
  font-weight: bold;
  background-image: linear-gradient(to right, #f6faff, #cce1ff);
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_ss_head span {
  font-size: inherit;
  color: inherit;
  font-weight: inherit;
  display: inline-block;
  vertical-align: middle;
  margin-left: 27px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_ss_head span.gcs_zxzx_ss_head_ico {
  margin-left: 25px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_ss_head span:gcs_zxzx_ss_head_lastimg {
  margin-left: 40px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_ss_head .gcs_zxzx_ss_head_lastimg {
  width: 145px;
  height: 10px;
  background: url('@/assets/imgs/yuanoaicos.png') no-repeat 0 -160px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_wlaqzthd_zstz .gcs_zxzx_ss_head_ico {
  width: 24px;
  height: 25px;
  background: url('@/assets/imgs/yuanoaicos.png') no-repeat 0 -132px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_wlaqzthd_wkt .gcs_zxzx_ss_head_ico {
  width: 31px;
  height: 24px;
  background: url('@/assets/imgs/yuanoaicos.png') no-repeat -28px -132px;
}

.gcs_zxzx_wlaqzthd_childs_cont .gcs_zxzx_wlaqzthd_listitem .gcs_zxzx_wlaqzthd_wlaqsj .gcs_zxzx_ss_head_ico {
  width: 24px;
  height: 25px;
  background: url('@/assets/imgs/yuanoaicos.png') no-repeat -60px -132px;
}

.article_list .item {
  height: 40px;
  line-height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px 0 22px;
}

.article_list .item:hover {
  background-color: #f4faff;
}

.article_list li.item a.link {
  font-size: 16px;
  width: 300px;
  text-decoration: none;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #555;
}

.wlaqsj .article_list li.item a.link {
  width: 260px;
}

.article_list li.item a.link:hover {
  color: #555;
}

.article_list li.item .datetime {
  font-size: 16px;
  color: #80848d;
}

.hbm-zxzx-sec.hbm-zxzx-sec4 {
  height: 100%;
}

/* // ================================ */

.gcs_tab_zxzx_single {
  width: 100%;
  height: 100%;
}

.gcs_tab_zxzx_single>.gcs_tabhd {
  background-color: #fff;
  height: 40px;
  border-bottom: 1px solid #eaeaea;
  width: 100%;
  position: relative;
}

.gcs_tab_zxzx_single>.gcs_tabhd ul {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 40px;
}

.gcs_tab_zxzx_single>.gcs_tabhd ul li {
  text-align: center;
  height: 38px;
  line-height: 38px;
  border: 0px solid transparent;
  cursor: pointer;
  float: left;
  margin-left: 10px;
  /* margin-top: 2px; */
  font-size: 13px;
  margin-left: 23px;
  font-size: 16px;
  /* font-family: Microsoft YaHei; */
  font-weight: bold;
  color: #404a56;
  position: relative;
}

.gcs_tab_zxzx_single>.gcs_tabhd ul li.gcs_cur .gcs_cur_inner {
  position: absolute;
  width: 68px;
  height: 4px;
  background: #0d5ffe;
  box-shadow: 0px 2px 3px 0px rgba(0, 112, 255, 0.2);
  border-radius: 0px 0px 4px 3px;
  top: 36px;
  left: 0px;
}

.gcs_tab_zxzx_single>.gcs_tabcont {
  padding: 10px 10px 10px 10px;
}

.gcs_tab_zxzx_single .hbm-moreico {
  top: 14px;
  right: 19px;
  position: absolute;
  font-size: 13px;
  color: #afafaf;
  cursor: pointer;
}

.gcs_tab_zxzx_single .hbm-moreico:hover {
  color: #363636;
}


/* 科技中心样式 */

.gcs_tab_kjcxyd_childs .gcs_newslist span.gcs_title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.gcs_tab_kjcxyd_childs {
  width: 100%;
  height: 100%;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd {
  background-color: #fff;
  height: 93vh;
  border-right: 1px solid #eaeaea;
  width: 240px;
  position: relative;
  float: left;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd ul {
  margin: 0;
  padding: 0;
  height: 40px;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li {
  text-align: left;
  height: 50px;
  line-height: 38px;
  cursor: pointer;
  margin-top: 2px;
  font-size: 16px;
  color: #404a56;
  position: relative;
  border: 4px solid transparent;

  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li span {
  display: inline-block;
  vertical-align: middle;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li span:first-child {
  margin-right: 14px;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li.gcs_cur {
  border-right: 4px solid #0070ff;
  font-size: 16px;
  color: #0070ff;
  font-weight: bold;
}

.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li.gcs_cur .icon {
  background-color: #0070ff;
}


.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li .gcs_cur span,
.gcs_tab_kjcxyd_childs>.gcs_tabhd ul li span {
  font-size: inherit;
  color: inherit;
  font-weight: inherit;
}

.gcs_tab_kjcxyd_childs>.gcs_tabcont {
  padding: 10px 10px 10px 10px;
}

.gcs_tab_kjcxyd_childs .hbm-moreico {
  top: 14px;
  right: 19px;
  position: absolute;
  font-size: 13px;
  color: #afafaf;
  cursor: pointer;
}

.gcs_tab_kjcxyd_childs .hbm-moreico:hover {
  color: #363636;
}

.gcs_tab_kjcxyd_childs .gcs_newslist span.gcs_title {
  width: 780px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.hbm-zxzx-sec.hbm-zxzx-sec5 {
  height: auto;
}

.gcs_tab_zxzx_single {
  width: 100%;
  height: 100%;
}

.gcs_tab_zxzx_single>.gcs_tabhd {
  background-color: #fff;
  height: 40px;
  border-bottom: 1px solid #eaeaea;
  width: 100%;
  position: relative;
}

.gcs_tab_zxzx_single>.gcs_tabhd ul {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 40px;
}

.gcs_tab_zxzx_single>.gcs_tabhd ul li {
  text-align: center;
  height: 38px;
  line-height: 38px;
  border: 0px solid transparent;
  cursor: pointer;
  float: left;
  margin-left: 10px;
  font-size: 13px;
  margin-left: 23px;
  font-size: 16px;
  font-weight: bold;
  color: #404a56;
  position: relative;
}

.gcs_tab_zxzx_single>.gcs_tabhd ul li .gcs_cur .gcs_cur_inner {
  position: absolute;
  width: 68px;
  height: 4px;
  background: #0d5ffe;
  box-shadow: 0px 2px 3px 0px rgba(0, 112, 255, 0.2);
  border-radius: 0px 0px 4px 3px;
  top: 36px;
  left: 0px;
}

.gcs_tab_zxzx_single>.gcs_tabcont {
  padding: 10px 10px 10px 10px;
}

.gcs_newslist_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px !important;
  box-sizing: border-box;
  padding-right: 20px;
  height: 40px;
  line-height: 40px;
}

.toper .gcs_newslist_item {
  position: relative;
}

.toper .gcs_newslist_item:hover::before {
  content: '';
  display: block;
  width: 4px;
  height: 40px;
  background: #0d5ffe;
  border-radius: 0px 8px 8px 0;
  position: absolute;
  left: 0;
  top: 0;
}

.gcs_newslist_item:hover {
  background-color: #f4faff;
}

.gcs_newslist_item.gcs_cur {
  background-color: #f4faff;
}

.gcs_newslist_item .gcs_cur::before {
  content: '';
  display: block;
  width: 4px;
  height: 40px;
  background: #0d5ffe;
  border-radius: 0px 8px 8px 0;
  position: absolute;
  left: 0;
  top: 0;
}

.gcs_newslist_item span {
  display: inline-block;
}

.gcs_newslist_item span.gcs_dt {
  float: right;
  color: #80848d;
}

.gcs_newslist_item a {
  text-decoration: none;
}

.gcs_newslist_item span.gcs_title {
  color: #363636;
  font-size: 14px;
}

.datetime {
  min-width: 90px;
  text-align: right;
}


/* 服务中心配置 */
.custom-card {
  margin-top: 10px;
}

.custom-card .custom-one-level-img {
  width: 20px;
  height: 20px;
  margin-top: -8px;
}

.custom-card .custom-one-level-title {
  font-size: 16px;
  color: #333;
}

.custom-grid .custom-grid-child > .custom-grid-content {
  margin: 10px 0 0 0;
  border: 1px solid #e5e5e5;
  min-height: 350px;
}

.custom-two-level {
  padding: 20px 20px 0 20px;
}

.custom-two-level .custom-two-level-img {
  width: 100%;
  min-height: 60px;
  max-height: 80px;
  margin-bottom: 15px;
  border-radius: 2px;
}

.custom-two-level hr {
  margin: 10px 0 !important;
}

.custom-grid-content .custom-small-img {
  width: 20px;
  height: 20px;
  margin-top: -5px;
}

.custom-grid-content ul {
  padding: 0 20px;
}

.view-more {
  width: 100%;
  height: 45px;
  border-top: 1px solid #e5e5e5;
  text-align: center;
  color: #999;
  font-size: 16px;
  padding: 10px 0 0 0;
}

.view-more .view-all {
  cursor: context-menu;
}

.custom-right-menu-content {
  position: relative;
  top: 10px;
  width: 96%;
  left: 2%;
  right: 2%;
}

.custom-left-menu-entry-content-itme {
  padding: 20px;
}

.custom-table-menu-list tbody tr td:before {
  content: '';
  position: absolute;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
}

.custom-table-menu-list tbody tr td a {
  padding-left: 20px;
  color: #333;
}

.custom-table-menu-list tbody tr td a:hover {
  padding-left: 20px;
  color: #333;
  text-decoration: none;
}

.uk-grid > * {
  padding-left: 10px;
  padding-right: 20px;
}

.uk-grid {
  padding-left: 30px;
  margin-right: -20px;
}

.custom-two-level-title {
  font-size: 16px;
  color: #333;
}

.right-entry-list li {
  width: 214px;
  height: 30px;
  vertical-align: middle;
  line-height: 30px;
  clear: both;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

.right-entry-list li .jump-link {
  display: block;
  padding-left: 15px;
  color: #666;
  font-size: 14px;
  width: 100%;
  text-decoration: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding-left: 10px;
}

.right-entry-list li .jump-link:hover {
  color: #666;
}

.right-entry-list li .jump-link:before {
  content: '';
  position: absolute;
  left: -10px;
  top: 5px;
  width: 1.5em;
  height: 1.5em;
  background-image: url(data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%20%20%20%20%3Ccircle%20fill%3D%22%23666%22%20cx%3D%223%22%20cy%3D%223%22%20r%3D%223%22%20%2F%3E%0A%3C%2Fsvg%3E);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  text-decoration: none;
}

.right-entry-list li .jump-link-modal {
  display: inline;
  color: #32b66b;
  font-size: 12px;
  border: 1px solid #32b66b;
  padding: 0 4px;
  border-radius: 3px;
  width: 58px;
}

.right-entry-list li .jump-link-modal:hover {
  display: inline;
  color: #32b66b;
  text-decoration: none;
  font-size: 12px;
  border: 1px solid #32b66b;
  padding: 0 4px;
  border-radius: 3px;
  width: 58px;
}

.custom-grid .custom-grid-item {
  /* min-width: 180px; */
  width: 285px;
}

/* 服务中心，头部菜单 */

.service-menu-item {
  margin-top: 20px;
}
.custom-breadcrumb li {
  display: inline-block;
  margin: 5px;
}

.custom-breadcrumb li a {
  color: #333;
  font-size: 14px;
}

.custom-breadcrumb li a:hover {
  text-decoration: none;
  color: #333;
  font-size: 14px;
}

.custom-breadcrumb li span {
  display: inline-block;
  text-align: right;
}
