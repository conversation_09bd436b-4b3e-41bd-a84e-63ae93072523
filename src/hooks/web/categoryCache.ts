/**
 * 配置浏览器本地存储的方式，可直接存储对象数组。
 */

import WebStorageCache from 'web-storage-cache'

type CacheType = 'localStorage' | 'sessionStorage'

export const CATEGORY = {
  IsLayout: 'IsLayout'
}

export const useLayoutCache = (type: CacheType = 'localStorage') => {
  const wsCache: WebStorageCache = new WebStorageCache({
    storage: type
  })

  return {
    wsCache
  }
}

export const deleteCategoryCache = () => {
  const { wsCache } = useLayoutCache()
  wsCache.delete(CATEGORY.IsLayout)
}

