import { useEnum } from '@/store/modules/enum'
import { getEnumItemList } from '@/api/system/enum'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache('sessionStorage')
const enums = useEnum()

export async function initEnum() {
  await enums.initialize()
}
export interface EnumItem {
  id: string
  enumDefId: string
  code: string
  name: string
}

export async function getEnumItem(enumType: EnumType) {
  let enumMap;
  const enumsCache = wsCache.get(CACHE_KEY.enums)
  if (enumsCache && Array.isArray(enumsCache)) {
    enumMap = enums.convertArrayToMap(enumsCache)
  } else {
    await initEnum()
    enumMap = enums.getEnumMap
  }
  const enumDef = enumMap.get(enumType)

  const res = await getEnumItemList(enumDef.id)
  return res.data || res
}

export enum EnumType {
  USER_TYPE = 'MH.USER_TYPE',
  COMMON_STATUS = 'MH.COMMON_STATUS',
  SYSTEM_OAUTH2_GRANT_TYPE = 'MH.SYSTEM_OAUTH2_GRANT_TYPE'
}
