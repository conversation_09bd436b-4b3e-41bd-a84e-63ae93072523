/**
 * 遗留的AJAX请求工具函数
 * 封装了master.js中的_cmaj函数，提供TypeScript支持
 */

import $ from 'jquery'
import '@/assets/js/master.js'

// 定义_cmaj函数的类型
interface CmajFunction {
  (
    adom: HTMLElement | null,
    type: string,
    url: string,
    data: any,
    headdata?: any,
    contenttype?: string,
    successfn?: (data: any) => void,
    errorfn?: (error: any) => void
  ): void
}

// 获取全局的_cmaj函数
const getCmaj = (): CmajFunction => {
  // 等待master.js加载完成
  if (typeof window !== 'undefined') {
    // 如果_cmaj已经存在，直接返回
    if ((window as any)._cmaj) {
      return (window as any)._cmaj
    }

    // 如果不存在，尝试从master.js中获取
    try {
      // 确保master.js已经执行
      const script = document.querySelector('script[src*="master.js"]')
      if (script || (window as any)._cmaj) {
        return (window as any)._cmaj
      }
    } catch (e) {
      console.warn('Failed to load _cmaj from master.js:', e)
    }
  }

  throw new Error('_cmaj function not found. Make sure master.js is loaded.')
}

/**
 * 使用_cmaj发送AJAX请求的封装函数
 * @param options 请求选项
 */
export const useLegacyAjax = (options: {
  dom?: HTMLElement | null
  type: 'GET' | 'POST'
  url: string
  data?: any
  headers?: any
  contentType?: string
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}) => {
  const _cmaj = getCmaj()
  
  _cmaj(
    options.dom || null,
    options.type,
    options.url,
    options.data || '',
    options.headers || null,
    options.contentType || 'application/x-www-form-urlencoded',
    options.onSuccess,
    options.onError
  )
}

/**
 * 直接导出_cmaj函数供使用（延迟获取）
 */
export const cmaj = (...args: Parameters<CmajFunction>) => {
  const _cmaj = getCmaj()
  return _cmaj(...args)
}

export default {
  useLegacyAjax,
  cmaj
}
