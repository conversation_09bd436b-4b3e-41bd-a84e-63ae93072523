// 综合走马灯组件
import ImageCard from '@/layout/portal/workCenter/components/ImageCard.vue'
import BannerImg from '@/layout/portal/consultCenter/components/BannerImg.vue'
import ImageCardForm from '@/layout/portal/workCenter/from/ImageCardForm.vue'
import BannerImgFrom from '@/layout/portal/consultCenter/from/BannerImgFrom.vue'

//办公中心组件
import LeftNav from '@/layout/portal/consultCenter/components/LeftNav.vue'
import CollectSc from '@/layout/portal/workCenter/components/CollectSc.vue'
import ItemList from '@/layout/portal/workCenter/components/ItemList.vue'
import WorkList from '@/layout/portal/workCenter/components/WorkList.vue'
import MsgAndMyFile from '@/layout/portal/workCenter/components/MsgAndMyFile.vue'
//办公中心配置项
import LeftNavForm from '@/layout/portal/workCenter/from/LeftNavForm.vue'
import MsgForm from '@/layout/portal/workCenter/from/MsgForm.vue'
import CollectForm from '@/layout/portal/workCenter/from/CollectForm.vue'
import ItemListForm from '@/layout/portal/workCenter/from/ItemListForm.vue'
import WorkListForm from '@/layout/portal/workCenter/from/WorkListForm.vue'

//咨询中心
import ConsultList from '@/layout/portal/consultCenter/components/ConsultList.vue'
import FinanceIcm from '@/layout/portal/consultCenter/components/FinanceIcm.vue'
import LinkItem from '@/layout/portal/consultCenter/components/LinkItem.vue'
import Science from '@/layout/portal/consultCenter/components/Science.vue'
import JtSzList from '@/layout/portal/consultCenter/components/JtSzList.vue'
//咨询中心配置项
import ScienceForm from '@/layout/portal/consultCenter/from/ScienceForm.vue'
import ConsultForm from '@/layout/portal/consultCenter/from/ConsultForm.vue'
import FinanceForm from '@/layout/portal/consultCenter/from/FinanceForm.vue'
import NewsForm from '@/layout/portal/consultCenter/from/NewsForm.vue'
import LinkForm from '@/layout/portal/consultCenter/from/LinkForm.vue'


//服务中心
import ServiceItem from '@/layout/portal/serviceCenter/components/ServiceItem.vue'
import ServiceLeftNav from '@/layout/portal/serviceCenter/components/ServiceLeftNav.vue'
import ServiceMenuItem from '@/layout/portal/serviceCenter/components/ServiceMenuItem.vue'

//服务中心配置
import ServiceItemFrom from '@/layout/portal/serviceCenter/from/ServiceItemFrom.vue'
import ServiceLeftNavFrom from '@/layout/portal/serviceCenter/from/ServiceLeftNavFrom.vue'
import ServiceMenuItemFrom from '@/layout/portal/serviceCenter/from/ServiceMenuItemFrom.vue'



//组件列表
export const customList = shallowRef([
  {
    name: markRaw(LeftNav), //组件
    formName: markRaw(LeftNavForm), //对应组件配置项
    id: 'leftNav', //唯一标识
    title: '左侧导航' //组件名称
  },
  {
    name: markRaw(CollectSc),
    formName: markRaw(CollectForm),
    id: 'collect',
    title: '功能收藏'
  },
  {
    name: markRaw(ImageCard),
    formName: markRaw(ImageCardForm),
    id: 'imageCard',
    title: '走马灯'
  },
  {
    name: markRaw(ItemList),
    formName: markRaw(ItemListForm),
    id: 'itemList',
    title: '院通知'
  },
  {
    name: markRaw(WorkList),
    formName: markRaw(WorkListForm),
    id: 'workList',
    title: '我的待办'
  },
  {
    name: markRaw(MsgAndMyFile),
    formName: markRaw(MsgForm),
    id: 'msg',
    title: '我的消息'
  },
  // 咨询中心
  {
    name: markRaw(ImageCard),
    formName: markRaw(ImageCardForm),
    id: 'consultImage',
    title: '咨询中心走马灯'
  },
  {
    name: markRaw(BannerImg),
    formName: markRaw(BannerImgFrom),
    id: 'bannerImg',
    title: '抬头轮播图'
  },
  {
    name: markRaw(ConsultList),
    formName: markRaw(ConsultForm),
    id: 'consult',
    title: '院发文'
  },
  {
    name: markRaw(FinanceIcm),
    id: 'financeIcm',
    formName: markRaw(FinanceForm),
    title: '财政资金专栏'
  },
  {
    name: markRaw(JtSzList),
    formName: markRaw(NewsForm),
    id: 'jtSzList',
    title: '时政新闻'
  },
  {
    name: markRaw(LinkItem),
    formName: markRaw(LinkForm),
    id: 'linkItem',
    title: '专题专栏'
  },
  {
    name: markRaw(Science),
    formName: markRaw(ScienceForm),
    id: 'science',
    title: '科技创新园地'
  },
  // 访问中心
  {
    name: markRaw(ServiceItem),
    formName: markRaw(ServiceItemFrom),
    id: 'serviceItem',
    title: '服务中心列表'
  },
  {
    name: markRaw(ServiceMenuItem),
    formName: markRaw(ServiceMenuItemFrom),
    id: 'serviceMenuItem',
    title: '服务中心头部菜单'
  },
  {
    name: markRaw(ServiceLeftNav),
    formName: markRaw(ServiceLeftNavFrom),
    id: 'serviceLeftNav',
    title: '服务中心左侧导航'
  }
])
export const workList = [
  { title: '左侧导航', icon: 'message', itemId: 'leftNav', category: 'workCenter' },
  { title: '走马灯', icon: 'money', itemId: 'imageCard', category: 'workCenter' },
  { title: '院通知', icon: 'shopping', itemId: 'itemList', category: 'workCenter' },
  { title: '我的待办', icon: 'shopping', itemId: 'workList', category: 'workCenter' },
  { title: '我的消息', icon: 'shopping', itemId: 'msg', category: 'workCenter' },
  { title: '功能收藏', icon: 'shopping', itemId: 'collect', category: 'workCenter' }
]

// export const myWorkList = [
//   { title: '左侧导航', icon: 'message', itemId: 'leftNav', category: 'myWork' },
//   { title: '我的工作', icon: 'money', itemId: 'myWork', category: 'myWork' },
// ]

export const consultList = [
  { title: '走马灯', icon: 'money', itemId: 'consultImage', category: 'consultCenter' },
  { title: '抬头轮播图', icon: 'money', itemId: 'bannerImg', category: 'consultCenter' },
  { title: '院发文', icon: 'message', itemId: 'consult', category: 'consultCenter' },
  { title: '财政资金专栏', icon: 'money', itemId: 'financeIcm', category: 'consultCenter' },
  { title: '时政新闻', icon: 'shopping', itemId: 'jtSzList', category: 'consultCenter' },
  { title: '专题专栏', icon: 'shopping', itemId: 'linkItem', category: 'consultCenter' },
  { title: '科技创新园地', icon: 'shopping', itemId: 'science', category: 'consultCenter' }
]

export const serviceList = [
  { title: '子系统入口', icon: 'shopping', itemId: 'serviceLeftNav', category: 'serviceCenter' },
  { title: '头部菜单', icon: 'shopping', itemId: 'serviceMenuItem', category: 'serviceCenter' },
  { title: '中心列表', icon: 'shopping', itemId: 'serviceItem', category: 'serviceCenter' }
]


