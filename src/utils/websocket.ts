import { useWebSocket } from "@vueuse/core";
import { getAccessToken } from "./auth";
const message = useMessage();


// WebSocket 服务地址
const server = ref(
  (import.meta.env.VITE_BASE_URL + '/infra/websocket').replace('http', 'ws') + '?token=' + getAccessToken()
)

/** 发起 WebSocket 连接 */
const { status, data, send, close, open } = useWebSocket(server.value, {
  autoReconnect: false,
  heartbeat: true
})

// WebSocket 连接是否打开
export const getIsOpen = computed(() => status.value === 'OPEN')

export const messageInfo = ref()

/** 监听接收到的数据 */
watchEffect(() => {
  if (!data.value) {
    return
  }
  try {
    // 1. 收到心跳
    if (data.value === 'pong') {
      return
    }
    // 2.1 解析 type 消息类型
    const content = JSON.parse(data.value)

    // 2.2 消息类型：demo-message-receive
    const single = true;
    if (single) {
      messageInfo.value = { type: content.type, userId: content.userId, timestamp: content.timestamp }
    } else {
      messageInfo.value = { type: content.type, userId: content.userId, timestamp: content.timestamp }
    }
  } catch (error) {
    message.error('处理消息发生异常：' + data.value)
    console.error(error)
  }
})


/** 发送消息 */
const sendText = ref('你好') // 发送内容
const sendUserId = ref('') // 发送人
export const handlerSend = () => {
  // 1.1 先 JSON 化 message 消息内容
  const messageContent = JSON.stringify({
    text: sendText.value,
    toUserId: sendUserId.value
  })
  // 1.2 再 JSON 化整个消息
  const jsonMessage = JSON.stringify({
    type: 'check-message-send',
    content: messageContent
  })
  // 2. 最后发送消息
  send(jsonMessage)
  sendText.value = ''
}


/** 切换 websocket 连接状态 */
export const toggleConnectStatus = () => {
  if (getIsOpen.value) {
    console.log("关闭websocket监听")
    close()
  } else {
    console.log("开启websocket监听")
    open()
  }
}
