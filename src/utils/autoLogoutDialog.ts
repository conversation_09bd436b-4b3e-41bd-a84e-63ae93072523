import { ElMessageBox } from 'element-plus'

class AutoLogoutDialogManager {
  private isShowing = false

  /**
   * 显示自动退出登录弹窗
   */
  show() {
    if (this.isShowing) {
      return
    }

    this.isShowing = true

    // 参考 service.ts 中的实现，阻止页面渲染，只显示弹窗
    document.body.innerHTML = '' // 清空页面内容
    document.body.style.overflow = 'hidden' // 禁止滚动

    // 使用 ElMessageBox 显示弹窗，与 service.ts 中的样式保持一致
    ElMessageBox.confirm(
      '由于您长时间没有操作，已自动退出登录',
      '自动退出登录',
      {
        showCancelButton: false,
        closeOnClickModal: false,
        showClose: false,
        confirmButtonText: '确定',
        type: 'warning'
      }
    ).then(() => {
      this.isShowing = false
      // 跳转到登录页
      window.location.href = '/portal/home/<USER>'
    }).catch(() => {
      // 即使用户按ESC或其他方式关闭，也要跳转到登录页
      this.isShowing = false
      window.location.href = '/portal/home/<USER>'
    })
  }

  /**
   * 隐藏弹窗
   */
  hide() {
    this.isShowing = false
    // 恢复页面滚动
    document.body.style.overflow = ''
  }

  /**
   * 销毁弹窗
   */
  destroy() {
    this.isShowing = false
    // 恢复页面滚动
    document.body.style.overflow = ''
  }
}

// 创建全局实例
export const autoLogoutDialogManager = new AutoLogoutDialogManager()
